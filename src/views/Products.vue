<template>
  <div class="products-page">
    <div class="top-background"></div>
    <!-- 顶部横幅 -->
    <div class="hero-section">
      <div class="container">
        <div class="text-center">
          <h1 class="hero-title">Our Products</h1>
          <p class="hero-subtitle">Cutting-edge AI solutions designed with simplicity and power in mind</p>
        </div>
      </div>
    </div>

    <!-- 产品展示区 -->
    <div class="products-showcase">
      <div class="container">
        <!-- 主要产品 -->
        <div class="featured-products">
          <div
            class="featured-card"
            :class="{ 'reverse': index % 2 === 1 }"
            v-for="(product, index) in featuredProducts"
            :key="index">
            <div class="product-content">
              <div class="product-title-container">
                <img v-if="product.logo" :src="product.logo" :alt="product.title + ' logo'" class="product-logo">
                <h2 class="product-title">{{ product.title }}</h2>
              </div>
              <p class="product-description">{{ product.description }}</p>
              <div>
                <div class="feature" v-for="(feature, i) in product.features" :key="i">
                  <!-- <div class="feature-icon">{{ feature.icon }}</div> -->
                  <div class="feature-text">
                    <h3>{{ feature.title }}</h3>
                    <p>{{ feature.description }}</p>
                  </div>
                </div>
              </div>
              <!-- <router-link :to="product.url" class="product-cta">Try Now</router-link> -->
            </div>
            <div class="product-image">
              <img :src="product.image" :alt="product.title">
            </div>
          </div>
        </div>
      </div>
      </div>
      

    <div class="large-banner"></div>

        <!-- 其他产品 -->
    <section class="other-products">
      <div class="container">
        <h2 class="section-title text-gradient">More Products</h2>
        <div class="other-products-grid">
          <div class="product-card small" v-for="(product, index) in otherProducts" :key="index">
            <div class="product-content">
              <h3 class="product-title">{{ product.title }}</h3>
              <p class="product-description">{{ product.description }}</p>
              <div class="card-footer">
                <router-link :to="product.url" class="btn-text-arrow">
                  learn more<i class="bi bi-arrow-up-right"></i>
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="spacer"></div>
        <!-- 即将推出 -->
    <div class="section-coming-soon">
      <div class="container">
        <h2 class="section-title text-gradient">Coming Soon</h2>
        <div class="products-grid">
          <div class="product-card coming-soon" v-for="(product, index) in comingSoonProducts" :key="index">
            <div class="product-content">
              <h3 class="product-title">{{ product.title }}</h3>
              <p class="product-description">{{ product.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="spacer"></div>
    <!-- 客户案例 -->
    <section class="case-studies">
      <div class="container">
        <h2 class="section-title text-gradient">Success Stories</h2>
        <div class="case-studies-grid">
          <div class="case-study" v-for="(caseStudy, index) in caseStudies" :key="index">
            <div class="case-study-content">
              <h3 class="case-study-title">{{ caseStudy.title }}</h3>
              <p class="case-study-description">{{ caseStudy.description }}</p>
              <div class="case-study-quote">
                <blockquote>"{{ caseStudy.quote }}"</blockquote>
                <cite>— {{ caseStudy.author }}, {{ caseStudy.role }}</cite>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行动召唤 -->
    <section class="cta-section">
      <div class="container">
        <h2 class="cta-title">Ready to transform your workflow?</h2>
        <p class="cta-subtitle">Join thousands of satisfied users who have elevated their productivity with our AI solutions</p>
        
        <router-link to="/contact">
          <button class="btn btn-cta btn-s">Contact Us</button>
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useHead } from '@vueuse/head'
import { useProductsStore } from '@/store/products'
import { storeToRefs } from 'pinia'
import { useMotion } from '@vueuse/motion'
import { onMounted, ref } from 'vue'

useMotion()

// 设置页面元数据
useHead({
  title: 'Little Grass - AI Products',
  meta: [
    {
      name: 'description',
      content: 'Explore Little Grass\'s suite of AI products designed to enhance productivity, creativity, and communication.'
    }
  ]
})

// 从store中获取产品数据
const productsStore = useProductsStore()
const { featuredProducts, otherProducts, comingSoonProducts, caseStudies } = storeToRefs(productsStore)

// 滚动触发动画
onMounted(() => {
  // 创建Intersection Observer实例
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      // 当元素进入视口时
      if (entry.isIntersecting) {
        // 添加动画类
        entry.target.classList.add('animate');
        // 停止观察该元素
        observer.unobserve(entry.target);
      }
    });
  }, {
    // 设置元素进入视口20%时触发
    threshold: 0.2
  });

  // 观察所有需要动画的元素
  const animatedElements = document.querySelectorAll('.section-title, .featured-card, .product-card, .feature, .case-study, .cta-title, .cta-subtitle, .cta-buttons');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
});
</script>

<style scoped>
/* 顶部横幅 */
.top-background{
  background-image: url('/images/bg_products.svg');
  background-color: #000;
}
.hero-content {
  position: relative;
  z-index: 2;
  padding: 120px 0 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 产品展示区 */
.products-showcase {
  padding: 40px 0 80px;
  margin-top: 200px;
}
.spacer {
  /* height: 20vh; */
  min-height: 200px;
}
/* 主要产品卡片 */
.featured-products {
  margin-bottom: 80px;
}

.featured-card {
  /* background-color: #1b1c1e; */
  display: flex;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 50px;
  padding: 18px 30px;
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); */
}

.featured-card.reverse {
  flex-direction: row-reverse;
}

.product-content {
  flex: 1;
  padding: 40px;
}

.product-image {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  margin: 20px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-title-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  margin-top: 10px;
}

.product-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
  flex-shrink: 0;
}

.product-title {
  font-size: 30px;
  font-weight: 900;
  /* font-style: italic; */
  margin: 0;
  letter-spacing: 0;
}

.product-description {
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
}

/* 产品特性 */
.feature {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.feature-text h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #fff;
}

.feature-text p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.product-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(51, 102, 255, 0.5);
}

.product-cta.small {
  padding: 8px 16px;
  font-size: 0.9rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: none;
}
.product-cta.text {
  padding: 8px 0px;
  font-size: 0.7rem;
  background: transparent;
  font-weight: 100;
}

.product-cta.small:hover {
  background: rgba(51, 102, 255, 0.1);
  border-color: #3366FF;
}

.large-banner {
  background-image: url('/images/products/large_banner.png');
  background-size: cover;
  background-position: center;
  height: 1075px;
  /* margin-bottom: 80px; */
}
/* 其他产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(280px, 500px));
  grid-template-rows: repeat(auto-fill, 290px);
  gap: 40px;
  margin-bottom: 80px;
  /* 添加以下属性使grid内容居中 */
  justify-content: center;
}
.product-card {
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.product-card.small {
  background: linear-gradient(180deg, #282837 0%, #352D2D 100%);
  height: 300px;
}

.product-card.small .product-content {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.product-card.small .product-description {
  font-size: 18px;
  margin-bottom: 20px;
  flex-grow: 1;
}

.card-footer {
  margin-top: auto;
  display: flex;
  justify-content: flex-start;
  /* padding: 1.25rem; */
}
.other-products-grid {
  display: grid;
  margin: 0 auto;
  grid-template-columns: repeat(2, minmax(300px, 500px));
  gap: 40px;
  justify-content: center;
}
/* 即将推出的产品 */
.section-coming-soon {
  padding-top: 80px;
}
.product-card.coming-soon {
  border: 2px solid #FFFFFF66;
}
.product-card.coming-soon .product-content {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.product-card.coming-soon:hover {
  border-color: rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}
.product-card.coming-soon .product-description {
  color: rgba(255, 255, 255, 0.6);
  font-size: 22px;
  line-height: 40px;
}

/* 客户案例 */
.case-studies {
  /* background-color: #0A0A0A; */
  padding: 80px 0;
}

.case-studies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.case-study {
  background-color: #111111;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-study:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.case-study-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #fff;
}

.case-study-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
}

.case-study-quote {
  border-left: 3px solid #4facfe;
  padding-left: 20px;
  margin-top: 20px;
}

.case-study-quote blockquote {
  font-size: 1.1rem;
  font-style: italic;
  color: #fff;
  margin-bottom: 10px;
}

.case-study-quote cite {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: normal;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .product-card.featured {
    flex-direction: column !important;
  }
  
  .product-card.featured .product-image {
    height: 300px;
    margin: 20px 20px 0;
  }
  
  .case-studies-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
}

@media (max-width: 576px) {
  .hero-section {
    padding: 100px 0 60px;
  }
  
  .product-card.featured .product-content,
  .product-card.small .product-content {
    padding: 20px;
  }
  
  .product-title-container {
    gap: 12px;
  }
  
  .product-logo {
    width: 40px;
    height: 40px;
  }
  
  .product-title {
    font-size: 1.8rem;
  }
  
  .product-description {
    font-size: 1rem;
  }
  
  .feature-icon {
    font-size: 1.5rem;
  }
  
  .feature-text h3 {
    font-size: 1.1rem;
  }
}

/* 文字渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片弹出动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 为标题添加动画 */
.hero-title {
  animation: fadeInUp 0.8s ease-out forwards;
}

.hero-subtitle {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

/* 为章节标题添加动画 - 滚动触发 */
.section-title {
  opacity: 0;
}

.section-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 为产品卡片添加动画 - 滚动触发 */
.featured-card {
  opacity: 0;
}

.featured-card.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

/* 为网格中的卡片添加动画，设置递增延迟 - 滚动触发 */
.products-grid .product-card {
  opacity: 0;
}

.products-grid .product-card.animate:nth-child(1) {
  animation: scaleIn 0.6s ease-out 0.1s forwards;
}

.products-grid .product-card.animate:nth-child(2) {
  animation: scaleIn 0.6s ease-out 0.2s forwards;
}

.products-grid .product-card.animate:nth-child(3) {
  animation: scaleIn 0.6s ease-out 0.3s forwards;
}

.products-grid .product-card.animate:nth-child(4) {
  animation: scaleIn 0.6s ease-out 0.4s forwards;
}

/* 为特性列表添加动画 - 滚动触发 */
.feature {
  opacity: 0;
}

.feature.animate {
  animation: fadeInUp 0.6s ease-out forwards;
}

.feature.animate:nth-child(1) {
  animation-delay: 0.2s;
}

.feature.animate:nth-child(2) {
  animation-delay: 0.3s;
}

.feature.animate:nth-child(3) {
  animation-delay: 0.4s;
}

/* 为客户案例添加动画 - 滚动触发 */
.case-study {
  opacity: 0;
}

.case-study.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.case-studies-grid .case-study.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.case-studies-grid .case-study.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.case-studies-grid .case-study.animate:nth-child(3) {
  animation-delay: 0.3s;
}

/* 为CTA按钮添加动画 - 滚动触发 */
.cta-title {
  opacity: 0;
}

.cta-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

.cta-subtitle {
  opacity: 0;
}

.cta-subtitle.animate {
  animation: fadeInUp 0.8s ease-out 0.1s forwards;
}

.cta-buttons {
  opacity: 0;
}

.cta-buttons.animate {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
}
</style>