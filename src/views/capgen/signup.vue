<template>
  <div class="capgen-product-page">
    <!-- 注册区域 -->
    <section class="signin-section">
      <div class="container">
        <div class="signin-content">
          <div class="signin-card">
            <div class="signin-header">
              <div class="logo-section">
                <img src="/images/logo_capgen.png" alt="CapGen Logo" class="signin-logo" />
                <h1 class="signin-title">Sign up for CapGen</h1>
              </div>
              <p class="signin-description">To start using professional AI subtitle services</p>
            </div>

            <div class="signin-form-section">
              <!-- 注册表单 -->
              <form @submit.prevent="handleSignup" class="signin-form">
                <div class="form-group">
                  <label for="name" class="form-label">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    v-model="formData.name"
                    class="form-input"
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="email" class="form-label">Email</label>
                  <input
                    type="email"
                    id="email"
                    v-model="formData.email"
                    class="form-input"
                    placeholder="Enter your email address"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="password" class="form-label">Password</label>
                  <div class="password-input-wrapper">
                    <input
                      :type="showPassword ? 'text' : 'password'"
                      id="password"
                      v-model="formData.password"
                      class="form-input"
                      placeholder="Enter your password (at least 8 characters)"
                      required
                    />
                    <button
                      type="button"
                      class="password-toggle"
                      @click="togglePassword"
                    >
                      <svg v-if="!showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                      </svg>
                      <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                        <path d="M1 1l22 22" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div class="form-group">
                  <label for="confirmPassword" class="form-label">Confirm Password</label>
                  <div class="password-input-wrapper">
                    <input
                      :type="showConfirmPassword ? 'text' : 'password'"
                      id="confirmPassword"
                      v-model="formData.confirmPassword"
                      class="form-input"
                      placeholder="Re-enter your password"
                      required
                    />
                    <button
                      type="button"
                      class="password-toggle"
                      @click="toggleConfirmPassword"
                    >
                      <svg v-if="!showConfirmPassword" width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                      </svg>
                      <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                        <path d="M1 1l22 22" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div class="form-options">
                  <label class="checkbox-label">
                    <input type="checkbox" v-model="formData.agreeTerms" required />
                    <span class="checkbox-custom"></span>
                    I agree to the <a href="/app/capgen/eula.html" class="terms-link" target="_blank">Terms of Service</a> and <a href="/app/capgen/privacy.html" class="terms-link" target="_blank">Privacy Policy</a>
                  </label>
                </div>

                <button
                  type="submit"
                  class="btn btn-blue btn-large signin-submit"
                  :disabled="isLoading"
                >
                  <span v-if="!isLoading">Create Account</span>
                  <span v-else class="loading-spinner">
                    <svg width="20" height="20" viewBox="0 0 24 24" class="spinner">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
                      <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
                    </svg>
                    Creating account...
                  </span>
                </button>
              </form>

              <div class="signin-footer">
                <p class="signup-prompt">
                  Already have an account?
                  <router-link to="/capgen/signin" class="signup-link">Sign in now</router-link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useHead } from '@vueuse/head'
import { useAuth0 } from '@auth0/auth0-vue'
import { useUserStore } from '@/store/user.js'
import { signupWithAuth0 } from '@/api/auth.js'
import { isAuth0Configured } from '@/config/auth0.js'

const router = useRouter()
const auth0 = useAuth0()
const userStore = useUserStore()

// 表单数据
const formData = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 界面状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoading = ref(false)

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 注册处理（使用API直接传递邮箱密码）
const handleSignup = async () => {
  // 表单验证
  if (!formData.name || !formData.email || !formData.password || !formData.confirmPassword) {
    alert('Please fill in all required fields')
    return
  }

  if (formData.password !== formData.confirmPassword) {
    alert('Passwords do not match')
    return
  }

  if (formData.password.length < 8) {
    alert('Password must be at least 8 characters long')
    return
  }

  if (!formData.agreeTerms) {
    alert('Please agree to the Terms of Service and Privacy Policy')
    return
  }

  // 检查Auth0是否已配置
  if (!isAuth0PasswordModeConfigured()) {
    alert('Auth0密码模式配置未完成，请联系管理员配置认证系统')
    return
  }

  try {
    isLoading.value = true
    
    // 使用API直接传递注册信息
    const result = await signupWithEmailPassword(
      formData.email,
      formData.password,
      formData.name
    )

    if (result.success) {
      // 注册成功后自动登录
      userStore.setUser(result.user)
      userStore.setToken(result.token)
      
      alert('Registration successful! Welcome to CapGen!')
      router.push('/capgen')
    } else {
      alert(result.message || 'Registration failed, please try again later')
    }
  } catch (error) {
    console.error('注册错误:', error)
    alert('Registration failed, please try again later')
  } finally {
    isLoading.value = false
  }
}

// 设置页面元数据
useHead({
  title: 'Sign Up - CapGen | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'Sign up for a CapGen account and start using professional AI video subtitle generation services. Quick registration, instant experience.'
    }
  ]
})
</script>

<style scoped>
@import '@/assets/styles/pages/capgen.css';
</style> 