<template>
  <div class="capgen-product-page">
    <!-- 登录区域 -->
    <section class="signin-section">
      <div class="container">
        <div class="signin-content">
          <div class="signin-card">
            <div class="signin-header">
              <div class="logo-section">
                <img src="/images/logo_capgen.png" alt="CapGen Logo" class="signin-logo" />
                <h1 class="signin-title">Sign in to CapGen</h1>
              </div>
              <p class="signin-description">To continue with professional AI subtitle services</p>
            </div>

            <div class="signin-form-section">
              <!-- 邮箱登录表单 -->
              <form @submit.prevent="handleEmailSignin" class="signin-form">
                <div class="form-group">
                  <label for="email" class="form-label">Email</label>
                  <input
                    type="email"
                    id="email"
                    v-model="formData.email"
                    class="form-input"
                    placeholder="Enter your email address"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="password" class="form-label">Password</label>
                  <div class="password-input-wrapper">
                    <input
                      :type="showPassword ? 'text' : 'password'"
                      id="password"
                      v-model="formData.password"
                      class="form-input"
                      placeholder="Enter your password"
                      required
                    />
                    <button
                      type="button"
                      class="password-toggle"
                      @click="togglePassword"
                    >
                      <svg v-if="!showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z" stroke="currentColor" stroke-width="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                      </svg>
                      <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                        <path d="M1 1l22 22" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>
                  </div>
                </div>

                <div class="form-options">
                  <label class="checkbox-label">
                    <input type="checkbox" v-model="formData.rememberMe" />
                    <span class="checkbox-custom"></span>
                    Remember me
                  </label>
                  <a href="#" class="forgot-password">Forgot password?</a>
                </div>

                <button
                  type="submit"
                  class="btn btn-blue btn-large signin-submit"
                  :disabled="isLoading"
                >
                  <span v-if="!isLoading">Sign In</span>
                  <span v-else class="loading-spinner">
                    <svg width="20" height="20" viewBox="0 0 24 24" class="spinner">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
                      <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
                    </svg>
                    Signing in...
                  </span>
                </button>
              </form>
            <div class="divider">
                <span class="divider-text">or</span>
            </div>
            <!-- Google 登录 -->
              <button class="btn-google-signin" @click="handleGoogleSignin">
                <svg width="20" height="20" viewBox="0 0 24 24" class="google-icon">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Sign in with Google
              </button>

              

              <div class="signin-footer">
                <p class="signup-prompt">
                  Don't have an account?
                  <router-link to="/capgen/signup" class="signup-link">Sign up now</router-link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useHead } from '@vueuse/head'
import { useAuth0 } from '@auth0/auth0-vue'
import { useUserStore } from '@/store/user.js'
import { signinWithAuth0, signinWithGoogle } from '@/api/auth.js'
import { isAuth0Configured } from '@/config/auth0.js'

const router = useRouter()
const auth0 = useAuth0()
const userStore = useUserStore()

// 表单数据
const formData = reactive({
  email: '',
  password: '',
  rememberMe: false
})

// 界面状态
const showPassword = ref(false)
const isLoading = ref(false)

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 邮箱登录处理（暂时禁用，提示使用Google登录）
const handleEmailSignin = async () => {
  alert('邮箱密码登录功能暂未开放，请使用Google登录')
}

// Google登录处理（使用Auth0）
const handleGoogleSignin = async () => {
  // 检查Auth0是否已配置
  if (!isAuth0Configured()) {
    alert('Auth0配置未完成，请联系管理员配置认证系统')
    return
  }

  try {
    isLoading.value = true
    await signinWithGoogle(auth0)
  } catch (error) {
    console.error('Google登录错误:', error)
    alert('Google login failed, please try again later')
  } finally {
    isLoading.value = false
  }
}

// 设置页面元数据
useHead({
  title: 'Sign In - CapGen | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'Sign in to CapGen and start using professional AI video subtitle generation services. Support email login and Google quick login.'
    }
  ]
})

// 在script setup中添加调试信息
onMounted(() => {
  // 调试信息
  const auth0Config = getAuth0Config()
  console.log('当前环境:', process.env.NODE_ENV)
  console.log('当前域名:', window.location.origin)
  console.log('Auth0配置:', {
    domain: auth0Config.domain,
    clientId: auth0Config.clientId,
    redirectUri: auth0Config.redirectUri
  })
  
  // 检查是否有错误参数
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.has('error')) {
    console.error('Auth0错误:', {
      error: urlParams.get('error'),
      error_description: urlParams.get('error_description'),
      error_uri: urlParams.get('error_uri')
    })
  }
})
</script>

<style scoped>
@import '@/assets/styles/pages/capgen.css';
</style> 