<template>
  <div class="capgen-product-page">
    <!-- 登录回调处理区域 -->
    <section class="signin-section">
      <div class="">
        <div class="signin-content">
          <div class="signin-card">
            <div class="signin-header">
              <div class="logo-section">
                <img src="/images/logo_capgen.png" alt="CapGen Logo" class="signin-logo" />
                
              </div>
              <h1 class="signin-title">Login to CapGen</h1>
              <!-- <p class="signin-description">{{ description }}</p> -->
            </div>

            <div class="callback-content">
              <!-- 加载状态 -->
              <div v-if="isLoading" class="loading-container">
                <div class="loading-spinner-large">
                  <svg width="48" height="48" viewBox="0 0 24 24" class="spinner">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1" fill="none" opacity="0.3"/>
                    <path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"/>
                  </svg>
                </div>
                <p class="loading-text">{{ title }}</p>
              </div>

              <!-- 错误状态  -->
              <div v-else-if="hasError" class="error-container">
                <i class="bi bi-exclamation-circle" style="font-size: 48px; color: #c32e2e;"></i>
                <p class="error-text">{{ errorMessage }}</p>
                <div class="error-actions">
                  <button @click="handleRetryLogin" class="btn btn-s btn-blue">
                    Retry Login
                  </button>
                  <router-link to="/capgen" class="btn btn-s btn-gray">
                    Back to Home
                  </router-link>
                </div>
              </div>

              <!-- 成功状态 -->
              <div v-else class="success-container">
                <div class="success-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="#10B981" stroke-width="1"/>
                    <path d="m9 12 2 2 4-4" stroke="#10B981" stroke-width="1"/>
                  </svg>
                </div>
                <p class="success-text">Login successful! redirecting...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth0 } from '@auth0/auth0-vue'
import { useHead } from '@vueuse/head'
import { handleAuth0Callback, handleAuthError, retryLogin } from '@/api/auth.js'

const router = useRouter()
const auth0 = useAuth0()

const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

const title = computed(() => {
  if (isLoading.value) return 'Processing Authentication'
  if (hasError.value) return 'Authentication Failed'
  return 'Authentication Successful'
})

// 处理Auth0回调
const handleCallback = async () => {
  try {
    isLoading.value = true
    hasError.value = false

    const result = await handleAuth0Callback(auth0)
    
    if (result.success) {
      console.log('登录回调处理成功:')
      // 可以根据需要跳转到特定页面
    } else {
      throw result.error
    }
  } catch (error) {
    console.error('Auth0回调处理失败:', error)
    hasError.value = true
    errorMessage.value = handleAuthError(error)
  } finally {
    isLoading.value = false
  }
}

// 重试登录
const handleRetryLogin = () => {
  retryLogin(router, '/capgen/signin')
}

// 组件挂载时处理回调
onMounted(() => {
  handleCallback()
})

// 设置页面元数据
useHead({
  title: 'Authentication Callback - CapGen | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'Processing authentication callback for CapGen login.'
    }
  ]
})
</script>

<style scoped>
/* CapGen产品页面基础样式 */
.capgen-product-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F7FAFC 0%, #EDF2F7 100%);
}

/* 登录区域样式 */
.signin-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 0 0 100px 0;
}

.signin-content {
  width: 380px;
}

.signin-card {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 48px;
  text-align: center;
  height: 400px;
}

.signin-header {
  margin-bottom: 32px;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.signin-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.signin-title {
  font-size: 20px;
  font-weight: 200;
  color: #0D141C;
  line-height: 1.2;
  margin: 0;
}

/* 回调内容样式 */
.callback-content {
  text-align: center;
  padding: 40px 0;
}

.loading-container,
.error-container,
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  height: 80px;
}

.loading-spinner-large {
  color: #369EFF;
  font-weight: 200;
}

.loading-spinner-large .spinner {
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #4573A1;
  margin: 0;
}

.error-container {
  color: #c32e2e;
}

.error-text {
  font-size: 16px;
  margin: 0 0 20px 0;
  max-width: 400px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.success-container {
  color: var(--success-color);
  font-weight: 200;
}

.success-text {
  font-size: 16px;
  color: var(--success-color);
  margin: 0;
}

/* 按钮样式 */
.btn-gray {
  background: #F0F4F8;
  color: #4573A1;
}

.btn-gray:hover {
  background: #E5EDF5;
  transform: translateY(-1px);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .signin-card {
    padding: 32px 24px;
    margin: 20px;
  }
  
  .signin-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
