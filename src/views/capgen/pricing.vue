<template>
  <div class="capgen-pricing-page">
    <!-- 背景装饰 -->
    <!-- <div class="top-background"></div> -->
    
    <!-- 全屏Loading遮罩 -->
    <div v-if="isPaymentProcessing" class="payment-loading-overlay">
      <div class="payment-loading-content">
        <div class="loading-spinner">
          <i class="bi bi-spinner" style="animation: spin 1s linear infinite; font-size: 48px;"></i>
        </div>
        <h3 class="loading-title">{{ loadingMessage }}</h3>
        <p class="loading-description">Please wait while we prepare your subscription...</p>
      </div>
    </div>
    
    <!-- 头部导航 -->
    <CapGenHeader currentPage="pricing" />

    <!-- 价格页面主标题 -->
    <section class="pricing-hero-section">
      <div class="container">
        <div class="pricing-hero-content">
          <h1 class="pricing-hero-title">Choose Your Perfect Plan</h1>
          <p class="pricing-hero-description">
            Start with our free plan or upgrade to unlock advanced features. 
          </p>
          
          <!-- 计费周期切换 -->
          <div class="billing-toggle">
            <div class="toggle-container">
              <button class="toggle-option" :class="{ active: billingType === 'monthly' }" @click="billingType = 'monthly'">
                Monthly
              </button>
              <button class="toggle-option" :class="{ active: billingType === 'annual' }" @click="billingType = 'annual'">
                Annual
                <span class="save-badge">Save 20%</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 价格方案 -->
    <section class="pricing-plans-section">
      <div class="container">
        <div class="pricing-plans-grid">
          <div 
            v-for="planKey in displayPlans" 
            :key="planKey"
            class="pricing-plan"
            :class="{ featured: pricingPlans[planKey].popular }"
          >
            <div v-if="pricingPlans[planKey].popular" class="popular-label">Most Popular</div>
            <div class="plan-header">
              <h3 class="plan-name">{{ pricingPlans[planKey].name }}</h3>
              <div class="plan-price">
                <span class="price-amount" v-if="planKey === 'free'">Free</span>
                <span class="price-amount" v-else>${{ getPlanPrice(planKey, billingType) }}</span>
                <span class="price-period" v-if="planKey !== 'free'">/month</span>
              </div>
              <p class="plan-description">{{ pricingPlans[planKey].description }}</p>
            </div>
            <button 
              :class="pricingPlans[planKey].popular ? 'btn btn-primary plan-button' : 'btn btn-outline plan-button'"
              :disabled="isPaymentProcessing"
              @click="selectPlan(planKey)"
            >
              <span v-if="isPaymentProcessing && currentProcessingPlan === planKey">
                <i class="bi bi-spinner" style="animation: spin 1s linear infinite;"></i>
                Creating Order...
              </span>
              <span v-else>
                {{ planKey === 'free' ? 'Get Started' : 'Choose Plan' }}
              </span>
            </button>
            <div class="plan-features">
              <div class="feature-item" v-for="feature in pricingPlans[planKey].features" :key="feature">
                <i class="bi bi-check2"></i>
                <span>{{ feature }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能对比表 -->
    <section class="feature-comparison-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Compare All Features</h2>
          <p class="section-description">Choose the plan that fits your needs</p>
        </div>
        
        <div class="comparison-table">
          <div class="table-header">
            <div class="feature-column">Features</div>  
            <div class="plan-column">Free</div>
            <div class="plan-column">Basic</div>
            <div class="plan-column featured">Pro</div>
            <div class="plan-column">Business</div>
          </div>
          
          <div class="table-row" v-for="(feature, key) in featureComparison" :key="key">
            <div class="feature-name">{{ feature.name }}</div>
            <div class="feature-value">{{ getFeatureValue(key, 'free') }}</div>
            <div class="feature-value">{{ getFeatureValue(key, 'basic') }}</div>
            <div class="feature-value">{{ getFeatureValue(key, 'pro') }}</div>
            <div class="feature-value">{{ getFeatureValue(key, 'business') }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 常见问题 -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Frequently Asked Questions</h2>
        </div>
        <div class="faq-grid">
          <div class="faq-item" v-for="(faq, index) in pricingFaqData" :key="index">
            <h4 class="faq-question">{{ faq.question }}</h4>
            <p class="faq-answer">{{ faq.answer }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="capgen-cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">Ready to Get Started?</h2>
          <p class="cta-description">
            Join thousands of creators who trust CapGen for their subtitle needs.
          </p>
          <div class="cta-buttons">
            <router-link to="/capgen/download">
              <button class="btn btn-primary btn-large">
                Start Free Trial
              </button>
            </router-link>
            <!-- <button class="btn btn-primary btn-large" @click="selectPlan('pro')">
              Start Free Trial
            </button> -->
            <!-- <button class="btn btn-outline btn-large" @click="contactSales">
              Talk to Sales
            </button> -->
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <CapGenFooter />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import CapGenHeader from '@/components/CapGenHeader.vue'
import CapGenFooter from '@/components/CapGenFooter.vue'
import { 
  pricingPlans, 
  featureComparison, 
  pricingFaqData, 
  getPlanPrice, 
  getProductId,
  getFeatureValue 
} from '@/store/capgen.js'
import { createPayment } from '@/api/strip.js'
import { useUserStore } from '@/store/user.js'
import { signinWithAuth0 } from '@/api/auth.js'
import { useAuth0 } from '@auth0/auth0-vue'
import { showSuccess, showError, showInfo, showWarning } from '@/utils/toast.js'


// 响应式数据
const billingType = ref('monthly')
const isPaymentProcessing = ref(false)
const currentProcessingPlan = ref(null)
const loadingMessage = ref('Creating Your Order')

// 路由和用户状态
const router = useRouter()
const userStore = useUserStore()
const auth0 = useAuth0()

// 计算属性
const displayPlans = computed(() => {
  return Object.keys(pricingPlans)
})

const isLoggedIn = computed(() => {
  return userStore.isLoggedIn
})

// 设置页面元数据
useHead({
  title: 'CapGen Pricing - Choose Your Perfect Plan | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'Choose the perfect CapGen plan for your subtitle needs. Start with a free trial and upgrade as you grow. All plans include advanced AI-powered subtitle extraction and translation.'
    },
    {
      name: 'keywords',
      content: 'CapGen pricing, subtitle tool pricing, video translation pricing, AI subtitle cost, subscription plans'
    }
  ]
})

// 方法
const selectPlan = async (planKey) => {
  // 免费计划特殊处理
  if (planKey === 'free') {
    showInfo('Free plan activated! You can now use CapGen with limited features.', { title: 'Free Plan Activated' })
    // 可以在这里添加免费计划的激活逻辑，比如跳转到下载页面
    router.push('/capgen/download')
    return
  }

  // 检查是否已登录
  if (!isLoggedIn.value) {
    // 将购买意图保存到URL参数中，登录后自动触发
    const returnUrl = `/capgen/pricing?auto_purchase=${planKey}&billing=${billingType.value}`
    
    try {
      await signinWithAuth0(returnUrl, auth0)
    } catch (error) {
      console.error('登录失败:', error)
      showError('Login failed, please try again', { title: 'Login Error' })
    }
    return
  }

  await processPurchase(planKey, billingType.value)
}

// 处理购买流程
const processPurchase = async (planKey, billing) => {
  // 防止重复点击
  if (isPaymentProcessing.value) {
    return
  }

  try {
    console.log('开始处理购买流程:', { planKey, billing, timestamp: new Date().toISOString() })
    
    isPaymentProcessing.value = true
    currentProcessingPlan.value = planKey
    loadingMessage.value = 'Creating Your Order'

    const productId = getProductId(planKey, billing)
    console.log('获取到的产品ID:', productId)
    
    if (!productId) {
      console.error('无效的订阅计划:', { planKey, billing })
      showError('Invalid subscription plan, please choose again', { title: 'Plan Error' })
      return
    }

    // 创建支付订单
    loadingMessage.value = 'Preparing Payment'
    const currentUrl = window.location.origin
    const successUrl = `${currentUrl}/capgen/payment-success?status=success`
    // const cancelUrl = `${currentUrl}/capgen/payment-success?status=cancelled`

    console.log('准备创建支付订单:', {
      productId,
      successUrl,
      currentUrl,
      timestamp: new Date().toISOString()
    })

    const paymentResult = await createPayment(productId, successUrl)
    console.log('支付订单创建结果:', paymentResult)

    if (paymentResult.success && paymentResult.pay_url && paymentResult.pay_id) {
      console.log('支付订单创建成功，准备跳转:', {
        pay_id: paymentResult.pay_id,
        pay_url: paymentResult.pay_url,
        plan: planKey,
        billing: billing,
        product_id: productId
      })
      
      // 存储支付信息到localStorage，支付成功后可以查询状态
      localStorage.setItem('capgen_payment_info', JSON.stringify({
        pay_id: paymentResult.pay_id,
        plan: planKey,
        billing: billing,
        product_id: productId,
        timestamp: Date.now()
      }))
      
      // 跳转到支付页面
      loadingMessage.value = 'Redirecting to Payment'
      setTimeout(() => {
        console.log('跳转到支付页面:', paymentResult.pay_url)
        window.location.href = paymentResult.pay_url
      }, 500)
    } else {
      console.error('支付订单创建失败:', {
        success: paymentResult.success,
        error: paymentResult.error,
        pay_url: paymentResult.pay_url,
        pay_id: paymentResult.pay_id,
        planKey,
        billing,
        productId
      })
      showError(paymentResult.error || 'Failed to create payment order, please try again', { title: 'Order Creation Failed' })
    }
  } catch (error) {
    console.error('支付处理失败 - 详细错误信息:', {
      error: error.message,
      stack: error.stack,
      planKey,
      billing,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })
    showError('Payment processing failed, please try again', { title: 'Processing Failed' })
  } finally {
    isPaymentProcessing.value = false
    currentProcessingPlan.value = null
    loadingMessage.value = 'Creating Your Order'
  }
}

// 处理URL参数中的支付状态（现在主要处理旧的回调，新的都会跳转到专门的成功页面）
const handlePaymentCallback = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const paymentStatus = urlParams.get('payment')
  
  if (paymentStatus === 'success') {
    // 旧的支付成功回调，重定向到新的成功页面
    const paymentInfo = localStorage.getItem('capgen_payment_info')
    if (paymentInfo) {
      try {
        const info = JSON.parse(paymentInfo)
        router.push(`/capgen/payment-success?status=success&plan=${info.plan}&billing=${info.billing}&pay_id=${info.pay_id}`)
        return
      } catch (error) {
        console.error('处理支付回调失败:', error)
      }
    }
  } else if (paymentStatus === 'cancelled') {
    // 支付取消，重定向到成功页面显示取消状态
    router.push('/capgen/payment-success?status=cancelled')
    return
  }
}

// 检查并处理自动购买
const checkAutoPurchase = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const autoPurchasePlan = urlParams.get('auto_purchase')
  const autoPurchaseBilling = urlParams.get('billing')
  
  if (autoPurchasePlan && isLoggedIn.value) {
    console.log('检测到自动购买请求:', { plan: autoPurchasePlan, billing: autoPurchaseBilling })
    
    // 设置计费类型
    if (autoPurchaseBilling) {
      billingType.value = autoPurchaseBilling
    }
    
    // 清除URL参数
    const newUrl = window.location.pathname
    window.history.replaceState({}, '', newUrl)
    
    // 显示提示并自动触发购买
    // showInfo(`Continuing with your ${autoPurchasePlan} subscription purchase...`, { title: 'Continue Purchase' })
    
    await processPurchase(autoPurchasePlan, billingType.value)
  }
}

onMounted(async () => {
  // 处理支付回调
  handlePaymentCallback()
  
  // 检查自动购买
  await checkAutoPurchase()
})

// 监听登录状态变化，处理异步登录完成的情况
watch(isLoggedIn, async (newValue) => {
  if (newValue) {
    // 用户刚登录，检查是否有自动购买请求
    await checkAutoPurchase()
  }
})
</script>

<style scoped>
@import '@/assets/styles/pages/capgen.css';

/* 添加spinner动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 全屏Loading遮罩样式 */
.payment-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(13, 20, 28, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.payment-loading-content {
  text-align: center;
  background: #FFFFFF;
  padding: 48px 32px;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 100%;
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.loading-spinner {
  color: #369EFF;
  margin-bottom: 24px;
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #0D141C;
  margin-bottom: 12px;
  letter-spacing: -0.02em;
}

.loading-description {
  font-size: 16px;
  color: #4573A1;
  margin: 0;
  line-height: 1.5;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .payment-loading-content {
    padding: 36px 24px;
    margin: 0 16px;
  }
  
  .loading-title {
    font-size: 20px;
  }
  
  .loading-description {
    font-size: 14px;
  }
}

/* 禁用状态下的按钮样式 */
.plan-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.plan-button:disabled:hover {
  transform: none;
}
</style> 