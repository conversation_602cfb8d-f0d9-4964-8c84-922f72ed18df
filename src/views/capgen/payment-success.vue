<template>
  <div class="capgen-product-page capgen-payment-success-page">
    <!-- 头部导航 -->
    <CapGenHeader currentPage="payment-success" />

    <!-- 支付结果区域 -->
    <section class="payment-result-section">
      <div class="container">
        <div class="payment-result-content">
          
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner">
              <i class="bi bi-spinner" style="animation: spin 1s linear infinite; font-size: 48px;"></i>
            </div>
            <h2 class="loading-title">Verifying Payment...</h2>
            <p class="loading-description">Please wait while we confirm your payment</p>
          </div>

          <!-- 支付成功 -->
          <div v-else-if="paymentStatus === 'success'" class="success-container">
            <div class="success-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#10B981" stroke-width="2"/>
                <path d="m9 12 2 2 4-4" stroke="#10B981" stroke-width="2"/>
              </svg>
            </div>
            <h1 class="success-title">Payment Successful!</h1>
            <p class="success-description">
              Congratulations! You are now a <strong>{{ getPlanDisplayName() }}</strong> member.
            </p>
            <div class="membership-info">
              <div class="membership-card">
                <div class="membership-header">
                  <h3>{{ getPlanDisplayName() }} Member</h3>
                  <span class="membership-badge">{{ billingType === 'annual' ? 'Annual' : 'Monthly' }}</span>
                </div>
                <div class="membership-features">
                  <div class="feature-item" v-for="feature in getPlanFeatures()" :key="feature">
                    <i class="bi bi-check2-circle"></i>
                    <span>{{ feature }}</span>
                  </div>
                </div>
                <div class="membership-actions">
                  <router-link to="/capgen/download">
                    <button class="btn btn-primary btn-large">
                      <i class="bi bi-download"></i>
                      Download CapGen App
                    </button>
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- 支付失败 -->
          <div v-else-if="paymentStatus === 'failed'" class="error-container">
            <div class="error-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#EF4444" stroke-width="2"/>
                <path d="m15 9-6 6" stroke="#EF4444" stroke-width="2"/>
                <path d="m9 9 6 6" stroke="#EF4444" stroke-width="2"/>
              </svg>
            </div>
            <h1 class="error-title">Payment Failed</h1>
            <p class="error-description">
              Unfortunately, your payment could not be processed. <br> 
              {{ errorMessage }}
            </p>
            <!-- <div class="error-actions">
              <router-link to="/capgen">
                <button class="btn btn-outline btn-large">
                  Back to CapGen
                </button>
              </router-link>
            </div> -->
          </div>

          <!-- 支付取消 -->
          <div v-else-if="paymentStatus === 'cancelled'" class="cancelled-container">
            <div class="cancelled-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#F59E0B" stroke-width="2"/>
                <path d="M8 12h8" stroke="#F59E0B" stroke-width="2"/>
              </svg>
            </div>
            <h1 class="cancelled-title">Payment Cancelled</h1>
            <p class="cancelled-description">
              Your payment has been cancelled. You can try again or choose a different plan.
            </p>
            <div class="cancelled-actions">
              <router-link to="/capgen/pricing">
                <button class="btn btn-primary btn-large">
                  Choose Plan
                </button>
              </router-link>
              <router-link to="/capgen">
                <button class="btn btn-outline btn-large">
                  Back to CapGen
                </button>
              </router-link>
            </div>
          </div>

        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <CapGenFooter />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useHead } from '@vueuse/head'
import CapGenHeader from '@/components/CapGenHeader.vue'
import CapGenFooter from '@/components/CapGenFooter.vue'
import { pricingPlans } from '@/store/capgen.js'
import { queryPaymentStatus } from '@/api/strip.js'
import { useUserStore } from '@/store/user.js'
import { getUserInfo } from '@/api/http.js'

// 响应式数据
const isLoading = ref(true)
const paymentStatus = ref(null) // 'success', 'failed', 'cancelled'
const errorMessage = ref('')
const planKey = ref('')
const billingType = ref('monthly')

// 路由和用户状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 计算属性
const getPlanDisplayName = () => {
  if (!planKey.value || !pricingPlans[planKey.value]) return 'Premium'
  return pricingPlans[planKey.value].name
}

const getPlanFeatures = () => {
  if (!planKey.value || !pricingPlans[planKey.value]) return []
  return pricingPlans[planKey.value].features
}

// 设置页面元数据
useHead({
  title: computed(() => {
    if (paymentStatus.value === 'success') {
      return `Payment Successful - Welcome to ${getPlanDisplayName()}! | CapGen`
    } else if (paymentStatus.value === 'failed') {
      return 'Payment Failed | CapGen'
    } else if (paymentStatus.value === 'cancelled') {
      return 'Payment Cancelled | CapGen'
    }
    return 'Processing Payment | CapGen'
  }),
  meta: [
    {
      name: 'description',
      content: 'CapGen payment processing result page'
    }
  ]
})

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const serverUser = await getUserInfo()
    if (serverUser) {
      const userData = {
        id: serverUser.uid,
        name: serverUser.name,
        nickname: serverUser.nickname,
        vip: serverUser.vip,
        vipExpiredAt: serverUser.vip_expired_at,
        loginTime: new Date().toISOString(),
      }
      await userStore.setUser(userData)
      console.log('用户信息已刷新')
    }
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

// 处理支付结果
const handlePaymentResult = async () => {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const status = urlParams.get('status')
    const plan = urlParams.get('plan')
    const billing = urlParams.get('billing')
    const payId = urlParams.get('pay_id')
    
    // 设置基本信息
    planKey.value = plan || 'pro'
    billingType.value = billing || 'monthly'

    if (status === 'success') {
      // 如果URL中有pay_id，使用它；否则从localStorage获取
      let payIdToUse = payId
      if (!payIdToUse) {
        const paymentInfo = localStorage.getItem('capgen_payment_info')
        if (paymentInfo) {
          try {
            const info = JSON.parse(paymentInfo)
            payIdToUse = info.pay_id
          } catch (error) {
            console.error('解析支付信息失败:', error)
          }
        }
      }
      
      if (payIdToUse) {
        // 验证支付状态
        const result = await queryPaymentStatus(payIdToUse)
        
        if (result.success && result.isPaid) {
          paymentStatus.value = 'success'
          // 刷新用户信息
          await refreshUserInfo()
          
          // 清除存储的支付信息
          localStorage.removeItem('capgen_payment_info')
        } else {
          paymentStatus.value = 'failed'
          errorMessage.value = 'Payment verification failed. Please contact support if you believe this is an error.'
        }
      } else {
        paymentStatus.value = 'failed'
        errorMessage.value = 'Payment ID not found. Please contact support.'
      }
    } else if (status === 'cancelled') {
      paymentStatus.value = 'cancelled'
      // 清除存储的支付信息
      localStorage.removeItem('capgen_payment_info')
    } else if (status === 'failed') {
      paymentStatus.value = 'failed'
      errorMessage.value = urlParams.get('error') || 'Payment processing failed. Please try again.'
    } else {
      // 尝试从localStorage获取支付信息
      const paymentInfo = localStorage.getItem('capgen_payment_info')
      if (paymentInfo) {
        try {
          const info = JSON.parse(paymentInfo)
          const result = await queryPaymentStatus(info.pay_id)
          
          planKey.value = info.plan
          billingType.value = info.billing
          
          if (result.success && result.isPaid) {
            paymentStatus.value = 'success'
            await refreshUserInfo()
            localStorage.removeItem('capgen_payment_info')
          } else {
            paymentStatus.value = 'failed'
            errorMessage.value = 'Payment verification failed.'
          }
        } catch (error) {
          console.error('处理支付信息失败:', error)
          paymentStatus.value = 'failed'
          errorMessage.value = 'Unable to verify payment status.'
        }
      } else {
        // 没有支付信息，重定向到定价页面
        router.push('/capgen/pricing')
        return
      }
    }
  } catch (error) {
    console.error('处理支付结果失败:', error)
    paymentStatus.value = 'failed'
    errorMessage.value = 'Unable to process payment result.'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  handlePaymentResult()
})
</script>

<style scoped>
@import '@/assets/styles/pages/capgen.css';

/* 支付结果页面样式 - 使用 CapGen 设计系统 */
.payment-result-section {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120px 0 80px;
  z-index: 1;
}

.payment-result-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

/* 加载状态 */
.loading-container {
  padding: 60px 0;
}

.loading-spinner {
  color: #369EFF;
  margin-bottom: 24px;
}

.loading-title {
  font-size: 32px;
  font-weight: 600;
  color: #0D141C;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.loading-description {
  font-size: 18px;
  color: #4573A1;
  margin-bottom: 0;
  line-height: 1.6;
}

/* 成功状态 */
.success-container {
  padding: 60px 0;
}

.success-icon {
  margin-bottom: 24px;
}

.success-title {
  font-size: 36px;
  font-weight: 700;
  color: #10B981;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.success-description {
  font-size: 20px;
  color: #4573A1;
  margin-bottom: 40px;
  line-height: 1.6;
}

.membership-info {
  margin-top: 40px;
}

.membership-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid #369EFF;
  transition: all 0.3s ease;
}

.membership-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.membership-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E5EDF5;
}

.membership-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #0D141C;
  margin: 0;
}

.membership-badge {
  background: linear-gradient(135deg, #369EFF, #5E8BFF);
  color: #FFFFFF;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.membership-features {
  text-align: left;
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.feature-item i {
  color: #369EFF;
  font-size: 18px;
}

.feature-item span {
  color: #4573A1;
  font-size: 16px;
}

.membership-actions {
  text-align: center;
}

/* 错误状态 */
.error-container {
  padding: 60px 0;
}

.error-icon {
  margin-bottom: 24px;
}

.error-title {
  font-size: 36px;
  font-weight: 700;
  color: #EF4444;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.error-description {
  font-size: 18px;
  color: #4573A1;
  margin-bottom: 40px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .btn,
.cancelled-actions .btn {
  min-width: 160px;
}

/* 取消状态 */
.cancelled-container {
  padding: 60px 0;
}

.cancelled-icon {
  margin-bottom: 24px;
}

.cancelled-title {
  font-size: 36px;
  font-weight: 700;
  color: #F59E0B;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.cancelled-description {
  font-size: 18px;
  color: #4573A1;
  margin-bottom: 40px;
  line-height: 1.6;
}

.cancelled-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 页面特定的按钮样式覆盖 */
.capgen-payment-success-page .btn-primary {
  background: linear-gradient(135deg, #369EFF, #5E8BFF);
  border: none;
  color: #FFFFFF;
  font-weight: 600;
  transition: all 0.3s ease;
}

.capgen-payment-success-page .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(54, 158, 255, 0.3);
}

.capgen-payment-success-page .btn-outline {
  background: #E5EDF5;
  color: #0D141C;
  border: 1px solid #CCDBEB;
  font-weight: 600;
}

.capgen-payment-success-page .btn-outline:hover {
  background: #D1E3F3;
  border-color: #369EFF;
  transform: translateY(-1px);
}

/* 添加spinner动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-result-section {
    padding: 80px 0 60px;
  }

  .payment-result-content {
    padding: 0 20px;
  }

  .success-title,
  .error-title,
  .cancelled-title {
    font-size: 28px;
  }

  .loading-title {
    font-size: 24px;
  }

  .success-description,
  .error-description,
  .cancelled-description {
    font-size: 16px;
  }

  .membership-card {
    padding: 24px;
  }

  .membership-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .error-actions,
  .cancelled-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
  }
}
</style> 