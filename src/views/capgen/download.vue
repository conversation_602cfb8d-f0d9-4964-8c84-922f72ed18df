<template>
  <div class="capgen-product-page">
    <!-- 背景装饰 -->
    <!-- <div class="top-background"></div> -->
    
    <!-- 头部导航 -->
    <CapGenHeader currentPage="download" />

    <!-- Download Options -->
    <section class="download-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Choose Your Platform</h2>
          <p class="section-subtitle">CapGen is available for all major operating systems with native performance</p>
        </div>
        
        <div class="download-grid">
          <div class="download-card">
            <div class="download-icon">
              <i class="bi bi-windows" style="font-size: 46px;"></i>
            </div>
            <h3 class="download-title">Windows</h3>
            <p class="download-description">
              Full-featured desktop application with advanced editing tools and batch processing capabilities.
            </p>
            <ul class="download-features">
              <li>Windows 10/11 Support</li>
              <li>Native Performance Optimization</li>
              <li>Batch Processing</li>
              <li>Offline Subtitle Generation</li>
              <li>Advanced Editing Tools</li>
            </ul>
            <button class="btn btn-outline btn-large download-button" disabled>
              
              Coming Soon
            </button>
            <p class="download-meta">Please wait for release</p>
          </div>

          <div class="download-card featured">
            <div class="popular-label">Available Now</div>
            <div class="download-icon">
              <i class="bi bi-apple" style="font-size: 48px;"></i>
            </div>
            <h3 class="download-title">macOS</h3>
            <p class="download-description">
              Native macOS application with seamless integration and optimized performance for Apple Silicon and Intel Macs.
            </p>
            <ul class="download-features">
              <li>macOS 13+ Support</li>
              <li>Apple Silicon Optimized</li>
              <li>Native Interface Design</li>
              <li>Spotlight Integration</li>
            </ul>
            <button class="btn btn-primary btn-large download-button" onclick="window.open('/app/capgen/release/CapGen_1.5.0_17.dmg', '_blank')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;">
                <path d="M3 16.5V18.75C3 19.9926 4.00736 21 5.25 21H18.75C19.9926 21 21 19.9926 21 18.75V16.5M16.5 12L12 16.5M12 16.5L7.5 12M12 16.5V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Download for macOS
            </button>
            <p class="download-meta">Version 1.5.0 • 24MB • Universal Binary</p>
          </div>

          <div class="download-card">
            <div class="download-icon">
              <i class="bi bi-tux" style="font-size: 48px;"></i>
            </div>
            <h3 class="download-title">Linux</h3>
            <p class="download-description">
              Cross-platform Linux application supporting major distributions with command-line and GUI interfaces.
            </p>
            <ul class="download-features">
              <li>Ubuntu/Debian Support</li>
              <li>AppImage Format</li>
              <li>Command Line Tools</li>
            </ul>
            <button class="btn btn-outline btn-large download-button" disabled>
              
              Coming Soon
            </button>
            <p class="download-meta">Please wait for release</p>
          </div>
        </div>
      </div>
    </section>

    <!-- System Requirements -->
    <section class="system-requirements">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">System Requirements</h2>
          <p class="section-subtitle">Ensure your system meets the minimum requirements for optimal performance</p>
        </div>
        
        <div class="requirements-grid">
          <div class="requirement-card">
            <div class="requirement-icon">
              <i class="bi bi-windows" style="font-size: 28px;"></i>
            </div>
            <h4 class="requirement-title">Windows</h4>
            <ul class="requirement-list">
              <li>Windows 10 or later</li>
              <li>4GB RAM (8GB recommended)</li>
              <li>1GB available disk space</li>
              <li>Internet connection (for AI processing)</li>
            </ul>
          </div>

          <div class="requirement-card">
            <div class="requirement-icon">
              <i class="bi bi-apple" style="font-size: 28px;"></i>
            </div>
            <h4 class="requirement-title">macOS</h4>
            <ul class="requirement-list">
              <li>macOS 13.0 or later</li>
              <li>4GB RAM (8GB recommended)</li>
              <li>1GB available disk space</li>
              <li>Intel and Apple Silicon support</li>
            </ul>
          </div>

          <div class="requirement-card">
            <div class="requirement-icon">
              <i class="bi bi-tux" style="font-size: 28px;"></i>
            </div>
            <h4 class="requirement-title">Linux</h4>
            <ul class="requirement-list">
              <li>Ubuntu 18.04+ / Debian 10+</li>
              <li>4GB RAM (8GB recommended)</li>
              <li>1GB available disk space</li>
              <li>X11 or Wayland support</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Installation Guide -->
    <section class="workflow-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Quick Installation Guide</h2>
          <p class="section-subtitle">Get up and running in minutes with our simple installation process</p>
        </div>
        <div class="workflow-steps">
          <div class="workflow-step">
            <div class="step-visual">
              <div class="step-number">1</div>
              <div class="step-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                  <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V10.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 0-.708-.708l3-3z"/>
                </svg>
              </div>
            </div>
            <div class="step-content">
              <h3 class="step-title">Download Installer</h3>
              <p class="step-description">
                Choose the version for your operating system and download the installer. All versions are digitally signed for security.
              </p>
              <div class="step-features">
                <span class="feature-tag">Digital Signature</span>
                <span class="feature-tag">Secure Download</span>
                <span class="feature-tag">Auto Update</span>
              </div>
            </div>
          </div>
          
          <div class="workflow-step">
            <div class="step-visual">
              <div class="step-number">2</div>
              <div class="step-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                  <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.061L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                </svg>
              </div>
            </div>
            <div class="step-content">
              <h3 class="step-title">Run Installation</h3>
              <p class="step-description">
                Run the downloaded installer and follow the setup wizard. Installation typically takes just a few clicks.
              </p>
              <div class="step-features">
                <span class="feature-tag">Easy Setup</span>
                <span class="feature-tag">Auto Configure</span>
                <span class="feature-tag">Quick Deploy</span>
              </div>
            </div>
          </div>
          
          <div class="workflow-step">
            <div class="step-visual">
              <div class="step-number">3</div>
              <div class="step-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                  <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
                </svg>
              </div>
            </div>
            <div class="step-content">
              <h3 class="step-title">Start Creating</h3>
              <p class="step-description">
                Launch CapGen, create an account or sign in to your existing account, and start your first subtitle project immediately.
              </p>
              <div class="step-features">
                <span class="feature-tag">Free Start</span>
                <span class="feature-tag">Ready to Use</span>
                <span class="feature-tag">Pro Results</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- Final CTA -->
    <section class="capgen-cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">Ready to Get Started?</h2>
            <p class="cta-description">
              Download CapGen now and start creating professional subtitles in minutes. 
              Free to try with no credit card required.
            </p>
          </div>
          <div class="cta-actions">
            <button class="btn btn-primary btn-large" onclick="window.open('https://github.com/LittleGrass-AI/CapGen-macOS/releases/latest', '_blank')">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;">
                <path d="M3 16.5V18.75C3 19.9926 4.00736 21 5.25 21H18.75C19.9926 21 21 19.9926 21 18.75V16.5M16.5 12L12 16.5M12 16.5L7.5 12M12 16.5V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Download Now
            </button>
          </div>
          <div class="cta-guarantee">
            <p>✓ Free download • ✓ 100 minutes free processing • ✓ Professional results</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <CapGenFooter />
  </div>
</template>

<script setup>
import { useHead } from '@vueuse/head'
import CapGenHeader from '@/components/CapGenHeader.vue'
import CapGenFooter from '@/components/CapGenFooter.vue'

// 设置页面元数据
useHead({
  title: 'Download CapGen - AI Video Subtitle Generator | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'Download CapGen for Windows, macOS, and Linux. Professional AI-powered video subtitle generation and translation software. Free download, no credit card required.'
    },
    {
      name: 'keywords',
      content: 'CapGen download, video subtitle software, AI subtitle generator, Windows macOS Linux, subtitle tool download'
    },
    {
      property: 'og:title',
      content: 'Download CapGen - Professional AI Video Subtitle Generator'
    },
    {
      property: 'og:description',
      content: 'Download CapGen for your platform. Available for Windows, macOS, and Linux with professional AI subtitle generation capabilities.'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script> 

<style scoped>
@import '@/assets/styles/pages/capgen.css';
</style> 