<template>
  <div class="capgen-product-page">
    <!-- 背景装饰 -->
    <div class="top-background"></div>
    
    <!-- 头部导航 -->
    <CapGenHeader currentPage="product" />

    <!-- 主标题区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <!-- <div class="hero-badge">
            <span class="badge-text">🎬 AI-Powered Video Subtitles</span>
          </div> -->
          <h1 class="hero-title">Transform Videos into Global Content with CapGen</h1>
          <p class="hero-description">
            CapGen automatically extracts, generates, and translates video subtitles with 99% accuracy. 
            Make your content accessible to millions worldwide in minutes, not hours.
          </p>
          <div class="hero-actions">
            <router-link to="/capgen/download">
              <button class="btn btn-black btn-large">
                Download App
              </button>
            </router-link>
            <!-- <router-link to="/capgen/pricing" class="btn btn-large">
              View Pricing Plans
            </router-link> -->
          </div>
          
        </div>
      </div>
    </section>

    <!-- 视频演示区域 -->
    <section class="demo-section">
      <div class="container">
        <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number">1M+</span>
              <span class="stat-label">Videos Processed</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">30+</span>
              <span class="stat-label">Languages Supported</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">99%</span>
              <span class="stat-label">Accuracy Rate</span>
            </div>
          </div>
        <div class="demo-content">
          <div class="demo-video">
            <div class="video-placeholder" @click="handlePlayClick">
              <!-- 播放按钮状态 -->
              <div v-if="!isLoading" class="play-button">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                </svg>
              </div>
              <!-- Loading状态 -->
              <div v-else class="loading-spinner">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                    <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                  </circle>
                </svg>
              </div>
              <p class="demo-text">{{ isLoading ? 'Loading Video...' : 'Watch CapGen in Action' }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心特性 -->
    <section class="features-section" id="features">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Powerful Features for Professional Results</h2>
          <p class="section-subtitle">Everything you need to create professional subtitles and reach global audiences</p>
        </div>
        
        <div class="features-showcase">
          <div class="feature-highlight">
            <div class="feature-content">
              <!-- <div class="feature-badge">⚡ Lightning Fast</div> -->
              <h3 class="feature-title">AI-Powered Subtitle Generation</h3>
              <p class="feature-description">
                Our advanced AI technology extracts and generates subtitles from your videos in minutes, 
                not hours. Support for 100+ languages with industry-leading accuracy.
              </p>
              <ul class="feature-list">
                <li>Automatic speech recognition</li>
                <li>Real-time processing</li>
                <li>Perfect timing synchronization</li>
                <li>Multiple export formats (SRT, VTT, ASS)</li>
              </ul>
            </div>
            <div class="feature-visual">
              <img src="/images/capgen/banner01.png" alt="AI Processing Demo" />
            </div>
          </div>

          <div class="feature-highlight reverse">
            <div class="feature-visual">
              <img src="/images/capgen/banner02.png" alt="Translation Demo" />
            </div>
            <div class="feature-content">
              <!-- <div class="feature-badge">🌍 Global Reach</div> -->
              <h3 class="feature-title">Instant Multi-Language Translation</h3>
              <p class="feature-description">
                Translate your subtitles into 100+ languages with context-aware AI translation. 
                Maintain cultural nuances and technical accuracy across all languages.
              </p>
              <ul class="feature-list">
                <li>100+ language pairs</li>
                <li>Context-aware translation</li>
                <li>Cultural adaptation</li>
                <li>Technical terminology support</li>
              </ul>
            </div>
          </div>

          <div class="feature-highlight">
            <div class="feature-content">
              <!-- <div class="feature-badge">🎯 Precision</div> -->
              <h3 class="feature-title">Perfect Synchronization</h3>
              <p class="feature-description">
                Advanced timing algorithms ensure your subtitles are perfectly synchronized with your video content. 
                Professional-grade results every time.
              </p>
              <ul class="feature-list">
                <li>Frame-perfect timing</li>
                <li>Automatic duration adjustment</li>
                <li>Reading speed optimization</li>
                <li>Professional formatting</li>
              </ul>
            </div>
            <div class="feature-visual">
              <img src="/images/capgen/banner03.png" alt="Synchronization Demo" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 使用案例 -->
    <section class="use-cases-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Perfect for Every Use Case</h2>
          <p class="section-subtitle">From content creators to enterprises, CapGen powers global video communication</p>
        </div>
        <div class="use-cases-grid">
          <div class="use-case-card">
            <div class="use-case-icon">
              <img src="/images/capgen/icon01.svg" alt="Online Education" />
            </div>
            <div class="use-case-content">
              <h4 class="use-case-title">Online Education</h4>
              <p class="use-case-description">
                Make your courses accessible to global students with accurate subtitles and translations. 
                Improve comprehension and engagement.
              </p>
              <div class="use-case-benefits">
                <span class="benefit">Increased accessibility</span>
                <span class="benefit">Better comprehension</span>
                <span class="benefit">Global reach</span>
              </div>
            </div>
          </div>
          
          <div class="use-case-card">
            <div class="use-case-icon">
              <img src="/images/capgen/icon02.svg" alt="Marketing & Sales" />
            </div>
            <div class="use-case-content">
              <h4 class="use-case-title">Marketing & Sales</h4>
              <p class="use-case-description">
                Expand your marketing reach by making product videos and promotional content accessible to international audiences.
              </p>
              <div class="use-case-benefits">
                <span class="benefit">Wider audience</span>
                <span class="benefit">Higher engagement</span>
                <span class="benefit">More conversions</span>
              </div>
            </div>
          </div>
          
          <div class="use-case-card">
            <div class="use-case-icon">
              <img src="/images/capgen/icon03.svg" alt="Content Creation" />
            </div>
            <div class="use-case-content">
              <h4 class="use-case-title">Content Creation</h4>
              <p class="use-case-description">
                YouTubers, podcasters, and content creators use CapGen to make their content accessible and reach global audiences.
              </p>
              <div class="use-case-benefits">
                <span class="benefit">Time saving</span>
                <span class="benefit">Professional quality</span>
                <span class="benefit">Multi-platform ready</span>
              </div>
            </div>
          </div>
          
          <div class="use-case-card">
            <div class="use-case-icon">
              <img src="/images/capgen/icon04.svg" alt="Enterprise Training" />
            </div>
            <div class="use-case-content">
              <h4 class="use-case-title">Enterprise Training</h4>
              <p class="use-case-description">
                Companies use CapGen for internal training videos, enabling multilingual teams to access the same high-quality content.
              </p>
              <div class="use-case-benefits">
                <span class="benefit">Team alignment</span>
                <span class="benefit">Cost effective</span>
                <span class="benefit">Scalable solution</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户评价 -->
    <section class="testimonials-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Trusted by Thousands Worldwide</h2>
          <p class="section-subtitle">See what our users say about CapGen's game-changing subtitle technology</p>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-content">
              <div class="rating">
                <span class="stars">★★★★★</span>
              </div>
              <p class="testimonial-text">
                "CapGen has revolutionized how we create subtitles for our educational content. 
                The accuracy is incredible and it saves us hours of manual work. Our international student engagement has increased by 300%."
              </p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar" style="background: #FEFF9F;">
                <img src="/images/capgen/head01.png" alt="Sarah Mitchell" />
              </div>
              <div class="author-info">
                <h4 class="author-name">Sarah Mitchell</h4>
                <p class="author-title">Head of Content, EduTech Academy</p>
                <p class="author-company">1000+ course enrollments</p>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card">
            <div class="testimonial-content">
              <div class="rating">
                <span class="stars">★★★★★</span>
              </div>
              <p class="testimonial-text">
                "We've seen a 250% increase in international viewership since using CapGen. 
                The translation quality is professional-grade and the turnaround time is amazing."
              </p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar" style="background: #D3EE98;">
                <img src="/images/capgen/head02.png" alt="David Reynolds" />
              </div>
              <div class="author-info">
                <h4 class="author-name">David Reynolds</h4>
                <p class="author-title">Managing Partner, Top Investments</p>
                <p class="author-company">100+ global meetings annually</p>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card">
            <div class="testimonial-content">
              <div class="rating">
                <span class="stars">★★★★★</span>
              </div>
              <p class="testimonial-text">
                "The synchronization is perfect every time. As a content creator, CapGen has become an essential tool in my workflow. 
                I can focus on creating while CapGen handles the technical work."
              </p>
            </div>
            <div class="testimonial-author">
              <div class="author-avatar" style="background: #F4E0AF;">
                <img src="/images/capgen/head03.png" alt="Emily Carter" />
              </div>
              <div class="author-info">
                <h4 class="author-name">Emily Carter</h4>
                <p class="author-title">Fitness Influencer & Digital Content Creator</p>
                <p class="author-company">2M+ YouTube subscribers</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 定价预览 -->
    <section class="pricing-preview-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Simple, Transparent Pricing</h2>
          <p class="section-subtitle">Choose the plan that fits your needs, from individual creators to enterprise teams</p>
        </div>
        <div class="pricing-cards">
          <div
            class="pricing-card"
            v-for="([key, plan]) in Object.entries(pricingPlans).slice(0, 3)"
            :key="key"
          >
            <h3 class="plan-name">{{ plan.name }}</h3>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="amount">{{ plan.price.monthly }}</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li v-for="feature in plan.features" :key="feature">{{ feature }}</li>
            </ul>
          </div>
        </div>
        <div class="pricing-footer">
          <router-link to="/capgen/pricing" class="pricing-link">
            View detailed pricing and features →
          </router-link>
        </div>
      </div>
    </section>

    <!-- 常见问题 -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Frequently Asked Questions</h2>
          <p class="section-subtitle">Everything you need to know about CapGen's subtitle technology</p>
        </div>
        <div class="faq-list">
          <div class="faq-item" v-for="(faq, index) in faqData" :key="index">
            <div class="faq-question" @click="toggleFaq(index)">
              <span>{{ faq.question }}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-chevron-down" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
              </svg>
            </div>
            <div class="faq-answer" v-show="activeFaq === index">
              <p>{{ faq.answer }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最终CTA -->
    <section class="capgen-cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">Ready to Reach a Global Audience?</h2>
            <p class="cta-description">
              Join thousands of creators and businesses using CapGen to break language barriers 
              and connect with audiences worldwide.
            </p>
          </div>
          <div class="cta-actions">
            <!-- <button class="btn btn-capgen-primary btn-large">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 8px;">
                <path d="M3 16.5V18.75C3 19.9926 4.00736 21 5.25 21H18.75C19.9926 21 21 19.9926 21 18.75V16.5M16.5 12L12 16.5M12 16.5L7.5 12M12 16.5V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Start Free Today
            </button> -->
            <router-link to="/capgen/download">
              <button class="btn btn-blue btn-large">
                Start Free Today
              </button>
            </router-link>
          </div>
          <div class="cta-guarantee">
            <p>✓ No credit card required • ✓ 100 minutes free processing • ✓ Professional results</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <CapGenFooter />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useHead } from '@vueuse/head'
import { useRoute, useRouter } from 'vue-router'
import CapGenHeader from '@/components/CapGenHeader.vue'
import CapGenFooter from '@/components/CapGenFooter.vue'
import { pricingPlans, faqData } from '@/store/capgen.js'

const route = useRoute()
const router = useRouter()

// FAQ 交互状态
const activeFaq = ref(null)
// 视频loading状态
const isLoading = ref(false)

const toggleFaq = (index) => {
  activeFaq.value = activeFaq.value === index ? null : index
}

// 处理播放按钮点击
const handlePlayClick = () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  // 模拟loading 3秒后回到播放按钮
  setTimeout(() => {
    isLoading.value = false
  }, 20000)
}

// 检查URL参数和Auth0回调
onMounted(() => {
  console.log('CapGen页面加载')
  console.log('当前路由:', route.fullPath)
  console.log('URL参数:', route.query)
  
  // 检查是否有Auth0回调参数
  if (route.query.code && route.query.state) {
    console.log('检测到Auth0回调参数:', {
      code: route.query.code,
      state: route.query.state
    })
    console.log('等待Auth0 SDK处理回调...')
    
    // 不要手动清理URL，让Auth0 SDK处理
    // 添加一个监听器来检查何时URL被清理
    const originalUrl = window.location.href
    console.log('原始URL:', originalUrl)
    
    const checkUrlChange = setInterval(() => {
      if (window.location.href !== originalUrl) {
        console.log('URL已变化从:', originalUrl)
        console.log('变化到:', window.location.href)
        clearInterval(checkUrlChange)
      }
    }, 100)
    
    // 5秒后停止监控
    setTimeout(() => {
      clearInterval(checkUrlChange)
    }, 5000)
  }
})

// 设置页面元数据
useHead({
  title: 'CapGen - Professional AI Video Subtitle Generation & Translation | Little Grass',
  meta: [
    {
      name: 'description',
      content: 'CapGen is the leading AI-powered video subtitle generator and translator. Create professional subtitles in 30+ languages with 99% accuracy. Trusted by content creators, educators, and enterprises worldwide.'
    },
    {
      name: 'keywords',
      content: 'AI video subtitles, automatic subtitle generation, video translation, multilingual subtitles, speech to text, CapGen, Little Grass, professional subtitles'
    },
    {
      property: 'og:title',
      content: 'CapGen - Professional AI Video Subtitle Generation & Translation'
    },
    {
      property: 'og:description',
      content: 'Transform your videos into global content with AI-powered subtitles. 99% accuracy, 30+ languages, instant results.'
    },
    {
      property: 'og:type',
      content: 'website'
    }
  ]
})
</script> 

<style scoped>
@import '@/assets/styles/pages/capgen.css';
</style>