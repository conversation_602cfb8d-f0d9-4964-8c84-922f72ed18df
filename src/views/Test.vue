<template>
  <section class="hero-section">
    <div class="hero-background">
      <div class="blob-container">
        <svg class="blob blob-blue" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
            <defs>
            <radialGradient id="grad-blue" cx="30%" cy="30%" r="90%">
                <stop offset="0%" stop-color="#0066ff" stop-opacity="0.9"/>
                <stop offset="100%" stop-color="#0055dd" stop-opacity="0"/>
            </radialGradient>
            <!-- <filter id="blur-blue">
                <feGaussianBlur stdDeviation="50" />
            </filter> -->
            </defs>
            <path id="blob-blue" d="M421.5,306Q383,362,306,383Q229,404,167,356Q105,308,101,229Q97,150,167,102Q237,54,306,102Q375,150,421.5,229Q468,308,421.5,306Z" fill="url(#grad-blue)" filter="url(#blur-blue)" />
        </svg>

        <svg class="blob blob-purple" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
            <defs>
            <radialGradient id="grad-purple" cx="70%" cy="70%" r="70%">
                <stop offset="0%" stop-color="#B01AE0" stop-opacity="0.9"/>
                <stop offset="100%" stop-color="#8A5AD9" stop-opacity="0"/>
            </radialGradient>
            <!-- <filter id="blur-purple">
                <feGaussianBlur stdDeviation="50" />
            </filter> -->
            </defs>
            <path id="blob-purple" d="M178.5,306Q217,362,294,383Q371,404,433,356Q495,308,499,229Q503,150,433,102Q363,54,294,102Q225,150,178.5,229Q132,308,178.5,306Z" fill="url(#grad-purple)" filter="url(#blur-purple)" />
        </svg>
      </div>
      
      <!-- 添加波浪动画 -->
      <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
      </div>
    </div>
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">
          Built Light. <span class="hero-title-accent">Thinks Deep.</span> Moves Fast
        </h1>
        <p class="hero-subtitle">
          We build advanced technologies that simplify life and shape smarter everyday experiences
        </p>
        <div class="hero-cta">
          <button class="btn btn-primary btn-lg">Explore Products</button>
          <button class="btn btn-outline btn-lg">Our Technology</button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
  // 初始化波浪动画
  initWaveAnimation();
});

function initWaveAnimation() {
  // 设置波浪动画的初始状态
  document.documentElement.style.setProperty('--wave1', getWavePattern(0, 1));
  document.documentElement.style.setProperty('--wave2', getWavePattern(10, 1.5));
  document.documentElement.style.setProperty('--wave3', getWavePattern(20, 2));
  
  // 动态更新波浪动画
  let offset = 0;
  setInterval(() => {
    offset += 0.2;
    document.documentElement.style.setProperty('--wave1', getWavePattern(offset, 1));
    document.documentElement.style.setProperty('--wave2', getWavePattern(offset + 10, 1.5));
    document.documentElement.style.setProperty('--wave3', getWavePattern(offset + 20, 2));
  }, 50);
}

function getWavePattern(offset, amplitude) {
  let points = [];
  for (let i = 0; i <= 100; i += 5) {
    const x = i;
    const y = 50 + Math.sin((i / 100 * Math.PI * 2) + offset) * amplitude * 5;
    points.push(`${x}% ${y}%`);
  }
  points.push('100% 100%', '0% 100%');
  return `polygon(${points.join(', ')})`;
}
</script>

<style scoped>
.hero-section {
  position: relative;
  overflow: hidden;
  background: #0f0f1a;
  padding: 10rem 2rem;
  color: white;
}

.hero-background {
  position: absolute;
  inset: 0;
  z-index: 0;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 包裹两个 blob 的容器整体旋转 */
.blob-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: -1;
}

.blob {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1000px;
  height: 1000px;
  transform: translate(-50%, -50%);
  animation: rotateBlob 20s linear infinite;
  mix-blend-mode: screen;
  filter: blur(100px)
}

.blob-blue {
  animation-delay: 0s;
  left: 30%;
}

.blob-purple {
  animation-delay: 0s;
  left: 70%;
}

@keyframes rotateBlob {
  0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
  50% { transform: translate(-50%, -50%) rotate(180deg) scale(0.8); }
  100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 波浪动画样式 */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  overflow: hidden;
  z-index: 0;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(180deg, rgba(0, 102, 255, 0) 0%, rgba(0, 102, 255, 0.2) 100%);
  opacity: 0.6;
}

.wave1 {
  clip-path: var(--wave1);
  animation: wave1 10s linear infinite;
  z-index: 3;
}

.wave2 {
  clip-path: var(--wave2);
  animation: wave2 8s linear infinite;
  opacity: 0.4;
  z-index: 2;
}

.wave3 {
  clip-path: var(--wave3);
  animation: wave3 12s linear infinite;
  opacity: 0.2;
  z-index: 1;
}

@keyframes wave1 {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@keyframes wave2 {
  0% { transform: translateX(0); }
  100% { transform: translateX(50%); }
}

@keyframes wave3 {
  0% { transform: translateX(-50%); }
  100% { transform: translateX(0); }
}

/* 内容样式 */
.container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.8;
  margin-bottom: 2rem;
}

.hero-cta button {
  margin: 0 0.5rem;
}
</style>
