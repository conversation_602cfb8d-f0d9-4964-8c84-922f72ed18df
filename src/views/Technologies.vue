<template>
  <div class="technologies-page">
    <div class="top-background"></div>
    <section class="hero-section">
      <div class="container">
        <div class="text-center">
          <h1 class="hero-title">lightweight, efficient</h1>
          <p class="hero-subtitle">Smart technology that just works</p>
        </div>
      </div>
    </section>

    <!-- Technology Stack Section -->
    <section class="tech-stack">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Built on <span class="text-gradient">proven technologies</span>
          </h2>
          <p class="section-subtitle">We combine applied AI research with solid engineering experience.</p>
        </div>
        <div class="figma-stack-grid">
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 3H21V5H3V3ZM3 7H21V9H3V7ZM3 11H21V13H3V11ZM3 15H21V17H3V15ZM3 19H21V21H3V19ZM7 7V9H17V7H7ZM7 11V13H17V11H7ZM7 15V17H17V15H7Z" fill="currentColor"/>
                <circle cx="5" cy="8" r="1" fill="currentColor"/>
                <circle cx="5" cy="12" r="1" fill="currentColor"/>
                <circle cx="5" cy="16" r="1" fill="currentColor"/>
              </svg>
            </div>
            <span class="stack-text">Large Language Models</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
                <circle cx="12" cy="12" r="3" fill="currentColor"/>
                <path d="M12 1L15.09 6.26L21 7L15.09 7.74L12 13L8.91 7.74L3 7L8.91 6.26L12 1Z" fill="currentColor" opacity="0.6"/>
              </svg>
            </div>
            <span class="stack-text">Computer Vision</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
              </svg>
            </div>
            <span class="stack-text">Generative AI</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="4" cy="6" r="2" fill="currentColor"/>
                <circle cx="12" cy="6" r="2" fill="currentColor"/>
                <circle cx="20" cy="6" r="2" fill="currentColor"/>
                <circle cx="4" cy="12" r="2" fill="currentColor"/>
                <circle cx="12" cy="12" r="2" fill="currentColor"/>
                <circle cx="20" cy="12" r="2" fill="currentColor"/>
                <circle cx="4" cy="18" r="2" fill="currentColor"/>
                <circle cx="12" cy="18" r="2" fill="currentColor"/>
                <circle cx="20" cy="18" r="2" fill="currentColor"/>
                <path d="M6 6L10 12M14 6L18 12M6 12L10 18M14 12L18 18M6 6L10 6M14 6L18 6M6 12L10 12M14 12L18 12M6 18L10 18M14 18L18 18" stroke="currentColor" stroke-width="1" opacity="0.4"/>
              </svg>
            </div>
            <span class="stack-text">Deep Learning</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 3H22V5H2V3ZM2 7H22V9H2V7ZM2 11H22V13H2V11ZM2 15H22V17H2V15ZM2 19H22V21H2V19Z" fill="currentColor"/>
              </svg>
            </div>
            <span class="stack-text">Transformer Architecture</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="40" height="24" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M35.0409 13.5991C32.8009 13.5991 31.0409 15.3591 31.0409 17.5991C31.0409 19.1991 32.0009 20.6391 33.4409 21.2791V24.1591L29.1209 26.8791C28.6409 27.1991 28.3209 27.6791 28.3209 28.1591V35.1991C28.3209 36.1591 28.9609 36.7991 29.9209 36.7991C30.8809 36.7991 31.5209 36.1591 31.5209 35.1991V29.1191L35.8409 26.3991C36.3209 26.0791 36.6409 25.5991 36.6409 25.1191V21.2791C38.0809 20.6391 39.0409 19.1991 39.0409 17.5991C39.0409 15.3591 37.2809 13.5991 35.0409 13.5991ZM23.5209 7.19906C21.2809 7.19906 19.5209 8.95906 19.5209 11.1991C19.5209 12.7991 20.4809 14.2391 21.9209 14.8791V35.3591C21.9209 36.3191 22.5609 36.9591 23.5209 36.9591C24.4809 36.9591 25.1209 36.3191 25.1209 35.3591V14.8791C26.5609 14.2391 27.5209 12.7991 27.5209 11.1991C27.5209 8.95906 25.7609 7.19906 23.5209 7.19906ZM10.0809 26.7191H6.56094V22.8791C8.00094 22.2391 8.96094 20.7991 8.96094 19.1991C8.96094 16.9591 7.20094 15.1991 4.96094 15.1991C2.72094 15.1991 0.960938 16.9591 0.960938 19.1991C0.960938 20.7991 1.92094 22.2391 3.36094 22.8791V28.3191C3.36094 29.2791 4.00094 29.9191 4.96094 29.9191H8.48094V35.3591C8.48094 36.3191 9.12094 36.9591 10.0809 36.9591C11.0409 36.9591 11.6809 36.3191 11.6809 35.3591V28.3191C11.6809 27.3591 10.8809 26.7191 10.0809 26.7191ZM13.1209 15.5191V10.7191C14.5609 10.0791 15.5209 8.63906 15.5209 7.03906C15.5209 4.79906 13.7609 3.03906 11.5209 3.03906C9.28094 3.03906 7.52094 4.79906 7.52094 7.03906C7.52094 8.63906 8.48094 10.0791 9.92094 10.7191V16.1591C9.92094 16.6391 10.0809 16.9591 10.4009 17.2791L14.8809 21.5991V35.3591C14.8809 36.3191 15.5209 36.9591 16.4809 36.9591C17.4409 36.9591 18.0809 36.3191 18.0809 35.3591V20.9591C18.0809 20.4791 17.9209 20.1591 17.6009 19.8391L13.1209 15.5191Z" fill="white"/>
              </svg>

            </div>
            <span class="stack-text">Neural Networks</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg width="24" height="24" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M34.7208 1.60156H5.28078C2.88078 1.60156 0.800781 3.68156 0.800781 6.08156V27.3616C0.800781 29.9216 2.88078 31.8416 5.28078 31.8416H14.0808L13.2808 35.0416H9.76078C8.80078 35.0416 8.16078 35.6816 8.16078 36.6416C8.16078 37.6016 8.80078 38.2416 9.76078 38.2416H30.2408C31.2008 38.2416 31.8408 37.6016 31.8408 36.6416C31.8408 35.6816 31.2008 35.0416 30.2408 35.0416H26.8808L26.0808 31.8416H34.8808C37.2808 31.8416 39.3608 29.7616 39.3608 27.3616V6.08156C39.2008 3.68156 37.1208 1.60156 34.7208 1.60156ZM36.0008 24.0016H22.7208V18.2416H24.9608C25.6008 19.6816 26.8808 20.6416 28.4808 20.6416C30.5608 20.6416 32.3208 18.8816 32.3208 16.8016C32.3208 14.7216 30.7208 12.8016 28.4808 12.8016C27.0408 12.8016 25.7608 13.6016 25.1208 14.8816H21.1208C20.1608 14.8816 19.5208 15.5216 19.5208 16.4816V24.0016H4.00078V13.2816H8.32078C8.96078 14.7216 10.2408 15.6816 11.8408 15.6816C13.9208 15.6816 15.6808 13.9216 15.6808 11.8416C15.8408 9.76156 14.0808 8.00156 12.0008 8.00156C10.5608 8.00156 9.28078 8.96156 8.48078 10.0816H4.00078V6.08156C4.00078 5.44156 4.64078 4.80156 5.28078 4.80156H34.7208C35.3608 4.80156 36.0008 5.44156 36.0008 6.08156V24.0016Z" fill="white"/>
              </svg>

            </div>
            <span class="stack-text">Model Optimization</span>
          </div>
          <div class="stack-item">
            <div class="stack-icon">
              <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="m422-232 207-248H469l29-227-185 267h139l-30 208ZM320-80l40-280H160l360-520h80l-40 320h240L400-80h-80Zm151-390Z"/></svg>
            </div>
            <span class="stack-text">Real-time Inference</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Core Technologies Section -->
    <section class="core-technologies">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Our Tech Engine</h2>
          <p class="section-subtitle">These proprietary technologies power our products.</p>
        </div>
        <div class="figma-core-grid">
          <div class="figma-core-card">
            <div class="figma-core-card-title">LightCore</div>
            <div class="figma-core-card-tag">Lightweight Decision Engine</div>
            <div class="figma-core-card-desc">LightCore is our lightweight prediction engine powered by efficient
              machine learning models. It helps users make smarter decisions and simplifies daily workflows through
              accurate data analysis.</div>
            <ul class="figma-core-card-list">
              <li>Efficient Data Processing</li>
              <li>Smart Recommendations</li>
              <li>Personalized Experience</li>
            </ul>
          </div>
          <div class="figma-core-card">
            <div class="figma-core-card-title">DeepInsight</div>
            <div class="figma-core-card-tag">Deep Data Analytics Platform</div>
            <div class="figma-core-card-desc">DeepInsight enables reliable, high-performance data processing pipelines.
              It transforms complex data into valuable insights to support clear decision-making.</div>
            <ul class="figma-core-card-list">
              <li>Data Visualization</li>
              <li>Smart Data Structuring</li>
              <li>Insight Reports</li>
            </ul>
          </div>
          <div class="figma-core-card">
            <div class="figma-core-card-title">FastServe</div>
            <div class="figma-core-card-tag">Scalable AI Service Platform</div>
            <div class="figma-core-card-desc">FastServe delivers fast and stable AI services for our products. It
              dynamically allocates resources based on demand, ensuring smooth experiences across scenarios.</div>
            <ul class="figma-core-card-list">
              <li>Intelligent Resource Management</li>
              <li>Multi-Scenario Adaptability</li>
              <li>Fast Response</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Work Section -->
    <section class="ai-section">
      <div class="container">
        <div class="section-header">
            <h2 class="section-title">Technology <span class="text-gradient">for Everyday Life</span></h2>
            <p class="section-subtitle">Turning practical AI into real-world tools.</p>
        </div>
        <div class="figma-ai-grid">
          <!-- 第一行：图片在左，内容在右 -->
          <div class="figma-ai-row figma-ai-row-left">
            <div class="figma-ai-image-container">
              <img src="/images/technologies/image01.png" alt="Image Enhancement" />
            </div>
            <div class="figma-ai-content">
              <div class="figma-ai-card-title">Image Enhancement</div>
              <div class="figma-ai-card-desc">Transform everyday photos into sharper, high-quality images and bring your
                memories to life.</div>
              <div class="figma-ai-card-metrics">
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">4x</span><span
                    class="figma-ai-metric-label">Clarity Boost</span></div>
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">200ms</span><span
                    class="figma-ai-metric-label">Processing Time</span></div>
              </div>
            </div>
          </div>
          
          <!-- 第二行：内容在左，图片在右 -->
          <div class="figma-ai-row figma-ai-row-right">
            <div class="figma-ai-content">
              <div class="figma-ai-card-title">Image Creation</div>
              <div class="figma-ai-card-desc">Use AI to generate visuals and spark inspiration for your designs and
                creative work.</div>
              <div class="figma-ai-card-metrics">
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">HD</span><span
                    class="figma-ai-metric-label">Output Quality</span></div>
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">90%</span><span
                    class="figma-ai-metric-label">User Satisfaction</span></div>
              </div>
            </div>
            <div class="figma-ai-image-container">
              <img src="/images/technologies/image02.png" alt="Image Creation" />
            </div>
          </div>
          
          <!-- 第三行：图片在左，内容在右 -->
          <div class="figma-ai-row figma-ai-row-left">
            <div class="figma-ai-image-container">
              <img src="/images/technologies/image03.png" alt="Text Assistant" />
            </div>
            <div class="figma-ai-content">
              <div class="figma-ai-card-title">Text Assistant</div>
              <div class="figma-ai-card-desc">Use AI language tools to take notes, write drafts, and boost
                productivity—effortlessly.</div>
              <div class="figma-ai-card-metrics">
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">Multi-use</span><span
                    class="figma-ai-metric-label">Applications</span></div>
                <div class="figma-ai-metric"><span class="figma-ai-metric-value">30+</span><span
                    class="figma-ai-metric-label">Language Support</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Research Section -->
    <section class="research-section">
      <div class="container">
        <div class="research-left">
          <!-- <div class="figma-research-badge">Research & Practice</div> -->
          <h2 class="section-title">Using Tech to <span class="text-gradient">Improve Lives</span></h2>
          <p class="research-desc">We turn research into practical tools that address real human needs.</p>
          <ul class="research-list">
            <li>
              <span class="research-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 3L1 9L12 15L21 10.09V17H23V9M5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z" fill="currentColor"/>
                </svg>
              </span>
              <div>
                <div class="research-list-title">Academia Partnerships</div>
                <div class="research-list-desc">Collaborating with educational institutions to turn theory into
                  technology.</div>
              </div>
            </li>
            <li>
              <span class="research-icon">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M660-570q-25 0-42.5-17.5T600-630q0-25 17.5-42.5T660-690q25 0 42.5 17.5T720-630q0 25-17.5 42.5T660-570Zm-360 0q-25 0-42.5-17.5T240-630q0-25 17.5-42.5T300-690q25 0 42.5 17.5T360-630q0 25-17.5 42.5T300-570Zm180 110q-25 0-42.5-17.5T420-520q0-25 17.5-42.5T480-580q25 0 42.5 17.5T540-520q0 25-17.5 42.5T480-460Zm0-220q-25 0-42.5-17.5T420-740q0-25 17.5-42.5T480-800q25 0 42.5 17.5T540-740q0 25-17.5 42.5T480-680Zm0 520q-20 0-40.5-3t-39.5-8v-143q0-35 23.5-60.5T480-400q33 0 56.5 25.5T560-314v143q-19 5-39.5 8t-40.5 3Zm-140-32q-20-8-38.5-18T266-232q-28-20-44.5-52T205-352q0-26-5.5-48.5T180-443q-10-13-37.5-39.5T92-532q-11-11-11-28t11-28q11-11 28-11t28 11l153 145q20 18 29.5 42.5T340-350v158Zm280 0v-158q0-26 10-51t29-42l153-145q12-11 28.5-11t27.5 11q11 11 11 28t-11 28q-23 23-50.5 49T780-443q-14 20-19.5 42.5T755-352q0 36-16.5 68.5T693-231q-16 11-34.5 21T620-192Z"/></svg>
              </span>
              <div>
                <div class="research-list-title">Tech Innovation</div>
                <div class="research-list-desc">Constantly improving our core technologies to enhance user
                  experience.</div>
              </div>
            </li>
            <li>
              <span class="research-icon">
                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M40-160v-160q0-34 23.5-57t56.5-23h131q20 0 38 10t29 27q29 39 71.5 61t90.5 22q49 0 91.5-22t70.5-61q13-17 30.5-27t36.5-10h131q34 0 57 23t23 57v160H640v-91q-35 25-75.5 38T480-200q-43 0-84-13.5T320-252v92H40Zm440-160q-38 0-72-17.5T351-386q-17-25-42.5-39.5T253-440q22-37 93-58.5T480-520q63 0 134 21.5t93 58.5q-29 0-55 14.5T609-386q-22 32-56 49t-73 17ZM160-440q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T280-560q0 50-34.5 85T160-440Zm640 0q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T920-560q0 50-34.5 85T800-440ZM480-560q-50 0-85-35t-35-85q0-51 35-85.5t85-34.5q51 0 85.5 34.5T600-680q0 50-34.5 85T480-560Z"/></svg>
              </span>
              <div>
                <div class="research-list-title">Community Engagement</div>
                <div class="research-list-desc">Sharing our knowledge and tools through open community
                  participation.</div>
              </div>
            </li>
          </ul>
        </div>
        <!-- <div class="figma-research-right">
          <div class="figma-research-image"></div>
        </div> -->
      </div>
    </section>

    <section class="big-banner"></section>

    <!-- Future Tech Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">Technology for Humanity</h2>
            <p class="cta-subtitle">We explore new ways to integrate tech into daily life—creating tools that are
              useful, thoughtful, and easy to use. We believe the best technology simplifies complexity and improves
              quality of life.</p>
            <router-link to="/products">
              <button class="btn btn-cta btn-s">Explore Our Products</button>
            </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useHead } from '@vueuse/head'
import { onMounted } from 'vue'

// 设置页面元数据
useHead({
  title: 'Little Grass - Technologies',
  meta: [
    {
      name: 'description',
      content: 'Discover the cutting-edge technologies powering Little Grass AI solutions. Learn about our lightweight, efficient approach to artificial intelligence.'
    }
  ]
})

// 滚动触发动画
onMounted(() => {
  // 创建Intersection Observer实例
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      // 当元素进入视口时
      if (entry.isIntersecting) {
        // 添加动画类
        entry.target.classList.add('animate');
        // 停止观察该元素
        observer.unobserve(entry.target);
      }
    });
  }, {
    // 设置元素进入视口20%时触发
    threshold: 0.2
  });

  // 观察所有需要动画的元素
  const animatedElements = document.querySelectorAll('.section-title, .stack-item, .figma-core-card, .figma-ai-row, .figma-research-list li, .cta-title, .cta-subtitle');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
});
</script>

<style scoped>
/* Hero Section */
.top-background {
  background-image: url('/images/bg_technologies.svg');
  animation: pulse 0.8s ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.4; }
  /* 50% { opacity: 0.5; } */
  100% { opacity: 1; }
}

/* Technology Stack Section */
.tech-stack {
  padding: 100px 0;
  text-align: center;
}
/* Core Technologies Section */
.core-technologies {
  padding: 120px 0;
  /* background-color: #181818; */
}

.tech-card {
  background-color: #222222;
  border-radius: 16px;
  padding: 40px 30px;
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.tech-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 60px;
  background: linear-gradient(180deg, #cdee2d 0%, #9be669 100%);
  border-radius: 4px 0 0 4px;
}

.tech-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(205, 238, 45, 0.2);
}

.tech-icon {
  font-size: 2.5rem;
  color: #cdee2d;
  margin-bottom: 1.5rem;
}

.tech-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.8;
  margin-bottom: 1.5rem;
}

/* AI Work Section */
.ai-section {
  padding: 120px 0;
}

.big-banner {
  background:url('/images/technologies/banner.png') center/cover;
  height: 700px;
}


/* Research Section */
.research-section {
  padding: 120px 0;
  /* background:url('/images/technologies/bg_bottom.png') center/cover; */
}

.research-list {
  list-style: none;
  padding: 0;
  margin: 2rem 0 0 0;
}

.research-list li {
  display: flex;
  margin-bottom: 2rem;
  align-items: flex-start;
}


.research-content h4 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.research-content p {
  opacity: 0.8;
  margin: 0;
}

.research-image {
  height: 400px;
  background-color: #222;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

/* Future Tech Section */
.future-tech {
  padding: 140px 0;
  
  position: relative;
  /* height: 1026px; */
}

.section-description {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.8;
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}
/* Responsive Adjustments */

@media (max-width: 768px) {
  
  
  .tech-card, .ai-card {
    margin-bottom: 30px;
  }
  
  .tech-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .research-list li {
    flex-direction: column;
  }
  
  .research-icon {
    margin-bottom: 1rem;
    margin-right: 0;
  }
}

/* Figma Technology Stack Section */

.figma-stack-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}
.stack-item {
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 32px 24px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: left;
  gap: 16px;
  transition: all 0.3s ease;
  min-height: 140px;
  justify-content: left;
}
/* .stack-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(205, 238, 45, 0.3);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
} */
.stack-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}
.stack-icon svg {
  width: 24px;
  height: 24px;
}
.stack-text {
  padding-top: 20px; 
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.4;
  text-align: left;
}
@media (max-width: 1200px) {
  .figma-stack-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 768px) {
  .figma-stack-grid { 
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  .stack-item {
    padding: 24px 16px;
    min-height: 120px;
  }
  .stack-icon {
    width: 40px;
    height: 40px;
  }
  .stack-text {
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  .figma-stack-grid { 
    grid-template-columns: 1fr;
  }
}

/* Figma Core Technologies Section */
.figma-core-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}
.figma-core-card {
  border-radius: 30px;
  border: 2px solid rgba(153,190,255,0.15);
  box-shadow: 0 -2px 0 0 #99BEFF;
  padding: 48px 36px 36px 36px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 420px;
}
.figma-core-card-title {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
}
.figma-core-card-tag {
  font-size: 20px;
  color: #99beff;
  font-weight: 300;
  margin-bottom: 18px;
}
.figma-core-card-desc {
  font-size: 16px;
  color: #fff;
  opacity: 0.8;
  margin-bottom: 24px;
  line-height: 1.6;
}
.figma-core-card-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}
.figma-core-card-list li {
  font-size: 16px;
  color: #fff;
  opacity: 0.8;
  padding-left: 18px;
  position: relative;
  margin-bottom: 10px;
}
.figma-core-card-list li:before {
  content: '•';
  color: #99beff;
  position: absolute;
  left: 0;
  top: 0;
}
@media (max-width: 1200px) {
  .figma-core-grid { grid-template-columns: 1fr; }
}

/* Figma AI Work Section */
.figma-ai-grid {
  margin-top: 120px;
  display: flex;
  flex-direction: column;
  gap: 200px;
}
.figma-ai-row {
  display: flex;
  align-items: center;
  gap: 60px;
}
.figma-ai-row-left {
  flex-direction: row;
}
.figma-ai-row-right {
  /* flex-direction: row-reverse; */
}
.figma-ai-image-container {
  flex: 0 0 60%;
  overflow: hidden;
}
.figma-ai-image-container img {
  width: 100%;
  height: auto;
  display: block;
}
.figma-ai-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.figma-ai-card-title {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16px;
}
.figma-ai-card-desc {
  font-size: 18px;
  color: #fff;
  opacity: 0.8;
  margin-bottom: 32px;
  line-height: 1.6;
}
.figma-ai-card-metrics {
  display: flex;
  gap: 40px;
}
.figma-ai-metric {
  text-align: left;
}
.figma-ai-metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #99beff;
  display: block;
  margin-bottom: 4px;
}
.figma-ai-metric-label {
  font-size: 14px;
  color: #fff;
  opacity: 0.7;
}
@media (max-width: 1200px) {
  .figma-ai-row {
    flex-direction: column !important;
    gap: 40px;
  }
  .figma-ai-image-container {
    flex: none;
    width: 100%;
  }
  .figma-ai-card-title {
    font-size: 28px;
  }
  .figma-ai-card-desc {
    font-size: 16px;
  }
}
@media (max-width: 768px) {
  .figma-ai-grid {
    gap: 60px;
  }
  .figma-ai-row {
    gap: 30px;
  }
  .figma-ai-card-title {
    font-size: 24px;
  }
  .figma-ai-card-metrics {
    gap: 20px;
  }
}

/* Figma Research Section */
.research-left {
  flex: 1;
}
.research-desc {
  font-size: 26px;
  font-weight: 300;
  opacity: 0.8;
  margin-bottom: 48px;
  text-align: center;
}
.research-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}
.research-list li {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: 32px;
}
.research-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid gray;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.research-list-title {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
}
.research-list-desc {
  font-size: 16px;
  color: #fff;
  opacity: 0.8;
}

/* 动画样式 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为标题添加动画 */
.hero-title {
  animation: fadeInUp 0.8s ease-out forwards;
}

.hero-subtitle {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

/* 为章节标题添加动画 - 滚动触发 */
.section-title {
  opacity: 0;
}

.section-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 为技术栈项目添加动画 - 滚动触发 */
.stack-item {
  opacity: 0;
}

.stack-item.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.figma-stack-grid .stack-item.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.figma-stack-grid .stack-item.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.figma-stack-grid .stack-item.animate:nth-child(3) {
  animation-delay: 0.3s;
}

.figma-stack-grid .stack-item.animate:nth-child(4) {
  animation-delay: 0.4s;
}

.figma-stack-grid .stack-item.animate:nth-child(5) {
  animation-delay: 0.5s;
}

.figma-stack-grid .stack-item.animate:nth-child(6) {
  animation-delay: 0.6s;
}

.figma-stack-grid .stack-item.animate:nth-child(7) {
  animation-delay: 0.7s;
}

.figma-stack-grid .stack-item.animate:nth-child(8) {
  animation-delay: 0.8s;
}

/* 为核心技术卡片添加动画 - 滚动触发 */
.figma-core-card {
  opacity: 0;
}

.figma-core-card.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.figma-core-grid .figma-core-card.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.figma-core-grid .figma-core-card.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.figma-core-grid .figma-core-card.animate:nth-child(3) {
  animation-delay: 0.3s;
}

/* 为AI工作行添加动画 - 滚动触发 */
.figma-ai-row {
  opacity: 0;
}

.figma-ai-row.animate.figma-ai-row-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.figma-ai-row.animate.figma-ai-row-right {
  animation: slideInRight 0.8s ease-out forwards;
}

/* 为研究列表项添加动画 - 滚动触发 */
.figma-research-list li {
  opacity: 0;
}

.figma-research-list li.animate {
  animation: fadeInUp 0.6s ease-out forwards;
}

.figma-research-list li.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.figma-research-list li.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.figma-research-list li.animate:nth-child(3) {
  animation-delay: 0.3s;
}

/* 为CTA部分添加动画 - 滚动触发 */
.cta-title {
  opacity: 0;
}

.cta-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

.cta-subtitle {
  opacity: 0;
}

.cta-subtitle.animate {
  animation: fadeInUp 0.8s ease-out 0.1s forwards;
}
</style>