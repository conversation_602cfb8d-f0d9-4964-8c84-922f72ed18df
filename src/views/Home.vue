<template>
  <main class="home-container">
    <!-- Hero Section -->
    <div class="hero-background">
      <div class="blob-container">
        <svg class="blob blob-blue" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
          <image href="/images/home/<USER>" width="100%" height="100%" />
        </svg>

        <svg class="blob blob-purple" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
          <image href="/images/home/<USER>" width="100%" height="100%" />
        </svg>
      </div>
    </div>
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Built Light. <span class="hero-title-accent">Thinks Deep.</span> Moves Fast</h1>
          <p class="hero-subtitle">AI-powered tools that simplify complex tasks and transform how you work with visual content, language, and data</p>
          <div class="hero-cta">
            <router-link to="/products" class="btn-link">
              <button class="btn btn-primary btn-m">Explore Products</button>
            </router-link>
            <!-- <router-link to="/technologies" class="btn-link">
              <button class="btn btn-secondary btn-m">Our Technology</button>
            </router-link> -->
          </div>
        </div>
      </div>
    </section>

    <!-- Crafted with Care Section -->
    <section class="crafted-section">
      <div class="">
        <div class="section-header">
          <h2 class="section-title text-gradient">Crafted with Care</h2>
          <p class="section-subtitle">We strive to make each product visually appealing, simple to use, and technically powerful</p>
        </div>
        
        <!-- 产品展示卡片 -->
        <div class="product-showcase">
          <div class="showcase-card">
            <div class="showcase-image">
              <img src="/images/home/<USER>" alt="Image Enhancement">
              <p class="showcase-title">Image Enhancement</p>
              <p class="showcase-description">We strive to make each product visually appealing, simple to use, and technically powerful</p>
            </div>
          </div>
          
          <div class="showcase-card">
              <div class="showcase-image">
                <img src="/images/home/<USER>" alt="Intelligent Translation">
                <p class="showcase-title">Intelligent Translation</p>
                <p class="showcase-description">We strive to make each product visually appealing, simple to use, and technically powerful</p>
              </div>
          </div>
          
          <div class="showcase-card">
            <div class="showcase-image">
              <img src="/images/home/<USER>" alt="Smart Assistant">
              <p class="showcase-title">Smart Assistant</p>
              <p class="showcase-description">We strive to make each product visually appealing, simple to use, and technically powerful</p>
            </div>
          </div>
        </div>

        <div class="image-card-grid">
          <!-- Statistics Card -->
          <div class="bento-card stats-card">
            <div class="stats-content">
              <div class="big-number">1,000,000<span class="plus-sign">+</span></div>
              <p>Comic and manga translated</p>
            </div>
            <div class="photo-stack" ref="photoStack">
              <div class="comic-animation">
                <img 
                  v-for="(comic, index) in comicImages" 
                  :key="index"
                  :src="comic.src" 
                  :alt="`Comic ${index + 1}`"
                  class="comic-image"
                  :class="{ 'comic-visible': visibleComics > index }"
                  :style="getComicStyle(comic, index)"
                />
              </div>
            </div>
          </div>

          <!-- No Imagination Card -->
          <div class="bento-card no-imagination">
            <img src="/images/home/<USER>" alt="No Imagination Beyond Your Creation">
            <div class="card-content">
              <h3>No Imagination<br>Beyond Your Creation</h3>
            </div>
          </div>
        </div>

        <div class="bento-grid-container">
        <div class="bento-grid">
          <!-- Left Column Container -->
          <div class="bento-column left-column">
            <!-- Team Meeting Card -->
            <div class="bento-card team-meeting">
              <img src="/images/home/<USER>" alt="Team Meeting">
            </div>
            <div class="bento-card team-description">
              <div class="card-content">
                <h3>"6 stars." <br> <span class="team-divider"> —— </span> For real.</h3>
                <div class="avatar-group">
                  <div class="avatar"></div>
                  <div class="avatar"></div>
                  <div class="avatar"></div>
                  <div class="avatar"></div>
                  <div class="avatar"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI Technology Card -->
          <div class="bento-card ai-technology">
            <div class="tech-content">
              <h3>Turning practical AI into<br>real-world tools.</h3>
              <img src="/images/home/<USER>" alt="AI Technology Visualization">
            </div>
          </div>

          <!-- AI Chat Card -->
          <div class="bento-card ai-chat" ref="aiChatCard">
            <div class="chat-header">
              <span class="chat-time">Today 09:17</span>
            </div>
            <div class="chat-messages">
              <div 
                class="message user-message" 
                :class="{ 'message-visible': visibleMessages >= 1 }"
              >
                Our upcoming product launch event is missing a keynote speaker. The original speaker canceled last minute, and we need someone with expertise in sustainable tech. Any ideas?
              </div>
              
              <!-- AI Loading Animation -->
              <div 
                v-if="showAiLoading"
                class="message ai-message ai-loading"
              >
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              
              <!-- AI Response -->
              <div 
                v-if="visibleMessages >= 2"
                class="message ai-message" 
                :class="{ 'message-visible': visibleMessages >= 2 }"
              >
                <span class="ai-typed-text">{{ aiTypingText }}</span>
              </div>
              
              <div 
                class="message user-message short" 
                :class="{ 'message-visible': visibleMessages >= 3 }"
              >
                Nora Li sounds perfect!
              </div>
              
              <!-- AI Final Loading Animation -->
              <div 
                v-if="showAiFinalLoading"
                class="message ai-message ai-loading"
              >
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              
              <!-- AI Final Response -->
              <div 
                v-if="visibleMessages >= 4"
                class="message ai-message" 
                :class="{ 'message-visible': visibleMessages >= 4 }"
              >
                <span class="ai-typed-text">{{ aiFinalTypingText }}</span>
              </div>
            </div>
            <div class="ai-assistant-label">
              <h4>Intelligent AI Assistant</h4>
            </div>
          </div>
        </div>
      </div>
      </div>
    </section>
    <section class="our-team-section">
      <div class="container">
        <div class="our-team-content">
          <h2 class="section-title">The Secret Sauce: <br> Our Team</h2>
          <p>We're obsessive about creating a place where people who are talented, hard-working, and full of team spirit can thrive</p>
        </div>

      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" style="display: none;">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Our AI-Powered Tools</h2>
          <p class="section-subtitle">Professional-grade AI tools designed to streamline your workflow and boost productivity</p>
        </div>
        <div class="products-grid">
          <div class="product-card" data-product="imtrans">
            <div class="product-image">
              <img src="/images/home/<USER>" alt="ImTrans - AI Image Translation">
            </div>
            <div class="product-content">
              <h3 class="product-title">ImTrans</h3>
              <p class="product-description">
                Instantly translate text in images across 100+ languages while preserving original formatting and design.
              </p>
              <ul class="product-features">
                <li>Real-time image text translation</li>
                <li>Preserves original layout & fonts</li>
                <li>Batch processing support</li>
                <li>API integration available</li>
              </ul>
              <div class="product-actions">
                <router-link to="/imtrans" class="btn btn-primary btn-sm">Try Now</router-link>
                <a href="#" class="product-link">Learn More →</a>
              </div>
            </div>
          </div>
          
          <div class="product-card" data-product="capgen">
            <div class="product-image">
              <img src="/images/home/<USER>" alt="CapGen - Video Subtitle Generator">
            </div>
            <div class="product-content">
              <h3 class="product-title">CapGen</h3>
              <p class="product-description">
                Automatically generate accurate subtitles for videos with AI-powered speech recognition and translation.
              </p>
              <ul class="product-features">
                <li>Automatic subtitle extraction</li>
                <li>Multi-language translation</li>
                <li>Perfect synchronization</li>
                <li>Multiple export formats</li>
              </ul>
              <div class="product-actions">
                <router-link to="/capgen" class="btn btn-primary btn-sm">Try Now</router-link>
                <router-link to="/capgen/pricing" class="product-link">View Pricing →</router-link>
              </div>
            </div>
          </div>
          
          <div class="product-card coming-soon" data-product="ai-assistant">
            <div class="product-image">
              <img src="/images/home/<USER>" alt="Smart AI Assistant">
            </div>
            <div class="product-content">
              <h3 class="product-title">Smart AI Assistant</h3>
              <p class="product-description">
                Your intelligent companion for content creation, data analysis, and task automation.
              </p>
              <ul class="product-features">
                <li>Natural language processing</li>
                <li>Content generation & editing</li>
                <li>Data insights & analysis</li>
                <li>Workflow automation</li>
              </ul>
              <div class="product-actions">
                <button class="btn btn-disabled btn-sm" disabled>Coming Soon</button>
                <a href="#" class="product-link">Join Waitlist →</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" style="display: none;">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Why Choose Little Grass</h2>
          <p class="section-subtitle">Built for professionals who demand reliability, accuracy, and efficiency</p>
        </div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">Enterprise-Grade Accuracy</h3>
            <p class="feature-description">Our AI models achieve 99%+ accuracy rates, trusted by Fortune 500 companies worldwide.</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">Lightning Fast Processing</h3>
            <p class="feature-description">Process images and videos in seconds, not minutes. Our optimized infrastructure ensures rapid results.</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22S6 16 6 10A6 6 0 0 1 18 10C18 16 12 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 10A4 4 0 0 1 16 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">Privacy & Security First</h3>
            <p class="feature-description">Your data is encrypted and processed securely. We never store or share your content without permission.</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">Seamless API Integration</h3>
            <p class="feature-description">Developer-friendly APIs with comprehensive documentation for easy integration into your existing workflow.</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 21V19A2 2 0 0 0 15 17H9A2 2 0 0 0 7 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">24/7 Expert Support</h3>
            <p class="feature-description">Get help when you need it with our dedicated support team and comprehensive knowledge base.</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 16V8A2 2 0 0 0 19 6H5A2 2 0 0 0 3 8V16A2 2 0 0 0 5 18H19A2 2 0 0 0 21 16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17 2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="feature-title">Flexible Pricing</h3>
            <p class="feature-description">From free plans to enterprise solutions, we offer pricing that scales with your needs and usage.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Section -->
    <section class="technology-section" style="display: none;">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Cutting-Edge AI Technology</h2>
          <p class="section-subtitle">Our proprietary AI models and infrastructure that power next-generation tools</p>
        </div>
        <div class="tech-showcase">
          <div class="tech-features">
            <div class="tech-feature">
              <div class="tech-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 16V8A2 2 0 0 0 19 6H5A2 2 0 0 0 3 8V16A2 2 0 0 0 5 18H19A2 2 0 0 0 21 16Z" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 12L8 8L12 4L16 8L12 12Z" fill="currentColor"/>
                </svg>
              </div>
              <div class="feature-content">
                <h3>Deep Learning Networks</h3>
                <p>Advanced neural architectures with continuous learning capabilities, trained on billions of data points for superior accuracy.</p>
              </div>
            </div>
            <div class="tech-feature">
              <div class="tech-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M2 12L22 12" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 2A15.3 15.3 0 0 1 16 12A15.3 15.3 0 0 1 12 22A15.3 15.3 0 0 1 8 12A15.3 15.3 0 0 1 12 2Z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <div class="feature-content">
                <h3>Computer Vision</h3>
                <p>State-of-the-art image recognition and processing that understands context, layout, and visual semantics with pixel-perfect precision.</p>
              </div>
            </div>
            <div class="tech-feature">
              <div class="tech-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                  <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                  <path d="M16 13H8" stroke="currentColor" stroke-width="2"/>
                  <path d="M16 17H8" stroke="currentColor" stroke-width="2"/>
                  <path d="M10 9H8" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <div class="feature-content">
                <h3>Natural Language Processing</h3>
                <p>Advanced language models that understand context, sentiment, and cultural nuances across 100+ languages for perfect translations.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="doing-well-section">
      <div class="">
        <div class="section-title doing-well-title">Doing Well and Doing Good</div>
        <div class="doing-well-content">
          <div class="doing-well-item">
            <h3>Education Support</h3>
            <p>We provide scholarships to groups traditionally underrepresented in STEM, including women in computer science and students with disabilities</p>
          </div>
          
          <div class="doing-well-divider">
          </div>
          
          <div class="doing-well-item">
            <h3>Environmental Responsibility</h3>
            <p>We partner with environmental organizations to offset 100% of our greenhouse gas emissions, committed to sustainable development</p>
          </div>
          
          <div class="doing-well-divider">
          </div>
          
          <div class="doing-well-item">
            <h3>Social Contribution</h3>
            <p>We actively participate in social impact projects, using our technological capabilities to solve societal challenges</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">Ready to explore the infinite possibilities of AI with us?</h2>
          <p class="cta-subtitle">Join us and start your journey to the future of AI</p>
          <router-link to="/career" class="btn-link">
            <button class="btn btn-cta btn-s">Join Us</button>
          </router-link>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useHead } from '@vueuse/head'

// Set page metadata
useHead({
  title: 'Little Grass - AI-Powered Tools for Modern Workflows',
  meta: [
    {
      name: 'description',
      content: 'Professional AI tools for image translation, video subtitles, and content processing. Trusted by thousands of professionals worldwide.'
    },
    {
      name: 'keywords',
      content: 'AI tools, image translation, video subtitles, artificial intelligence, workflow automation, professional tools'
    }
  ]
})

// AI Chat animation state
const visibleMessages = ref(0)
const aiChatCard = ref(null)
const showAiLoading = ref(false)
const aiTypingText = ref('')
const showAiFinalLoading = ref(false)
const aiFinalTypingText = ref('')
const hasAnimationPlayed = ref(false)

// Comic animation state
const visibleComics = ref(0)
const photoStack = ref(null)
const hasComicAnimationPlayed = ref(false)

let observer = null
let comicObserver = null
let animationTimeouts = []
let comicAnimationTimeouts = []

const aiFullText = "I've analyzed your event theme and attendee profile. Here are 3 suitable replacements:\n1. Dr. Emily Park (MIT, specializes in eco-friendly materials) – available next week\n2. Prof. Carlos Mendez (wrote 'Green Tech Futures') – high audience engagement score\n3. Startup CEO Nora Li – adds diversity to your panel\n\nShall I draft outreach emails tailored to each?"

const aiFinalText = "May your meeting go well."

// Function to get comic style with proper transform handling
const getComicStyle = (comic, index) => {
  const baseStyle = { ...comic.style }
  
  // If comic is not visible yet, apply the slide-up transform
  if (visibleComics.value <= index) {
    // Keep the original transform but add translateY for slide effect
    const originalTransform = baseStyle.transform || ''
    baseStyle.transform = `translateY(200px) ${originalTransform}`
  }
  
  return baseStyle
}

// Comic images configuration with fan-shaped positioning (two layers)
const comicImages = [
  {
    src: '/images/home/<USER>',
    style: {
      position: 'absolute',
      bottom: '16%',
      left: '10%',
      width: '45%',
      zIndex: 2,
      transform: 'rotate(0deg)'
    }
  },
  {
    src: '/images/home/<USER>',
    style: {
      position: 'absolute',
      bottom: '35%',
      left: '20%',
      width: '45%',
      zIndex: 3,
      transform: 'rotate(0deg)'
    }
  },
  {
    src: '/images/home/<USER>',
    style: {
      position: 'absolute',
      bottom: '40%',
      left: '68%',
      width: '45%',
      zIndex: 4,
      transform: 'translateX(-50%) rotate(0deg)'
    }
  },
  {
    src: '/images/home/<USER>',
    style: {
      position: 'absolute',
      bottom: '13%',
      right: '25%',
      width: '45%',
      zIndex: 5,
      transform: 'rotate(0deg)'
    }
  },
  {
    src: '/images/home/<USER>',
    style: {
      position: 'absolute',
      bottom: '14%',
      right: '4%',
      width: '45%',
      zIndex: 6,
      transform: 'rotate(0deg)'
    }
  }
]

// Clear all animation timeouts
const clearAnimationTimeouts = () => {
  animationTimeouts.forEach(timeout => clearTimeout(timeout))
  animationTimeouts = []
}

const clearComicAnimationTimeouts = () => {
  comicAnimationTimeouts.forEach(timeout => clearTimeout(timeout))
  comicAnimationTimeouts = []
}

// Start comic animation
const startComicAnimation = () => {
  if (hasComicAnimationPlayed.value) return
  
  clearComicAnimationTimeouts()
  visibleComics.value = 0
  
  // Show comics one by one from left to right
  for (let i = 0; i < comicImages.length; i++) {
    const timeout = setTimeout(() => {
      visibleComics.value = i + 1
    }, i * 250 + 300) // Optimized timing
    comicAnimationTimeouts.push(timeout)
  }
  
  hasComicAnimationPlayed.value = true
}



// Typing animation for AI message
const typeAiMessage = () => {
  aiTypingText.value = ''
  let currentIndex = 0
  
  const typeNextChar = () => {
    if (currentIndex < aiFullText.length) {
      const char = aiFullText[currentIndex]
      aiTypingText.value += char
      currentIndex++
      
      // Variable typing speed for more natural effect
      let delay = 25
      if (char === '\n') delay = 100 // Pause at line breaks
      else if (char === '.' || char === '?' || char === '!') delay = 200 // Pause at sentence ends
      else if (char === ',') delay = 50 // Short pause at commas
      
      const timeout = setTimeout(typeNextChar, delay)
      animationTimeouts.push(timeout)
    } else {
      // AI message typing complete, show final user message
      const timeout = setTimeout(() => {
        visibleMessages.value = 3
        
        // After user message, show AI final loading and response
        const timeout2 = setTimeout(() => {
          showAiFinalLoading.value = true
          
          const timeout3 = setTimeout(() => {
            showAiFinalLoading.value = false
            visibleMessages.value = 4 // Show final AI message
            typeAiFinalMessage()
          }, 1000) // Show loading for 1 second
          animationTimeouts.push(timeout3)
        }, 1500) // Wait 1.5 seconds after user message
        animationTimeouts.push(timeout2)
      }, 1000) // Wait a bit before showing final message
      animationTimeouts.push(timeout)
    }
  }
  
  typeNextChar()
}

// Typing animation for final AI message
const typeAiFinalMessage = () => {
  aiFinalTypingText.value = ''
  let currentIndex = 0
  
  const typeNextChar = () => {
    if (currentIndex < aiFinalText.length) {
      const char = aiFinalText[currentIndex]
      aiFinalTypingText.value += char
      currentIndex++
      
      let delay = 40 // Slightly slower for the final message
      if (char === '.' || char === '?' || char === '!') delay = 300
      
      const timeout = setTimeout(typeNextChar, delay)
      animationTimeouts.push(timeout)
    }
  }
  
  typeNextChar()
}

// Start chat animation (only once)
const startChatAnimation = () => {
  if (hasAnimationPlayed.value) return
  
  // Clear any existing timeouts
  clearAnimationTimeouts()
  
  // Reset animation state
  visibleMessages.value = 0
  showAiLoading.value = false
  aiTypingText.value = ''
  showAiFinalLoading.value = false
  aiFinalTypingText.value = ''
  
  // Start animation sequence
  const timeout1 = setTimeout(() => {
    visibleMessages.value = 1 // Show first user message
    
    const timeout2 = setTimeout(() => {
      showAiLoading.value = true // Show AI loading
      
      const timeout3 = setTimeout(() => {
        showAiLoading.value = false
        visibleMessages.value = 2 // Show AI message container
        typeAiMessage() // Start typing animation
      }, 1500) // Show loading for 1.5 seconds
      animationTimeouts.push(timeout3)
    }, 1000) // Wait 1 second before AI loading
    animationTimeouts.push(timeout2)
  }, 500) // Initial delay
  animationTimeouts.push(timeout1)
  
  hasAnimationPlayed.value = true
}

// Setup intersection observer to trigger animation when card is visible
onMounted(() => {
  // Reset animation state on mount
  hasAnimationPlayed.value = false
  visibleMessages.value = 0
  showAiLoading.value = false
  aiTypingText.value = ''
  showAiFinalLoading.value = false
  aiFinalTypingText.value = ''
  
  // Reset comic animation state
  hasComicAnimationPlayed.value = false
  visibleComics.value = 0
  

  
  // Setup AI chat observer
  if (aiChatCard.value) {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimationPlayed.value) {
            startChatAnimation()
          }
        })
      },
      {
        threshold: 0.3 // Trigger when 30% of the card is visible
      }
    )
    
    observer.observe(aiChatCard.value)
  }
  
  // Setup comic animation observer
  if (photoStack.value) {
    comicObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasComicAnimationPlayed.value) {
            startComicAnimation()
          }
        })
      },
      {
        threshold: 0.3, // Trigger when 30% of the element is visible
        rootMargin: '-25% 0px -25% 0px' // Only trigger when element is well into the viewport
      }
    )
    
    comicObserver.observe(photoStack.value)
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
  if (comicObserver) {
    comicObserver.disconnect()
  }
  clearAnimationTimeouts()
  clearComicAnimationTimeouts()
})
</script>

<style scoped>
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1100px;
  /* overflow: hidden; */
  z-index: 0;
}

/* Blob 动画 */
/* 包裹两个 blob 的容器整体旋转 */
.blob-container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: -1;
}

.blob {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 900px;
  transform: translate(-50%, -50%);
  animation: rotateBlob 12s linear infinite reverse;
  mix-blend-mode: screen;
  /* filter: blur(100px) */
}

.blob-blue {
  animation-delay: 0s;
  left: 50%;
  top: 35%;
}

.blob-purple {
  animation-delay: 0s;
  left: 50%;
  top: 45%;
}

@keyframes rotateBlob {
  0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
  50% { transform: translate(-50%, -50%) rotate(180deg) scale(1); }
  100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* General Styles */
section {
  padding: 6rem 0;
  position: relative;
}

/* Hero Section */
.hero-content {
  position: relative;
  z-index: 2;
  margin: 0 auto;
  text-align: center;
}

.hero-title {
  font-weight: 800;
  line-height: 1;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #FFFFFF 0%, #E0E0E0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title-accent {
  background: linear-gradient(90deg, #98F8FF 0%, #5E8BFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  font-weight: 400;
  font-size: 1.25rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
}

.btn-link {
  text-decoration: none;
}

/* Trust indicators */
.trust-indicators {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.indicator {
  text-align: center;
}

.indicator-number {
  display: block;
  font-size: 2rem;
  font-weight: 900;
  color: #98F8FF;
  margin-bottom: 0.5rem;
}

.indicator-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Products Section */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.product-card {
  background: linear-gradient(145deg, rgba(30, 30, 30, 0.8), rgba(20, 20, 20, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(55, 114, 255, 0.3);
}

.product-card.coming-soon {
  opacity: 0.8;
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-content {
  padding: 2rem;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #FFFFFF;
}

.product-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.product-features {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.product-features li {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.product-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #98F8FF;
  font-weight: bold;
}

.product-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.product-link {
  color: #98F8FF;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.product-link:hover {
  color: #FFFFFF;
}

/* Features Section */
.features-section {
  background: linear-gradient(135deg, rgba(10, 10, 10, 0.9), rgba(20, 20, 20, 0.8));
  padding: 8rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background: rgba(30, 30, 30, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: rgba(55, 114, 255, 0.3);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 1.5rem;
  color: #98F8FF;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #FFFFFF;
}

.feature-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Technology Section */
.tech-showcase {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.tech-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

.tech-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1rem;
}

.tech-icon {
  width: 60px;
  height: 60px;
  color: #98F8FF;
  margin-bottom: 1rem;
}

.feature-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #FFFFFF;
}

.feature-content p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-size: 1rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

/* Crafted with Care Section */
.crafted-section {
  padding: 0;
}

/* Product Showcase */
.product-showcase {
  display: flex;
  flex-direction: column;
  gap: 6rem;
  margin: 6rem 0px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.showcase-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s ease;
  background: transparent;
}

/* 第二张卡片靠右对齐 */
.showcase-card:nth-child(2) *{
  float: right;
}

.showcase-image {
  margin-bottom: 2rem;
  padding-left: 20px;
  padding-right: 20px;
  display: block;
  width: fit-content;
}

.showcase-image img {
  width: auto;
  height: auto;
  max-width: 80%;
  object-fit: contain;
  display: block;
}
.showcase-title {
  width: 80%;
  font-size: 1.8rem;
  margin-top: 1.6rem;
  font-weight: 200;
}
.showcase-description {
  width: 80%;
  font-size: 1rem;
  color: #9e9e9e;
}

.image-card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  background: linear-gradient(to bottom, #000 50%, #fff 50%);
  padding: 60px 40px 0px 40px;
}

.bento-grid-container {
  padding: 0px 40px 100px 40px;
  background: #fff;
}

.bento-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 14px;
  padding-top: 60px;
  align-items: stretch;
  min-height: 600px;
}

.bento-column {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.left-column {
  grid-column: 1 / 2;
}

.bento-card {
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  height: auto;
  min-height: auto;
}

/* No Imagination Card */
.no-imagination {
  grid-column: 2 / 3;
}

.no-imagination img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.no-imagination .card-content {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  z-index: 2;
  color: white;
}

.no-imagination h3 {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1.2;
}

.team-description {
  background: #Eeeeee;
  color: #080808;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 150px;
}
.team-divider {
  padding-left: 40px;
  font-weight: 200;
  /* font-size: 1rem; */
  /* vertical-align: middle; */
}

/* AI Chat Card */
.ai-chat {
  grid-column: 3 / 4;
  background: white;
  border: 1px solid #e0e0e0;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.chat-header {
  margin-bottom: 2rem;
}

.chat-time {
  font-size: 0.75rem;
  color: rgba(8, 8, 8, 0.4);
  font-weight: 500;
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  padding: 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  line-height: 1.4;
  font-weight: 200;
  max-width: 85%;
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.user-message {
  background: #6D8DFF;
  color: white;
  margin-left: auto;
}

.user-message.short {
  max-width: 60%;
}

.ai-message {
  background: #f4f4f4;
  color: #080808;
  margin-right: auto;
}

.ai-message.message-visible {
  animation: aiMessageAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes aiMessageAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    background: #f8f8f8;
  }
  50% {
    background: #f0f0f0;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    background: #f4f4f4;
  }
}

/* AI Loading Animation */
.ai-loading {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
  animation: aiLoadingAppear 0.5s ease-out;
}

@keyframes aiLoadingAppear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(8, 8, 8, 0.4);
  animation: loadingDot 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* AI Typed Text */
.ai-typed-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* AI Technology Card */
.ai-technology {
  grid-column: 2 / 3;
  display: flex;
  flex-direction: column;
}
.tech-content {
  position: relative;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.tech-content h3 {
  position: absolute;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  line-height: 1.3;
  width: 100%;
  text-align: center;
  margin-top: 30px;
}

.tech-content img {
  width: 100%;
  flex: 1;
  object-fit: cover;
  min-height: 300px;
}

/* Statistics Card */
.stats-card {
  grid-column: 1 / 2;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.stats-content {
 position: absolute;
  bottom: 0;
  color: #080808;
  left: 15px;
}

.big-number {
  font-size: 3rem;
  font-weight: 600;
  color: #080808;
  line-height: 0.96;
  margin-bottom: 0.5rem;
}

.plus-sign {
  font-size: 2rem;
  vertical-align: top;
}

.stats-content p {
  font-size: 0.9rem;
  max-width: 200px;
  line-height: 1.4;
  
}

/* Comic animation container */
.photo-stack {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 70%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.comic-animation {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.comic-image {
  opacity: 1;
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-width: none;
  height: auto;
  object-fit: contain;
  will-change: transform;
}

/* Add hover effects for comics */
.comic-image:hover {
  transform: translateY(-8px) scale(1.08);
  z-index: 10 !important;
  transition: all 0.3s ease;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

/* Team Meeting Card */
.team-meeting {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.team-meeting img {
  width: 100%;
  flex: 1;
  object-fit: cover;
  min-height: 200px;
}

.team-meeting .card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

.team-meeting h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.avatar-group {
  display: flex;
  gap: -1rem;
  /* margin-left: -1rem; */
  justify-content: right;
  margin-top: 1rem;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #ddd;
  border: 2px solid white;
  margin-left: -0.5rem;
}

.avatar:nth-child(1) { 
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e); 
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
}
.avatar:nth-child(2) { 
  background: linear-gradient(45deg, #4ecdc4, #44a08d); 
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
}
.avatar:nth-child(3) { 
  background: linear-gradient(45deg, #45b7d1, #96c93d); 
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
}
.avatar:nth-child(4) { 
  background: linear-gradient(45deg, #f093fb, #f5576c); 
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
}
.avatar:nth-child(5) { 
  background: #ebebeb; 
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
}

/* AI Assistant Label */
.ai-assistant-label {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.ai-assistant-label h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #080808;
  text-align: center;
  margin: 0;
}

.our-team-section {
  background-image: url('/images/home/<USER>');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 100vh;
  min-height: 800px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.our-team-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
.our-team-content p {
  text-align: center;
  max-width: 700px;
  line-height: 1.5;
  font-size: 32px;
  font-weight: 200;
}

/* Doing Well Section */
.doing-well-section {
  background: #ffffff;
  padding: 8rem 0;
  height: 90vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.doing-well-title {
  color: #111112;
  letter-spacing: -4%;
  font-weight: 400;
}

.doing-well-content {
  display: flex;
  justify-content: top;
  align-items: top;
  gap: 30px;
  /* max-width: 1500px; */
  margin-top: 100px;
  /* height: 200px; */
}

.doing-well-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
  flex: 1;
}

.doing-well-item h3 {
  font-weight: 700;
  font-size: 26px;
  line-height: 1.8;
  letter-spacing: -4%;
  text-align: center;
  color: #111112;
  margin: 0;
}

.doing-well-item p {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.65;
  letter-spacing: -4%;
  text-align: center;
  color: #111112;
  opacity: 0.8;
  width: 360px;
  margin: 0;
}

.doing-well-divider {
  /* height: 260px; */
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #111112;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .stats-card {
    grid-column: 1 / 2;
  }
  .big-number {
    font-size: 2rem;
  }
  .no-imagination .card-content h3{
    /* padding: 1rem; */
    font-size: 1.5rem;
  }

  .bento-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    align-items: stretch;
  }
  .left-column {
    grid-column: 1 / 2;
  }
  .team-meeting {
    min-height: 250px;
  }
  .team-description {
    min-height: 120px;
  }
  .ai-technology {
    grid-column: 2 / 3;
    min-height: 400px;
  }
  .ai-chat {
    grid-column: 1 / 3;
    min-height: 350px;
  }
  
  .product-showcase {
    gap: 3rem;
    margin: 2rem 20px;
  }
  .doing-well-section {
    padding: 4rem 0;
    min-height: 900px;
  }
  
  .doing-well-title {
    font-size: 48px;
  }
  
  .doing-well-content {
    flex-direction: column;
    gap: 3rem;
  }
  
  .doing-well-divider {
    display: none;
  }
  
  .doing-well-item p {
    width: auto;
    max-width: 600px;
  }
}

@media (max-width: 800px) {
  .crafted-section .section-header{
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
  .product-showcase {
    margin-top: 2em;
    margin-bottom: 120px;
    margin-left: 0px;
    margin-right: 0px;
    gap: 2rem;
  }
  
  .showcase-card {
    margin: 0 1rem;
    align-items: flex-start !important;
  }
  .showcase-image {
    padding-left: 0px;
    padding-right: 0px;
  }

  .showcase-image img {
    width: auto;
    height: auto;
    max-width: 100%;
    object-fit: contain;
    display: block;
    padding-left: 5px;
    padding-right: 5px;
  }
  .showcase-card:nth-child(2) *{
    float: left;
  }

  .image-card-grid {
    display: flex;
    flex-direction: column;
    /* background: linear-gradient(to bottom, #000 25%, #fff 75%); */
    background: #fff;
    /* padding: 60px 20px 0px 20px; */
    padding:0;
    width: 100%;
  }
  .stats-card {
    order: 1;
    display: flex;
    flex-direction: column;
    min-height: 350px;
    padding: 1.5rem;
    position: relative;
    z-index: 1;
    background: linear-gradient(to bottom, #000 50%, #fff 50%);
    padding: 0 20px;
    border-radius:0;
  }
  .no-imagination {
    order: 2;
    position: relative;
    z-index: 1;
    padding: 0 20px;
  }
  .big-number {
    color: #000;
    bottom: 0;
    left: 0;
  }
  .stats-content {
    color: #000;
    top: 60%;
    left: 40px;
  }
  
  .photo-stack {
    width: 60%;
    /* height: 80%; */
  }
  
  .comic-image {
    max-width: 100%;
    height: auto;
    max-height: 100%;
  }
  
  .comic-image:hover {
    transform: translateY(-3px) scale(1.03);
    filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.2));
  }
  
  .bento-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 20px;
  }
  .bento-grid-container {
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 60px;
  }
  .bento-card {
    width: 100%;
    /* height: auto; */
    /* min-height: auto; */
  }
  .bento-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .left-column {
    order: 1;
    width: 100%;
  }
  .team-meeting {
    aspect-ratio: 4 / 3;
    min-height: 200px;
  }
  .team-description {
    min-height: 120px;
  }
  .ai-technology {
    order: 2;
    max-height: 400px;
    min-height: 300px;
    overflow: hidden;
  }
  .ai-technology .tech-content {
    height: 100%;
  }
  .ai-technology .tech-content img {
    object-fit: cover;
  }
  .ai-chat {
    order: 3;
    min-height: 350px;
  }
  
  .no-imagination,
  .ai-chat {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: auto;
    align-items: stretch;
  }
  .no-imagination img,
  .ai-chat img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
    flex-shrink: 0;
  }
  .team-meeting {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: auto;
    align-items: stretch;
  }
  .team-meeting img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    flex-shrink: 0;
  }
  
  .ai-chat {
    padding: 1.5rem;
  }
  
  .message {
    font-size: 0.9rem;
  }
  
  .loading-dots span {
    width: 6px;
    height: 6px;
  }
  
  .big-number {
    font-size: 2.5rem;
  }
  
  .team-meeting h3 {
    font-size: 1.3rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .products-grid, .tech-features, .features-grid {
    grid-template-columns: 1fr;
  }
  
  .product-card {
    margin: 0 1rem;
  }
  
  .product-title, .feature-content h3 {
    font-size: 1.3rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .doing-well-title {
    font-size: 40px;
    margin-bottom: 3rem;
  }
  
  .doing-well-item h3 {
    font-size: 24px;
  }
  
  .doing-well-item p {
    font-size: 18px;
  }
  .our-team-content p {
    font-size: 26px;
  }
  .our-team-content h2 {
    font-size: 42px;
  }
}

@media (max-width: 480px) {
  .no-imagination .card-content,
  .team-meeting .card-content {
    bottom: 1rem;
    left: 1rem;
    padding: 1rem;
  }
  .no-imagination h3,
  .team-meeting h3 {
    font-size: 1.4rem;
  }
  .hero-subtitle {
    font-size: 1.1rem;
  }
  .big-number {
    font-size: 1.5rem;
  }
  .indicator-number {
    font-size: 1.5rem;
  }
  .product-content {
    padding: 1.5rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .cta-title {
    font-size: 1.5rem;
  }

  .doing-well-title {
    font-size: 30px;
  }
  
  .doing-well-item h3 {
    font-size: 20px;
  }
  
  .doing-well-item p {
    font-size: 18px;
  }
  .our-team-content p {
    font-size: 22px;
  }
  .our-team-content h2 {
    font-size: 30px;
  }
}
</style>