<template>
  <div class="about-page">
    <!-- 顶部横幅 -->
    <div class="hero-section">
      <div class="container" style="height: 100%; display: flex; align-items: center; justify-content: center;">
        <p class="intro-text gradient-text">Creating Infinite Possibilities with AI</p>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="container content-section">
      <!-- 公司简介 -->
      <div class="intro-section">
        <p class="intro-text">
          Welcome to Little Grass, where intelligence meets innovation. Established in 2024, we are a pioneering AI technology startup dedicated to harnessing the power of artificial intelligence to solve real-world problems and drive meaningful impact. Our mission is to create intelligent solutions that empower businesses, enhance user experiences, and pave the way for a smarter, more connected future.
        </p>
      </div>

      <!-- 卡片式内容展示 -->
      <div class="cards-container">
        <!-- 愿景卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="vision-icon"></i>
          </div>
          <h2 class="card-title">Our Vision</h2>
          <p class="card-content">
            At Little Grass, we envision a world where AI is seamlessly integrated into every facet of life, making processes more efficient, decisions more informed, and experiences more personalized. We aspire to be the trusted partner for organizations seeking to leverage AI to unlock new potential, optimize operations, and stay ahead of the curve in an increasingly digital world.
          </p>
        </div>

        <!-- 专长卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="expertise-icon"></i>
          </div>
          <h2 class="card-title">Our Expertise</h2>
          <p class="card-content">
            Our team of AI enthusiasts, data scientists, and software engineers specializes in developing cutting-edge solutions across a range of domains, including:
          </p>
          <ul class="expertise-list">
            <li><span class="highlight">Machine Learning & Deep Learning</span>: Crafting algorithms that learn and adapt, enabling predictive analytics, automation, and personalized recommendations.</li>
            <li><span class="highlight">Natural Language Processing (NLP)</span>: Bridging the gap between humans and machines through conversational interfaces, sentiment analysis, and language understanding.</li>
            <li><span class="highlight">Computer Vision</span>: Enabling machines to "see" and interpret visual data, transforming industries like healthcare, retail, and security.</li>
            <li><span class="highlight">AI-Driven Automation</span>: Streamlining workflows and enhancing productivity through intelligent automation and robotics.</li>
          </ul>
        </div>

        <!-- 方法卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="approach-icon"></i>
          </div>
          <h2 class="card-title">Our Approach</h2>
          <p class="card-content">
            We take a human-centered approach to AI, ensuring that our solutions not only deliver on technical prowess but also align with ethical standards and societal values. We prioritize collaboration, working closely with our clients to understand their unique challenges and tailor AI solutions that meet their specific needs.
          </p>
        </div>

        <!-- 创新与研发卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="innovation-icon"></i>
          </div>
          <h2 class="card-title">Our Innovation</h2>
          <p class="card-content">
            Innovation is at the heart of everything we do at Little Grass. Our dedicated research and development team constantly explores emerging technologies and methodologies to push the boundaries of what's possible with AI. We invest significantly in R&D to develop proprietary algorithms and frameworks that give our solutions a competitive edge while addressing complex challenges in novel ways.
          </p>
        </div>

        <!-- 社会责任卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="responsibility-icon"></i>
          </div>
          <h2 class="card-title">Our Responsibility</h2>
          <p class="card-content">
            We believe that with great technology comes great responsibility. At Little Grass, we are committed to developing AI that benefits humanity and the planet. We adhere to strict ethical guidelines in our development process, prioritize data privacy and security, and actively work to reduce algorithmic bias. Through educational initiatives and community partnerships, we strive to make AI more accessible and ensure its benefits are widely shared.
          </p>
        </div>

        <!-- 历程卡片 -->
        <div class="info-card">
          <div class="card-icon">
            <i class="journey-icon"></i>
          </div>
          <h2 class="card-title">Our Journey</h2>
          <p class="card-content">
            From our inception as a small team with a big dream, we have grown steadily, earning recognition for our innovative solutions and commitment to excellence. Our journey has been fueled by a passion for AI and a relentless pursuit of knowledge, driving us to constantly evolve and push the boundaries of what's possible.
          </p>
        </div>
      </div>

      <!-- 团队展示 -->
      <div class="team-section">
        <h2 class="section-title">Our Team</h2>
        <p class="section-subtitle">A passionate group of AI experts dedicated to creating technology that changes the world</p>
        <div class="team-grid">
          <!-- 团队成员占位 - 实际使用时可替换为真实团队信息 -->
          <div class="team-placeholder">
            <div class="team-icon">👩‍💻</div>
            <p>AI Researcher</p>
          </div>
          <div class="team-placeholder">
            <div class="team-icon">👨‍💻</div>
            <p>Software Engineer</p>
          </div>
          <div class="team-placeholder">
            <div class="team-icon">🧠</div>
            <p>Data Scientist</p>
          </div>
          <div class="team-placeholder">
            <div class="team-icon">🎨</div>
            <p>UI/UX Designer</p>
          </div>
        </div>
      </div>

      <!-- 加入我们 -->
      <div class="join-section">
        <h2 class="section-title">Join Us</h2>
        <p class="join-text">
          We invite you to join us on this exciting journey of exploration and discovery. Whether you're a business looking to harness the power of AI, a talented individual seeking to contribute to our mission, or simply someone interested in the future of AI, we welcome your engagement.
        </p>
        <div class="cta-container">
          <a href="/contact" class="cta-button">Contact Us</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useHead } from '@vueuse/head'

// 设置页面元数据
useHead({
  title: 'Little Grass - About Us',
  meta: [
    {
      name: 'description',
      content: 'Learn how Little Grass leverages AI technology to create innovative solutions, empower businesses, enhance user experiences, and pave the way for a smarter future.'
    }
  ]
})
</script>

<style scoped>
/* 整体页面样式 */
.about-page {
  background-color: #111;
  color: #eee;
  font-family: 'Inter', sans-serif;
}

/* 顶部横幅 */
.hero-section {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: white;
  padding: 0;
  text-align: center;
  margin-top: 92px; /* 添加与header高度相同的上边距 */
  margin-bottom: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  max-width: 700px;
  margin: 0 auto;
}

/* 内容区域 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-section {
  padding-bottom: 80px;
}

/* 简介部分 */
.intro-section {
  margin-bottom: 60px;
  text-align: center;
}

.intro-text {
  font-size: 1.25rem;
  line-height: 1.8;
  color: #ccc;
  max-width: 900px;
  margin: 0 auto;
}

.gradient-text {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
  padding: 0;
  margin: 0;
  animation: shimmer 3s infinite alternate;
}

@keyframes shimmer {
  0% {
    background-position: left;
    text-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  }
  100% {
    background-position: right;
    text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.4);
  }
}

/* 卡片容器 */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 80px;
}

/* 信息卡片 */
.info-card {
  background: #222;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.card-icon {
  margin-bottom: 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon i {
  width: 50px;
  height: 50px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.vision-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
}

.expertise-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M12 3L1 9l11 6 9-4.91V17h2V9L12 3z M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82z"/></svg>');
}

.approach-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/></svg>');
}

.journey-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M13.5 5.5c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zM9.8 8.9L7 23h2.1l1.8-8 2.1 2v6h2v-7.5l-2.1-2 .6-3C14.8 12 16.8 13 19 13v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1-.3 0-.5.1-.8.1L6 8.3V13h2V9.6l1.8-.7"/></svg>');
}

.innovation-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>');
}

.responsibility-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23aaa"><path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm1-11h-2v3H8v2h3v3h2v-3h3v-2h-3z"/></svg>');
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #fff;
}

.card-content {
  font-size: 1rem;
  line-height: 1.6;
  color: #aaa;
}

/* 专长列表 */
.expertise-list {
  margin-top: 15px;
  padding-left: 20px;
}

.expertise-list li {
  margin-bottom: 10px;
  position: relative;
  color: #aaa;
}

.highlight {
  color: #ccc;
  font-weight: 600;
}

/* 团队部分 */
.team-section {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #fff;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #aaa;
  max-width: 700px;
  margin: 0 auto 40px;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.team-placeholder {
  background-color: #222;
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.team-placeholder:hover {
  transform: translateY(-5px);
}

.team-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

/* 加入我们部分 */
.join-section {
  text-align: center;
  background: #222;
  border-radius: 12px;
  padding: 60px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.join-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #aaa;
  max-width: 800px;
  margin: 0 auto 30px;
}

.cta-container {
  margin-top: 30px;
}

.cta-button {
  display: inline-block;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 15px 40px;
  border-radius: 50px;
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
  /* .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  } */
  
  .section-title {
    font-size: 2rem;
  }
  
  .cards-container {
    grid-template-columns: 1fr;
  }
  
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  /* .hero-title {
    font-size: 2rem;
  } */
  
  .team-grid {
    grid-template-columns: 1fr;
  }
}
</style>