<template>
  <div class="career-page">
    <div class="top-background"></div>
    <!-- 顶部横幅 -->
    <div class="hero-section">
      <div class="container text-center">
        <h1 class="hero-title">
          Join Our Team
        </h1>
        <p class="hero-subtitle">
          Shape the future of AI
        </p>
      </div>
    </div>

    <!-- 主要内容区 -->
      <section class="intro-section">
        <div class="container">
          <p class="section-subtitle">
            We're building the next generation of AI solutions that transform how people interact with
            technology. We're looking for passionate, innovative individuals who are excited about pushing the
            boundaries
            of what's possible with artificial intelligence. Join our diverse team and help us create technology that
            makes a difference.
          </p>
        </div>
      </section>

      <!-- 我们的文化 -->
      <section class="culture-section">
        <div class="container">
        <h2 class="section-title text-gradient">Our Culture</h2>
        <div class="culture-grid">
          <div class="culture-item">
            <div class="culture-icon">
              <img src="@/assets/images/icon/icon_rocket.svg" alt="Innovation" />
            </div>
            <h3>Innovation</h3>
            <p>We encourage bold ideas and creative thinking to solve complex problems.</p>
          </div>
          <div class="culture-item">
            <div class="culture-icon">
              <img src="@/assets/images/icon/icon_connect.svg" alt="Collaboration" />
            </div>
            <h3>Collaboration</h3>
            <p>We believe great achievements come from working together across disciplines.</p>
          </div>
          <div class="culture-item">
            <div class="culture-icon">
              <img src="@/assets/images/icon/icon_mountain.svg" alt="Growth" />
            </div>
            <h3>Growth</h3>
            <p>We invest in continuous learning and professional development.</p>
          </div>
          <div class="culture-item">
            <div class="culture-icon">
              <img src="@/assets/images/icon/icon_group.svg" alt="Impact" />
            </div>
            <h3>Impact</h3>
            <p>We're committed to creating technology that positively impacts the world.</p>
          </div>
        </div>
        </div>
      </section>

      <!-- 职位列表 -->
      <div class="jobs-section">
        <div class="container">
        <h2 class="section-title text-gradient">Open Positions</h2>

        <!-- AI技术类职位 -->
        <div class="job-category">
          <h3 class="category-title">AI Technology</h3>

          <div class="job-card">
            <h4 class="job-title">Senior Machine Learning Engineer</h4>
            <div class="job-meta">
              <span class="job-badge">Singapore</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              We're looking for an experienced Machine Learning Engineer to join our AI research team. You'll be
              responsible for designing, implementing, and optimizing machine learning models for our core products.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>5+ years of experience in machine learning or deep learning</li>
                <li>Strong programming skills in Python and familiarity with ML frameworks (TensorFlow, PyTorch)</li>
                <li>Experience with NLP, computer vision, or recommendation systems</li>
                <li>Master's or PhD in Computer Science, AI, or related field</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>

          </div>

          <div class="job-card">
            <h4 class="job-title">AI Research Scientist</h4>
            <div class="job-meta">
              <span class="job-badge">Remote</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              Join our research team to advance the state-of-the-art in artificial intelligence. You'll conduct original
              research, publish papers, and translate research findings into practical applications.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>PhD in Computer Science, Machine Learning, or related field</li>
                <li>Publication record in top-tier AI conferences (NeurIPS, ICML, ICLR, etc.)</li>
                <li>Experience implementing and scaling complex ML systems</li>
                <li>Strong mathematical background in statistics and optimization</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>
          </div>
        </div>

        <!-- 产品类职位 -->
        <div class="job-category">
          <h3 class="category-title">Product</h3>

          <div class="job-card">
            <h4 class="job-title">AI Product Manager</h4>
            <div class="job-meta">
              <span class="job-badge">Singapore</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              We're seeking a Product Manager with AI expertise to help define and execute our product vision. You'll
              work closely with engineering, design, and business teams to create innovative AI-powered solutions.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>3+ years of product management experience, preferably with AI products</li>
                <li>Strong understanding of machine learning concepts and applications</li>
                <li>Excellent communication and stakeholder management skills</li>
                <li>Experience with agile development methodologies</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>
          </div>

          <div class="job-card">
            <h4 class="job-title">UX/UI Designer for AI Products</h4>
            <div class="job-meta">
              <span class="job-badge">Singapore</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              Help us create intuitive and engaging user experiences for our AI products. You'll design interfaces that
              make complex AI capabilities accessible and valuable to users.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>3+ years of UX/UI design experience</li>
                <li>Portfolio demonstrating strong visual design skills and user-centered approach</li>
                <li>Experience with design tools (Figma, Sketch, Adobe XD)</li>
                <li>Understanding of AI/ML concepts and their UX implications</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>
          </div>
        </div>

        <!-- 市场与运营类职位 -->
        <div class="job-category">
          <h3 class="category-title">Marketing & Operations</h3>

          <div class="job-card">
            <h4 class="job-title">AI Marketing Specialist</h4>
            <div class="job-meta">
              <span class="job-badge">Hong Kong</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              Join our marketing team to help communicate the value of our AI solutions to the world. You'll develop
              compelling content, campaigns, and strategies to reach our target audiences.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>3+ years of B2B marketing experience, preferably in tech or AI</li>
                <li>Strong content creation and storytelling abilities</li>
                <li>Experience with digital marketing channels and analytics</li>
                <li>Ability to explain complex technical concepts to diverse audiences</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>
          </div>

          <div class="job-card">
            <h4 class="job-title">AI Community Manager</h4>
            <div class="job-meta">
              <span class="job-badge">Remote</span>
              <span class="job-badge">Full-time</span>
            </div>
            <p class="job-description">
              Build and nurture our community of AI enthusiasts, developers, and users. You'll create engagement
              strategies, manage social media, and organize events to foster a vibrant ecosystem around our products.
            </p>
            <div class="job-requirements">
              <h5>Requirements:</h5>
              <ul>
                <li>2+ years of community management or developer relations experience</li>
                <li>Strong communication and interpersonal skills</li>
                <li>Experience with community platforms and social media management</li>
                <li>Passion for AI technology and its applications</li>
              </ul>
            </div>
            <a href="#" class="apply-button">Apply Now</a>
          </div>
        </div>
      </div>

      <section class="big-banner"></section>

      <!-- 福利待遇 -->
      <div class="benefits-section">
        <div class="container">
        <h2 class="section-title text-gradient">Benefits & Perks</h2>
        <div class="benefits-grid">
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_ticket.svg" alt="Competitive Compensation" />
            </div>
            <h3>Competitive Compensation</h3>
            <p>Salary packages that recognize your skills and experience, plus equity options.</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_protect.svg" alt="Comprehensive Healthcare" />
            </div>
            <h3>Comprehensive Healthcare</h3>
            <p>Medical, dental, and vision coverage for you and your dependents.</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_book.svg" alt="Learning & Development" />
            </div>
            <h3>Learning & Development</h3>
            <p>Budget for conferences, courses, and learning resources.</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_clock.svg" alt="Flexible Work" />
            </div>
            <h3>Flexible Work</h3>
            <p>Flexible hours and remote work options to support your lifestyle.</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_rocket.svg" alt="Generous PTO" />
            </div>
            <h3>Generous PTO</h3>
            <p>Paid time off, holidays, and parental leave policies.</p>
          </div>
          <div class="benefit-item">
            <div class="benefit-icon">
              <img src="@/assets/images/icon/icon_game.svg" alt="Fun Workspace" />
            </div>
            <h3>Fun Workspace</h3>
              <p>Modern offices with recreational areas and regular team events.</p>
            </div>
          </div>
        </div>
        </div>
      </div>

      <!-- 应聘流程 -->
      <div class="process-section">
        <div class="container">
        <h2 class="section-title text-gradient">Our Hiring Process</h2>
        <div class="process-steps">
          <div class="process-step">
            <div class="step-number">1</div>
            <h3>Application Review</h3>
            <p>Our team reviews your resume and application materials.</p>
          </div>
          <div class="process-step">
            <div class="step-number">2</div>
            <h3>Initial Interview</h3>
            <p>A 30-minute call to discuss your background and the role.</p>
          </div>
          <div class="process-step">
            <div class="step-number">3</div>
            <h3>Technical Assessment</h3>
            <p>A take-home assignment or technical interview relevant to the position.</p>
          </div>
          <div class="process-step">
            <div class="step-number">4</div>
            <h3>Team Interviews</h3>
            <p>Meet with potential teammates and cross-functional partners.</p>
          </div>
          <div class="process-step">
            <div class="step-number">5</div>
            <h3>Final Decision</h3>
            <p>We make an offer to the selected candidate.</p>
          </div>
        </div>
        </div>
      </div>

      <!-- 联系我们 -->
      <div class="cta-section">
        <div class="container">
          <h2 class="cta-title">Don't See a Perfect Fit?</h2>
          <p class="cta-subtitle">
          We're always looking for talented individuals to join our team. If you don't see a position that matches your
          skills, send us your resume and tell us how you can contribute to Little Grass.
        </p>
        <router-link to="/contact">
          <button class="btn btn-cta btn-s">Contact Our Recruiting Team</button>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useHead } from '@vueuse/head'
import { onMounted } from 'vue'

// 设置页面元数据
useHead({
  title: 'Little Grass - Careers',
  meta: [
    {
      name: 'description',
      content: 'Join our team at Little Grass and help shape the future of AI. Explore exciting career opportunities in AI technology, product development, marketing, and operations.'
    }
  ]
})

// 滚动触发动画
onMounted(() => {
  // 创建Intersection Observer实例
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      // 当元素进入视口时
      if (entry.isIntersecting) {
        // 添加动画类
        entry.target.classList.add('animate');
        // 停止观察该元素
        observer.unobserve(entry.target);
      }
    });
  }, {
    // 设置元素进入视口20%时触发
    threshold: 0.2
  });

  // 观察所有需要动画的元素
  const animatedElements = document.querySelectorAll('.section-title, .culture-item, .job-card, .benefit-item, .process-step, .cta-title, .cta-subtitle');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
});
</script>

<style scoped>
/* 顶部横幅 */
.top-background {
  background-image: url("/images/bg_career2.png");
  color: #fff;
}
@keyframes shimmer {
  0% {
    background-position: 0% 50%;
    text-shadow: 0px 0px 20px rgba(79, 172, 254, 0.3);
  }
  50% {
    background-position: 100% 50%;
    text-shadow: 0px 0px 30px rgba(0, 242, 254, 0.5);
  }
  100% {
    background-position: 0% 50%;
    text-shadow: 0px 0px 20px rgba(79, 172, 254, 0.3);
  }
}

/* 简介部分 */
.intro-section {
  margin: 80px auto;
  text-align: center;
  max-width: 900px;
  color: #fff;
}

/* 文化部分 */
.culture-section {
  /* margin: 200px auto; */
  text-align: center;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80vh;
  min-height: 600px;
}
.culture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
  margin-bottom: 80px;
  margin-top: 100px;
}

.culture-item {
  /* background-color: #1a1a1a; */
  border-radius: 30px;
  padding: 30px 30px 20px 30px;
  text-align: left;
  transition: transform 0.3s ease;
  border: 2px solid #333;
}

.culture-icon {
  font-size: 2.5rem;
  margin-bottom: 50px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  /* background-color: #222; */
  border: 2px solid #333;
}

.culture-item h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #fff;
}

.culture-item p {
  color: #aaa;
  line-height: 1.5;
}

/* 职位部分 */
.job-category {
  margin-bottom: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-title {
  font-size: 1.8rem;
  color: #4facfe;
  margin-bottom: 30px;
  /* border-bottom: 1px solid #333; */
  padding-bottom: 10px;
  text-align: left;
  width: 79%;
}

.job-card {
  background: linear-gradient(180deg, rgba(42,42,54,0.5) 100%, rgba(31,28,28,0.5) 100%);
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  transition: transform 0.3s ease;
  width: 80%;
  display: flex;
  flex-direction: column;
}
.job-badge {
  background-color: rgba(153,190,255,0.20);
  margin-top: 10px;
  padding: 5px 10px;
  border-radius: 10px;
  color: #99BEFF; 
}

.job-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  color: #aaa;
  font-size: 0.9rem;
}

.job-description {
  color: #fff;
  margin-bottom: 20px;
  line-height: 25px;
  font-weight: 200;
  font-size: 16px;
}

.job-requirements h5 {
  color: #fff;
  margin-bottom: 10px;
  font-size: 22px;
  /* font-weight: 200; */
}

.job-requirements ul {
  padding-left: 20px;
  margin-bottom: 25px;
  color: #fff;
}

.job-requirements li {
  margin-bottom: 8px;
  line-height: 30px;
  font-weight: 200;
  list-style: disc;
}

.apply-button {
  align-self: flex-end;
  display: inline-block;
  background-color: rgba(255,255,255,0.10);
  color: #fff;
  padding: 12px 24px;
  border-radius: 30px;
  text-transform: uppercase;
  font-weight: bold;
  transition: all 0.3s ease;
  font-size: 16px;
}

.apply-button:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4); */
}
.big-banner {
  background-image: url("/images/career/banner.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 600px;
  width: 100%;
  margin-top: 100px;
  margin-bottom: 100px;
}

/* 福利部分 */
.benefits-section {
  margin: 100px auto;
  min-height: 600px;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  /* margin-bottom: 80px; */
}

.benefit-item {
  /* background-color: #1a1a1a; */
  border-radius: 30px;
  padding: 25px 25px 20px 25px;
  height: 300px;
  text-align: center;
  transition: transform 0.3s ease;
  border: 2px solid #333;
  text-align: left;
}

.benefit-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px solid #333;
}

.benefit-item h3 {
  font-size: 22px;
  margin-top: 50px;
  margin-bottom: 20px;
  color: #fff;
  font-weight: 200;
}

.benefit-item p {
  color: #aaa;
  line-height: 1.5;
}
.process-section {
  /* margin: 100px auto; */
  /* text-align: center; */
  width: 100%;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("/images/career/bottom_bg.svg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 800px;
  width: 100%;
  margin-top: 100px;
  margin-bottom: 100px;
}

/* 流程部分 */
.process-steps {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.process-step {
  flex: 1;
  min-width: 200px;
  text-align: center;
  padding: 20px;
  position: relative;
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
}

.process-step h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: #fff;
}

.process-step p {
  color: #aaa;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .process-steps {
    flex-direction: column;
  }
  
  .process-step {
    margin-bottom: 30px;
  }
  
  .culture-grid,
  .benefits-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画样式 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 为标题添加动画 */
.hero-title {
  animation: fadeInUp 0.8s ease-out forwards;
}

.hero-subtitle {
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
  opacity: 0;
}

/* 为章节标题添加动画 - 滚动触发 */
.section-title {
  opacity: 0;
}

.section-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 为文化项目添加动画 - 滚动触发 */
.culture-item {
  opacity: 0;
}

.culture-item.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.culture-grid .culture-item.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.culture-grid .culture-item.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.culture-grid .culture-item.animate:nth-child(3) {
  animation-delay: 0.3s;
}

.culture-grid .culture-item.animate:nth-child(4) {
  animation-delay: 0.4s;
}

/* 为职位卡片添加动画 - 滚动触发 */
.job-card {
  opacity: 0;
}

.job-card.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

/* 为福利项目添加动画 - 滚动触发 */
.benefit-item {
  opacity: 0;
}

.benefit-item.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.benefits-grid .benefit-item.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.benefits-grid .benefit-item.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.benefits-grid .benefit-item.animate:nth-child(3) {
  animation-delay: 0.3s;
}

.benefits-grid .benefit-item.animate:nth-child(4) {
  animation-delay: 0.4s;
}

.benefits-grid .benefit-item.animate:nth-child(5) {
  animation-delay: 0.5s;
}

.benefits-grid .benefit-item.animate:nth-child(6) {
  animation-delay: 0.6s;
}

/* 为流程步骤添加动画 - 滚动触发 */
.process-step {
  opacity: 0;
}

.process-step.animate {
  animation: fadeInUp 0.6s ease-out forwards;
}

.process-steps .process-step.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.process-steps .process-step.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.process-steps .process-step.animate:nth-child(3) {
  animation-delay: 0.3s;
}

.process-steps .process-step.animate:nth-child(4) {
  animation-delay: 0.4s;
}

.process-steps .process-step.animate:nth-child(5) {
  animation-delay: 0.5s;
}

/* 为CTA部分添加动画 - 滚动触发 */
.cta-title {
  opacity: 0;
}

.cta-title.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

.cta-subtitle {
  opacity: 0;
}

.cta-subtitle.animate {
  animation: fadeInUp 0.8s ease-out 0.1s forwards;
}
</style>