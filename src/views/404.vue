<template>
  <div class="not-found-container" :class="{ 'svg-background': useSvgBackground }">
    <!-- SVG Background -->
    <div v-if="useSvgBackground" class="svg-background-container">
      <img src="/images/404.svg" alt="Starry Night Background" class="svg-background-image" />
      <!-- Animated Stars -->
      <!-- <div class="star-field">
        <div
          class="animated-star"
          v-for="n in 30"
          :key="n"
          :style="getStarStyle(n)"
        ></div>
      </div>-->
    </div>

    <!-- Original Background Effects (when not using SVG) -->
    <div v-else class="background-effects">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="floating-elements">
        <div class="floating-dot" v-for="n in 20" :key="n" :style="getFloatingStyle(n)"></div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="not-found-content">
      <div class="error-visual">
        <div class="error-number">404</div>
        <div class="error-glow"></div>
      </div>

      <div class="error-text">
        <h1 class="error-title">Page Not Found</h1>
        <p class="error-description">
          The page you're looking for doesn't exist or has been moved.
          <br>Let's get you back to <a href="/" class="link">home page</a>.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'

const router = useRouter()
const useSvgBackground = ref(false)

const goBack = () => {
  window.history.length > 1 ? router.go(-1) : router.push('/')
}

// Generate random positions for floating elements
const getFloatingStyle = (index) => {
  const size = Math.random() * 4 + 2 // 2-6px
  const left = Math.random() * 100 // 0-100%
  const top = Math.random() * 100 // 0-100%
  const delay = Math.random() * 5 // 0-5s
  const duration = Math.random() * 3 + 2 // 2-5s

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    top: `${top}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`
  }
}

// Generate random positions for stars
const getStarStyle = (index) => {
  const size = Math.random() * 3 + 1 // 1-4px
  const left = Math.random() * 100 // 0-100%
  const top = Math.random() * 100 // 0-100%
  const delay = Math.random() * 10 // 0-10s
  const duration = Math.random() * 60 + 60 // 60-120s
  const distance = Math.random() * 50 + 25 // 25-75px orbit radius
  const opacity = Math.random() * 0.6 + 0.4 // 0.4-1.0 opacity
  const brightness = Math.random() * 0.5 + 0.5 // 0.5-1.0 brightness

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    top: `${top}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`,
    '--orbit-radius': `${distance}px`,
    opacity: opacity,
    filter: `brightness(${brightness})`
  }
}

// Randomly choose background on mount
onMounted(() => {
  useSvgBackground.value = 1 //Math.random() > 0.5
})
</script>

<style scoped>
/* Import main styles */

.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0a0a0a;
  position: relative;
  overflow: hidden;
  padding: 2rem;
  font-family: 'Inter', sans-serif;
}

/* Background Effects */
.background-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.3;
  animation: float 8s ease-in-out infinite;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #3772FF 0%, #37D5C3 100%);
  top: -20%;
  left: -10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #9945FF 0%, #FF6B6B 100%);
  bottom: -15%;
  right: -5%;
  animation-delay: 4s;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-dot {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: twinkle 3s ease-in-out infinite;
}

/* SVG Background Styles */
.svg-background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.svg-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Overlay for better text readability on SVG background */
.svg-background-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: rgba(0, 0, 0, 0.3); */
  z-index: 2;
}

.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.animated-star {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: starOrbit linear infinite;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.animated-star::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: starTwinkle 2s ease-in-out infinite alternate;
}

/* Main Content */
.not-found-content {
  text-align: center;
  color: #ffffff;
  max-width: 700px;
  z-index: 15;
  position: relative;
}

/* Error Visual */
.error-visual {
  position: relative;
  margin-bottom: 3rem;
}

.error-number {
  font-size: 72px;
  font-weight: bold;
  color: #fff;
  /* background: linear-gradient(135deg, #3772FF 0%, #37D5C3 100%); */
  /* -webkit-background-clip: text; */
  /* -webkit-text-fill-color: transparent; */
  background-clip: text;
  margin-bottom: 0;
  letter-spacing: -0.05em;
  position: relative;
  z-index: 2;
}

.error-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(55, 114, 255, 0.2) 0%, transparent 70%);
  z-index: 1;
  animation: pulse 2s ease-in-out infinite;
}

.link:hover {
  text-decoration: underline;
  color: #fff;
}
/* Error Text */
.error-text {
  margin-bottom: 3rem;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.error-description {
  font-size: 1.125rem;
  color: #9e9e9e;
  line-height: 1.6;
  margin-bottom: 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-buttons .btn i {
  font-size: 1rem;
}

/* Quick Links */
.quick-links {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
}

.quick-links h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.quick-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(55, 114, 255, 0.3);
  transform: translateY(-2px);
  color: #ffffff;
  text-decoration: none;
}

.quick-link i {
  font-size: 1.5rem;
  color: #3772FF;
}

.quick-link span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* Star Animation Keyframes */
@keyframes starOrbit {
  0% {
    transform: rotate(0deg) translate3d(var(--orbit-radius), 0, 0) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translate3d(var(--orbit-radius), 0, 0) rotate(-360deg);
  }
}

@keyframes starTwinkle {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .not-found-container {
    padding: 1.5rem;
  }
  
  .error-number {
    font-size: 7rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-description {
    font-size: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-buttons .btn {
    width: 100%;
    max-width: 280px;
  }
  
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-links {
    padding: 1.5rem;
  }
  
  .gradient-orb {
    display: none;
  }

  /* Reduce star animations on mobile for performance */
  .animated-star {
    animation-duration: 120s !important;
  }

  .svg-background-image {
    opacity: 0.6;
  }
}

@media (max-width: 480px) {
  .error-number {
    font-size: 5rem;
  }
  
  .error-title {
    font-size: 1.75rem;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-links {
    padding: 1rem;
  }
}
</style> 