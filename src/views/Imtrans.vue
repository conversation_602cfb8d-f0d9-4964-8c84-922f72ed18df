<template>
  <div class="imtrans-page">
    <div class="top-background"></div>
    
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">AI Manga <span class="text-gradient">Translator</span></h1>
            <p class="hero-subtitle">AI translation accurately and seamlessly converts manga images into the target language with professional quality results.</p>
            <div class="hero-cta">
              <div class="download-section">
                <img :src="downloadSrc" alt="Download App" class="download-image" @mousemove="showQRCode" @mouseleave="">
              </div>
              <!-- <a href="./transpic.html?key=1" class="btn btn-primary btn-m">Try It Now</a> -->
              <!-- <button class="btn btn-outline btn-m" @click="scrollToDemo">See Demo</button> -->
            </div>
          </div>
          <div class="hero-image">
            <img src="/images/imtrans/banner1x.png" alt="AI Manga Translation" class="hero-banner">
            
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Elevate Your Manga Experience</h2>
          <p class="section-subtitle">Download our browser extension or mobile app to seamlessly translate raw manga, manhua, manhwa, and comics on your favorite websites. Enjoy high-quality translations across all devices with just one account.</p>
        </div>
      </div>
    </section>

    <!-- Demo Section -->
    <section class="demo-section" ref="demoSection">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">How Does It Work</h2>
          <p class="section-subtitle">Upload images and click start button.</p>
        </div>
        
        <div class="demo-showcase">
          <div class="demo-image">
            <img src="/images/imtrans/image-before.svg" alt="Original manga" class="demo-img">
            <div class="demo-label">Original</div>
          </div>
          <div class="demo-arrow">
            <img src="/images/imtrans/arrow-right.svg" alt="Arrow" class="demo-arrow-img">
          </div>
          <div class="demo-image">
            <img src="/images/imtrans/image-after.svg" alt="Translated manga" class="demo-img">
            <div class="demo-label">Translated</div>
          </div>
        </div>
        
        <!-- <div class="demo-cta">
          <a href="./transpic.html?key=1" class="btn btn-primary btn-lg">Try It Now</a>
        </div> -->
      </div>
    </section>

    <!-- Languages Section -->
    <section class="languages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title text-gradient">Languages We Support</h2>
          <p class="section-subtitle">Enjoy high quality AI translation for multiple languages. We will continue to support more languages. Let us know if you can't wait for the language you want.</p>
        </div>
        
        <div class="languages-showcase">
          <div class="language-card from-languages">
            <h3 class="language-title">From</h3>
            <ul class="language-list">
              <li>Japanese</li>
              <li>Chinese</li>
              <li>Korean</li>
              <li>And More...</li>
            </ul>
          </div>
          <div class="language-arrow">
            <i class="bi bi-arrow-right"></i>
          </div>
          <div class="language-card to-languages">
            <h3 class="language-title">To</h3>
            <ul class="language-list">
              <li>English</li>
              <li>Indonesian</li>
              <li>Chinese</li>
              <li>And More...</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">Ready to Transform Your Manga Reading?</h2>
          <p class="cta-subtitle">Join thousands of manga enthusiasts who have upgraded their reading experience with our AI translator.</p>
          <!-- <a href="https://apps.apple.com/us/app/imtrans/id6639613717" class="btn-link">  -->
            <button class="btn btn-cta btn-s" onclick="window.open('https://apps.apple.com/us/app/imtrans/id6639613717', '_blank')">Download Now</button>
          <!-- </a> -->
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useHead } from '@vueuse/head'

// 设置页面标题和描述
useHead({
  title: 'Little Grass - AI Manga Translator',
  meta: [
    {
      name: 'description',
      content: 'AI translation accurately and seamlessly converts manga images into the target language with professional quality results.'
    },
    {
      name: 'apple-itunes-app',
      content: 'app-id=6639613717'
    }
  ]
})

// 响应式状态
const downloadSrc = ref('/images/imtrans/ios_down.png')
const demoSection = ref(null)

// 监听窗口大小变化和图片懒加载
onMounted(() => {
  // 懒加载图片
  const images = document.querySelectorAll('img[data-src]')
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        const dataSrc = img.getAttribute('data-src')
        if (dataSrc) {
          const newImg = new Image()
          newImg.onload = () => {
            img.src = dataSrc
            img.classList.add('loaded')
          }
          newImg.src = dataSrc
        }
        imageObserver.unobserve(img)
      }
    })
  })
  
  images.forEach(img => imageObserver.observe(img))
})

// 方法
function showQRCode() {
  downloadSrc.value = '/images/imtrans/code.png'
}

function showDownload() {
  downloadSrc.value = '/images/imtrans/ios_down.png'
}

function scrollToDemo() {
  if (demoSection.value) {
    demoSection.value.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style scoped>
.imtrans-page {
  min-height: 100vh;
}

.top-background {
  background-image: url('/images/imtrans/bg.svg');
}
/* Hero Section */
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  min-height: 80vh;
  padding: 4rem 0;
  align-items: center;
  margin-top: 100px;
}

.hero-title {
  font-size: 4rem;
}
.hero-subtitle {
  font-size: 1.2rem;
  color: #fff;
  opacity: 0.8;
  font-weight: 200;
}
.hero-text {
  flex: 1;
  text-align: center;
}

.hero-image {
  display: flex;
  justify-content: flex-end;
}

.hero-banner {
  max-width: 80%;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: center;
}

.download-section {
  text-align: center;
}

.download-image {
  width: 150px;
  height: 150px;
  cursor: pointer;
  transition: transform 0.3s ease;
  border-radius: 15px;
}

.download-image:hover {
  transform: scale(1.05);
}

/* Features Section */
.features-section {
  padding: 6rem 0;
}

/* Demo Section */
.demo-section {
  padding: 6rem 0;
}

.demo-showcase {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  margin: 4rem 0;
}

.demo-image {
  position: relative;
  text-align: center;
}

.demo-img {
  max-width: 300px;
  height: auto;
  border-radius: 15px;
}

.demo-img.loaded {
  opacity: 1;
}

.demo-label {
  margin-top: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--light-text);
}

.demo-arrow {
  font-size: 3rem;
  color: var(--primary-color);
}

.demo-cta {
  text-align: center;
  margin-top: 3rem;
}

/* Languages Section */
.languages-section {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.1);
}

.languages-showcase {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  margin-top: 4rem;
}

.language-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  min-width: 250px;
  text-align: center;
}

.from-languages {
  background: rgba(0, 0, 0, 0.8);
  color: var(--light-text);
}

.to-languages {
  background: rgba(255, 255, 255, 0.95);
  color: #1b1c1e;
}

.language-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.language-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.language-list li {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.8rem;
  font-style: italic;
}

.language-arrow {
  font-size: 3rem;
  color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }
  
  .hero-cta {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .demo-showcase {
    flex-direction: column;
    gap: 2rem;
  }
  
  .demo-arrow {
    transform: rotate(90deg);
  }
  
  .languages-showcase {
    flex-direction: column;
    gap: 2rem;
  }
  
  .language-arrow {
    transform: rotate(90deg);
  }
  
  .language-card {
    min-width: auto;
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 3rem;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .demo-img {
    max-width: 250px;
  }
}
</style>