<template>
  <div class="contact-page">
    <div class="top-background"></div>
    <div class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Cantact Us</h1>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
      <div class="contact-header">
        <div class="container">
          <p>Thank you for your interest in Little Grass! <br />
            We're excited to connect with you and explore how our AI
            solutions can help your business grow. <br />
            Please use the information below to get in touch with us.</p>
        </div>
      </div>

       <!-- Contact Information Cards -->
      <section class="contact-section">
        <div class="container">
        <div class="contact-info-section">
          <div class="contact-card">
            <h2>General Inquiries</h2>
            <p class="inline-text">For general questions, comments, or inquiries about our AI solutions, please contact us:</p>
            <a href="mailto:<EMAIL>" class="contact-button">
              <span>Email Us</span>
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.91016 4V5.6H13.1822L3.91016 14.872L5.03816 16L14.3102 6.728V12H15.9102V4H7.91016Z" fill="white"/>
              </svg>

            </a>
          </div>

          <div class="contact-card">
            <h2>
              <i class="layui-icon layui-icon-chart"></i>Sales & Partnerships
            </h2>
            <p>If you'd like to learn more about our pricing, partnership opportunities, or custom solutions, please
              contact our sales team:</p>
            <a href="mailto:<EMAIL>" class="contact-button">
              <span>Contact Sales</span>
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.91016 4V5.6H13.1822L3.91016 14.872L5.03816 16L14.3102 6.728V12H15.9102V4H7.91016Z" fill="white"/>
              </svg>
            </a>
          </div>

          <div class="contact-card">
            <h2>
              <i class="layui-icon layui-icon-service"></i>Technical Support
            </h2>
            <p>Need help with our products or services? Our technical support team is ready to assist you:</p>
            <a href="mailto:<EMAIL>" class="contact-button">
              <span>Get Support</span>
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.91016 4V5.6H13.1822L3.91016 14.872L5.03816 16L14.3102 6.728V12H15.9102V4H7.91016Z" fill="white"/>
              </svg>
            </a>
          </div>

          <div class="contact-card">
            <h2>Careers</h2>
            <p>Interested in joining our team of AI experts? Check out our career opportunities:</p>
            <a href="/career" class="contact-button">
              <span>View Careers</span>
              <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.91016 4V5.6H13.1822L3.91016 14.872L5.03816 16L14.3102 6.728V12H15.9102V4H7.91016Z" fill="white"/>
              </svg>
            </a>
          </div>
          </div>
        </div>
      </section>

      <!-- Feedback Form -->
      <section class="feedback-section" id="feedback" style="display: none;">
        <div class="section-title text-gradient">Feedback</div>
        <p class="section-subtitle">Please fill out the form below to provide your feedback, suggestions, or questions. We will get back to you
          as soon as possible.</p>
        <form @submit.prevent="submitFeedback" class="feedback-form">
          <div class="form-group">
            <label for="product" class="form-label">Product</label>
            <select id="product" v-model="feedback.product" class="form-control">
              <option value="" disabled>Select a product</option>
              <option v-for="product in contentStore.products" :key="product.url" :value="product.appid">
                {{ product.title }}
              </option>
              <option value="home">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label for="content" class="form-label">Feedback</label>
            <textarea id="content" v-model="feedback.content" class="form-control" rows="5"
              placeholder="Please enter your feedback" required></textarea>
          </div>

          <div class="form-group">
            <label for="email" class="form-label">Email (Optional)</label>
            <input type="email" id="email" v-model="feedback.email" class="form-control"
              placeholder="Please enter your email address">
            <small class="form-text">If you'd like to receive a reply, please provide your email address</small>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-xl btn-secondary" :disabled="isSubmitting">
              {{ isSubmitting ? 'Submitting...' : 'Submit Feedback' }}
            </button>
          </div>
        </form>

        <div v-if="submitSuccess" class="success-message">
          Thank you for your feedback! We have received your information and will process it soon.
        </div>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </section>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useHead } from '@vueuse/head'
import { useContentStore } from '@/store/content'
import { submitFeedback as submitFeedbackApi } from '@/api/http'

// State management
const contentStore = useContentStore()

// Set page metadata
useHead({
  title: 'Little Grass - Support',
  meta: [
    {
      name: 'description',
      content: 'Contact the Little Grass team, learn about our AI solutions, provide feedback, or get technical support. We are ready to help you.'
    }
  ]
})

// 滚动触发动画
onMounted(() => {
  // 创建Intersection Observer实例
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      // 当元素进入视口时
      if (entry.isIntersecting) {
        // 添加动画类
        entry.target.classList.add('animate');
        // 停止观察该元素
        observer.unobserve(entry.target);
      }
    });
  }, {
    // 设置元素进入视口20%时触发
    threshold: 0.2
  });

  // 观察所有需要动画的元素
  const animatedElements = document.querySelectorAll('.contact-header, .contact-card, .feedback-section');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
});

// Feedback form data
const feedback = reactive({
  product: '',
  content: '',
  email: ''
})

const isSubmitting = ref(false)
const submitSuccess = ref(false)
const errorMessage = ref('')

// Submit feedback
const submitFeedback = async () => {
  isSubmitting.value = true
  errorMessage.value = ''
  
  try {
    // 调用API提交反馈
    await submitFeedbackApi({
      content: feedback.content,
      product: feedback.product
    })
    
    // 重置表单
    feedback.product = ''
    feedback.content = ''
    feedback.email = ''
    
    // 显示成功消息
    submitSuccess.value = true
    setTimeout(() => {
      submitSuccess.value = false
    }, 5000)
  } catch (error) {
    console.error('Failed to submit feedback:', error)
    errorMessage.value = error.message || 'Failed to submit feedback'
    setTimeout(() => {
      errorMessage.value = ''
    }, 5000)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.top-background {
  background-image: url('/images/bg_support.svg');
}
.contact-header {
  text-align: center;
  padding: 60px 0 40px;
  max-width: 1200px;
  margin: 0 auto;
  font-size: 22px;
  font-weight: 200;
}
.contact-section {
  padding: 100px 0;
  margin-bottom: 200px;
}

/* Section styling */
.feedback-section {
  margin-bottom: 60px;
  padding: 40px 0;
  max-width: 800px;
  margin: 0 auto 60px;
}

.feedback-section h2 {
  display: flex;
  align-items: center;
  font-size: 28px;
  margin-bottom: 24px;
  /* color: #4db6ac; */
}

.feedback-section h2 i {
  color: #4db6ac;
  margin-right: 12px;
  font-size: 20px;
}

.feedback-section p {
  font-size: 18px;
  line-height: 1.6;
  color: #b0bec5;
  margin-bottom: 32px;
}

/* Contact info section */
.contact-info-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 50px;
  margin-bottom: 60px;
  width: 80%;
  margin: 0 auto;
}

.contact-card {
  background: linear-gradient(180deg, #282837 100%, #2D3035 100%);
  border-radius: 10px;
  padding: 30px;
  height: 280px;
  display: flex;
  flex-direction: column;
}

/* .contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.5);
} */

.contact-card h2 {
  display: flex;
  align-items: center;
  font-size: 22px;
  margin-bottom: 16px;
  /* color: #4db6ac; */
}

.contact-card p {
  font-size: 16px;
  line-height: 1.5;
  color: #b0bec5;
  margin-bottom: 16px;
  
}
.contact-card a {
  margin-top: auto;
}

/* Feedback form */
.feedback-form {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 32px;
  margin-top: 24px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #e0e0e0;
  font-size: 16px;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  background-color: #2d2d2d;
  border: 1px solid #444444;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

select.form-control {
  font-size: 14px; 
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
  height: auto; /* 自动调整高度 */
  padding-top: 8px; /* 增加上下内边距 */
  padding-bottom: 8px;
}

textarea.form-control {
  min-height: 120px; /* 约等于5行文本的高度 */
  resize: vertical; /* 允许用户调整高度 */
}

.form-control:focus {
  border-color: #4db6ac;
  outline: none;
  box-shadow: 0 0 0 2px rgba(77, 182, 172, 0.2);
}

.form-control::placeholder {
  color: #757575;
}

.form-text {
  display: block;
  margin-top: 6px;
  color: #9e9e9e;
  font-size: 14px;
}

.form-actions {
  margin-top: 32px;
}

.submit-button {
  background-color: #4db6ac;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  transition: background-color 0.2s, box-shadow 0.2s;
}

.submit-button:hover {
  background-color: #26a69a;
  box-shadow: 0 1px 3px rgba(0,0,0,0.5);
}

.submit-button:disabled {
  background-color: #424242;
  color: #757575;
  cursor: not-allowed;
  box-shadow: none;
}

.success-message {
  background-color: #1b5e20;
  border-left: 4px solid #4db6ac;
  color: #e0e0e0;
  padding: 16px;
  border-radius: 4px;
  margin-top: 24px;
  font-weight: 500;
}

.error-message {
  background-color: #b71c1c;
  border-left: 4px solid #ef5350;
  color: #e0e0e0;
  padding: 16px;
  border-radius: 4px;
  margin-top: 24px;
  font-weight: 500;
}

/* Contact buttons */
.contact-button {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 200;
  font-size: 16px;
  padding-left: 5px;
  /* padding: 12px 24px; */
  /* border: 2px solid rgba(255, 255, 255, 0.2); */
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-top: auto;
  line-height: 1.5;
}
.contact-button span {
  line-height: 20px;
}

.contact-button span:hover {
  text-decoration: underline;
}

.contact-button:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .contact-header h2 {
    font-size: 32px;
  }
  
  .contact-info-section {
    grid-template-columns: 1fr;
  }
  
  .feedback-form {
    padding: 24px;
  }
  
  .submit-button {
    width: 100%;
  }
}

/* 动画样式 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 为标题添加动画 */
.hero-title {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 为联系头部添加动画 - 滚动触发 */
.contact-header {
  opacity: 0;
}

.contact-header.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* 为联系卡片添加动画 - 滚动触发 */
.contact-card {
  opacity: 0;
}

.contact-card.animate {
  animation: scaleIn 0.6s ease-out forwards;
}

.contact-info-section .contact-card.animate:nth-child(1) {
  animation-delay: 0.1s;
}

.contact-info-section .contact-card.animate:nth-child(2) {
  animation-delay: 0.2s;
}

.contact-info-section .contact-card.animate:nth-child(3) {
  animation-delay: 0.3s;
}

.contact-info-section .contact-card.animate:nth-child(4) {
  animation-delay: 0.4s;
}

/* 为反馈部分添加动画 - 滚动触发 */
.feedback-section {
  opacity: 0;
}

.feedback-section.animate {
  animation: fadeInUp 0.8s ease-out forwards;
}

</style>