// Auth0配置文件
const auth0Config = {
  domain: 'auth.littlegrass.cc',
  clientId: 'EEwiaXCZYz6K8Ag1jEijdvyERTPDexeN',
  // clientId: 'DkYJkGGlbJNLot64YOMaWcLtU3froLVV',
  authorizationParams: {
    redirect_uri: window.location.origin + '/capgen/login',
    scope: 'openid profile email'
  },
  cacheLocation: 'localstorage',
  useRefreshTokens: true
}

// 获取Auth0配置
export const getAuth0Config = () => {
  return auth0Config
}

// 检查Auth0是否已正确配置
export const isAuth0Configured = () => {
  return auth0Config.domain !== 'YOUR_AUTH0_DOMAIN' && 
         auth0Config.clientId !== 'YOUR_AUTH0_CLIENT_ID'
}