<template>
  <div class="app-container">
    <Header v-if="!hideHeader" />
    <div class="router-view-container" :class="{ 'full-page': hideHeader && hideFooter }">
      <router-view />
    </div>
    <Footer v-if="!hideFooter" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Header from './components/common/Header.vue'
import Footer from './components/common/Footer.vue'

const route = useRoute()

// 根据路由元数据决定是否隐藏头部和底部
const hideHeader = computed(() => route.meta.hideHeader)
const hideFooter = computed(() => route.meta.hideFooter)
</script>

<style>
@import './assets/styles/main.scss';

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 确保内容区域占据所有可用空间，将footer推到底部 */
.router-view-container {
  flex: 1;
  position: relative;
  min-height: 500px; /* 确保有足够的最小高度 */
  /* padding-top: 70px;  */
}

/* 添加加载状态样式 */
.router-view-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--dark-bg);
  z-index: -1;
}

/* 全页面样式（无头部和底部） */
.full-page {
  padding: 0;
  margin: 0;
  min-height: 100vh;
}
</style>