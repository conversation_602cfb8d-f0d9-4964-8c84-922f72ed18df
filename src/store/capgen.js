// CapGen 定价计划和功能配置
export const pricingPlans = {
  free: {
    name: 'Free',
    price: {
      monthly: 0,
      annual: 0
    },
    description: 'Perfect for trying out CapGen',
    features: [
      '100 minutes of video',
      'Basic subtitle extraction', 
      '6 languages translation',
      'Community support'
    ],
    exportFormats: ['SRT'],
    apiAccess: false,
    supportLevel: 'Community',
    stripeLinks: {
      monthly: null,
      annual: null
    }
  },
  basic: {
    name: 'Basic',
    popular: true,
    price: {
      monthly: 1.99,
      annual: 1.58
    },
    description: 'Great for content creators',
    features: [
      'Unlimited video processing',
      'AI-powered subtitle extraction',
      'Translation (20+ languages)',
      'Email support'
    ],
    exportFormats: ['SRT'],
    apiAccess: false,
    supportLevel: 'Email',
    stripeLinks: {
      monthly: 'https://buy.stripe.com/5kQeV59eY6ck2YAbdr38402',
      annual: 'https://buy.stripe.com/dRmdR1dve1W4gPq81f38405'
    },
    product_id: {
      monthly: 'basic_monthly',
      annual: 'basic_annual'
    }
  },
  pro: {
    name: 'Pro',
    price: {
      monthly: 7.99,
      annual: 6.42
    },
    description: 'Best for professional creators',
    popular: false,
    features: [
      'Unlimited video processing',
      'AI-powered subtitle extraction',
      'Translation (20+ languages)',
      'High-precision timeline fitting',
      'Export in multiple formats',
      'Priority support',
    ],
    exportFormats: ['SRT', 'VTT', 'ASS'],
    apiAccess: true,
    supportLevel: 'Priority',
    stripeLinks: {
      monthly: 'https://buy.stripe.com/dRm8wH62M6ck9mYdlz38403',
      annual: 'https://buy.stripe.com/3cI7sD62McAI9mY3KZ38406'
    },
    product_id: {
      monthly: 'pro_monthly',
      annual: 'pro_annual'
    }
  },
  // business: {
  //   name: 'Business',
  //   price: {
  //     monthly: 299,
  //     annual: 239
  //   },
  //   description: 'For large teams and organizations',
  //   features: [
  //     'Unlimited video processing',
  //     'AI-powered subtitle extraction',
  //     'Translation (20+ languages)',
  //     'Export in multiple formats',
  //     'Dedicated support',
  //     'API access',
  //   ],
  //   exportFormats: ['SRT', 'VTT', 'ASS'],
  //   apiAccess: true,
  //   supportLevel: 'Dedicated',
  //   stripeLinks: {
  //     monthly: 'https://buy.stripe.com/00wbITaj258gczagxL38404',
  //     annual: 'https://buy.stripe.com/4gM14f8aUgQYfLm81f38407'
  //   },
  //   product_id: {
  //     monthly: 'business_monthly',
  //     annual: 'business_annual'
  //   }
  // }
}

// 功能对比表配置
export const featureComparison = {
  videoProcessingTime: {
    name: 'Video Processing Time',
    values: {
      free: '100 min',
      basic: 'Unlimited',
      pro: 'Unlimited',
      business: 'Unlimited'
    }
  },
  languagesSupported: {
    name: 'Languages Supported',
    values: {
      free: '6',
      basic: '20+',
      pro: '20+',
      business: '100+'
    }
  },
  exportFormats: {
    name: 'Export Formats',
    values: {
      free: 'SRT',
      basic: 'SRT',
      pro: 'SRT, VTT, ASS',
      business: 'SRT, VTT, ASS'
    }
  },
  apiAccess: {
    name: 'API Access',
    values: {
      free: '-',
      basic: '-',
      pro: '-',
      business: '✓'
    }
  },
  supportLevel: {
    name: 'Support Level',
    values: {
      free: 'Community',
      basic: 'Email',
      pro: 'Priority',
      business: 'Dedicated'
    }
  }
}

// 常见问题配置
export const faqData = [
  {
    question: 'How accurate is CapGen\'s subtitle extraction?',
    answer: 'CapGen achieves 99%+ accuracy rates using state-of-the-art AI models trained on millions of hours of video content. Accuracy depends on audio quality, but our advanced noise reduction and speech enhancement ensure professional results even with challenging audio.'
  },
  {
    question: 'What languages are supported for translation?',
    answer: 'CapGen supports 100+ languages including English, Spanish, French, German, Chinese, Japanese, Korean, Arabic, Hindi, Portuguese, Russian, and many more. Our AI understands context and cultural nuances for accurate translations.'
  },
  {
    question: 'How long does subtitle generation take?',
    answer: 'Processing time is typically 1/4 the length of your video. A 60-minute video takes about 15 minutes to process. Pro users get priority processing for even faster results.'
  },
  {
    question: 'What video formats are supported?',
    answer: 'CapGen supports all major video formats including MP4, MOV, AVI, WMV, MKV, FLV, and more. You can also directly import from YouTube, Vimeo, and other platforms by pasting the URL.'
  },
  {
    question: 'Is my video content secure and private?',
    answer: 'Absolutely. All videos are encrypted during upload and processing. We never store your content permanently and automatically delete files after processing. Enterprise customers get additional security features and compliance certifications.'
  }
]

// 定价页面专用FAQ
export const pricingFaqData = [
  {
    question: 'Can I change plans anytime?',
    answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and you\'ll be charged pro-rata for any upgrades.'
  },
  {
    question: 'What happens after the free trial?',
    answer: 'After your 14-day free trial ends, you\'ll be automatically charged for your selected plan. You can cancel anytime during the trial without any charges.'
  },
  {
    question: 'Do you offer refunds?',
    answer: 'Yes, we offer a 30-day money-back guarantee. If you\'re not satisfied with CapGen, contact us within 30 days for a full refund.'
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers for enterprise customers.'
  }
]

// 工具函数
export const getPlanPrice = (planKey, billingType = 'monthly') => {
  return pricingPlans[planKey]?.price[billingType] || 0
}

export const getPlanFeatures = (planKey) => {
  return pricingPlans[planKey]?.features || []
}

export const getStripeLink = (planKey, billingType = 'monthly') => {
  return pricingPlans[planKey]?.stripeLinks[billingType] || null
}

export const getProductId = (planKey, billingType = 'monthly') => {
  const productId = pricingPlans[planKey]?.product_id[billingType] || null
  
  console.log('getProductId 函数执行:', {
    planKey,
    billingType,
    productId,
    availablePlans: Object.keys(pricingPlans),
    planData: pricingPlans[planKey],
    timestamp: new Date().toISOString()
  })
  
  if (!productId) {
    console.error('未找到产品ID:', {
      planKey,
      billingType,
      availablePlans: Object.keys(pricingPlans),
      planData: pricingPlans[planKey]
    })
  }
  
  return productId
}

export const getFeatureValue = (featureKey, planKey) => {
  return featureComparison[featureKey]?.values[planKey] || '-'
}
