import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useProductsStore = defineStore('products', {
  state: () => ({
    // 主要产品数据
    featuredProducts: [
      {
        title: 'Imtrans',
        logo: '/images/products/logo_imtrans.png',
        description: 'imtrans empowers you to effortlessly translate images and comics across multiple languages. Whether capturing photos, uploading images, processing compressed files, or downloading from web pages, imtrans delivers accurate translations while preserving original layouts.',
        features: [
          {
            icon: '📷',
            title: 'Instant Photo Translation',
            description: 'Capture photos and translate text in real-time—perfect for signs, menus, or handwritten notes.'
          },
          {
            icon: '🖼️',
            title: 'Image & Comic Upload',
            description: 'Upload images or entire comic pages for seamless, multi-language translation.'
          },
          {
            icon: '📦',
            title: 'Compressed File & Web Support',
            description: 'Translate comics from compressed files or directly from website image galleries.'
          }
        ],
        url: '/imtrans',
        image: '/images/products/imtrans.png' 
      },
      {
        title: 'CapGen',
        logo: '/images/products/logo_capgen.png',
        description: 'Capgen enables effortless extraction of subtitles from your videos and translates them into multiple languages. Enhance accessibility and reach a global audience with ease.',
        features: [
          {
            icon: '🎬',
            title: 'Automatic Subtitle Extraction',
            description: 'Upload your video files and let Capgen automatically generate accurate subtitles.'
          },
          {
            icon: '🌐',
            title: 'Multilingual Translation',
            description: 'Translate extracted subtitles into various languages to cater to a diverse audience.'
          },
          {
            icon: '🛠️',
            title: 'Customizable Subtitles',
            description: 'Edit and style your subtitles to match your brand or personal preferences.'
          }
        ],
        url: '/capgen',
        image: '/images/products/capgen.png'
      },
      {
        title: '24 Assistant',
        logo: '/images/products/logo_24assistant.png',
        description: 'Your AI-powered conversation companion, now with vision! Chat, analyze files, and explore endless possibilities with diverse AI personalities.',
        features: [
          {
            icon: '📸',
            title: 'Multimodal AI',
            description: 'Upload images or documents, and let 24 Assistant extract insights, answer questions, or even brainstorm ideas—all through natural conversation.'
          },
          {
            icon: '🔍',
            title: 'Role-Based Expertise',
            description: 'Switch between specialized AI roles: a creative writer, a data analyst, a language tutor, and more. Tailor responses to your needs instantly.'
          },
          {
            icon: '💰',
            title: 'Smart & Adaptive',
            description: 'Get precise, context-aware answers whether you’re learning, working, or just exploring. 24 Assistant learns from your inputs to deliver richer interactions over time.'
          }
        ],
        url: '/24Assistant',
        image: '/images/products/24assistant.png'
      }
    ],
    
    // 其他产品数据
    otherProducts: [
      {
        title: 'AI Content Generator',
        description: 'Create high-quality content for blogs, social media, and marketing materials with AI assistance.',
        url: '/content-generator',
        image: '/img/products/content-generator.jpg'
      },
      {
        title: 'Voice Recognition',
        description: 'Convert speech to text with industry-leading accuracy in multiple languages and dialects.',
        url: '/voice-recognition',
        image: '/img/products/voice-recognition.jpg'
      },
      {
        title: 'Data Visualization',
        description: 'Transform complex data into clear, interactive visualizations that tell a compelling story.',
        url: '/data-visualization',
        image: '/img/products/data-visualization.jpg'
      },
      {
        title: 'AI Assistant',
        description: 'Boost developer productivity with intelligent code suggestions and automated debugging.',
        url: '/code-assistant',
        image: '/img/products/code-assistant.jpg'
      }
    ],
    
    // 即将推出的产品
    comingSoonProducts: [
      {
        title: 'AI Video Editor',
        description: 'Automatically edit videos, remove backgrounds, and add special effects with AI technology.',
        image: '/img/products/video-editor.jpg'
      },
      {
        title: 'Sentiment Analysis',
        description: 'Understand customer emotions and feedback through advanced natural language processing.',
        image: '/img/products/sentiment-analysis.jpg'
      }
    ],
    
    // 客户案例
    caseStudies: [
      {
        title: 'How TechCorp Increased Productivity by 40%',
        description: 'TechCorp implemented our AI Assistant across their organization, resulting in significant time savings and improved workflow efficiency.',
        quote: 'The AI solutions from Little Grass have transformed how our team works. Tasks that used to take hours now take minutes.',
        author: 'Sarah Johnson',
        role: 'CTO at TechCorp'
      },
      {
        title: 'GlobalMedia Reaches New Markets with AI Translation',
        description: 'Using our Intelligent Translation system, GlobalMedia was able to expand their content to 15 new countries in just three months.',
        quote: 'The accuracy of the translations is remarkable. Our international audience has grown by 300% since implementing this solution.',
        author: 'Michael Chen',
        role: 'Content Director at GlobalMedia'
      },
      {
        title: 'DesignStudio Enhances Creative Output',
        description: 'DesignStudio leverages our AI Image Enhancement tools to deliver higher quality work to clients in less time.',
        quote: 'The image enhancement capabilities have become an essential part of our workflow. The results are consistently impressive.',
        author: 'Emma Rodriguez',
        role: 'Lead Designer at DesignStudio'
      }
    ]
  }),
  
  getters: {
    getFeaturedProducts: (state) => state.featuredProducts,
    getOtherProducts: (state) => state.otherProducts,
    getComingSoonProducts: (state) => state.comingSoonProducts,
    getCaseStudies: (state) => state.caseStudies
  }
})