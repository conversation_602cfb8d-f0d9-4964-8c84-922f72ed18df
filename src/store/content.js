import { defineStore } from 'pinia'

export const useContentStore = defineStore('content', {
  state: () => ({
    products: [
      {
        title: "Imtrans",
        description: "AI Translation accurately and seamlessly convert image text into the target language.",
        join: "Try now",
        url: "/imtrans",
        img: "/img/imgt.png",
        appid: "ai-manga"
      },
      {
        title: "CapGen",
        description: "",
        join: "Try now",
        url: "/capgen",
        img: "/img/imgt.png",
        appid: "ai-subtitle"
      },
      {
        title: "Pick+",
        description: "",
        join: "Try now",
        url: "/pickplus",
        img: "/img/imgt.png",
        appid: "pickplus"
      },
      // {
      //   title: "Upscale",
      //   description: "Enhance image quality by improving clarity, contrast and color saturation while effectively reducing noise and distortion.",
      //   join: "Try now",
      //   url: "/enhancement",
      //   img: "/img/imgt.png",
      // },
      // {
      //   title: "Outpaint",
      //   description: "AI-powered upscaling provide clearer, more natural magnification while preserving image detail and color.",
      //   join: "Try now",
      //   url: "/outpaint",
      //   img: "/img/imgt.png",
      // },
      {
        title: "Chat Assistant",
        description: "I have a powerful AI conversation engine that can help you write quickly, come and try it!",
        join: "Try now",
        url: "/chat",
        img: "/img/imgt.png",
        appid: "ai-chat"
      }
    ],
    menuItems: [
      { name: 'About', path: '/' },
      { name: 'Products', path: '/products' },
      { name: 'Technologies', path: '/technologies' },
      { name: 'Career', path: '/career' },
      { name: 'Contact', path: '/contact' }
    ],
    homeContent: {
      banner: {
        title: "Making AI your reliable companion",
        description: "Seamlessly integrating AI into your daily life\nUnlocking new possibilities through intelligent technology",
        image: "/img/index/image.png"
      },
      manga: {
        title: "Imtrans: Unlock the world of comics with our mobile app",
        description: "Download our or mobile app to seamlessly translate comics on your phone. Enjoy high-quality translations with just one tap. Upgrade your reading adventure now.",
        beforeImage: "/img/index/manga-ad1.jpg",
        afterImage: "/img/index/manga-ad.jpg"
      },
      upscale: {
        title: "Experience the transformative power of our Image AI Processing services, designed to enhance your visual content",
        description: "Let our advanced AI technology elevate your visuals, showcasing the impact of quality, conversion-oriented design on your business",
        items: [
          {
            title: "Upscale",
            description: "Intelligently increase image resolution, meeting various size requirements while maintaining quality.",
            beforeImage: "/img/index/upscale1-1.jpg",
            afterImage: "/img/index/upscale1-0.jpg"
          },
          {
            title: "Sharpen",
            description: "Enhance edges and details, making your images more vivid and striking.",
            beforeImage: "/img/index/upscale2-1.jpg",
            afterImage: "/img/index/upscale2-0.jpg"
          },
          {
            title: "Outpainting",
            description: "Extend your images beyond their original boundaries with AI-generated content.",
            beforeImage: "/img/index/upscale3-1.jpg",
            afterImage: "/img/index/upscale3-0.jpg"
          }
        ]
      },
      subtitle: {
        title: "Introducing our Intelligent Media Player, a state-of-the-art solution designed to automatically add subtitles to your videos, enhancing accessibility and viewer engagement",
        description: "Automatic Subtitle Generation: Employs advanced speech recognition technology to accurately transcribe audio from your videos, providing precise subtitles without manual input.\nMulti-Format Support: Compatible with various video formats, including AVI, MKV, MP4, and MOV, ensuring seamless integration with your existing media library.\nMulti-Language Support: Capable of recognizing and transcribing multiple languages, making it ideal for diverse content and audiences.\nCustomization Options: Allows you to edit and customize the generated subtitles, adjusting font, size, color, and positioning to match your preferences.\nUser-Friendly Interface: Designed with simplicity in mind, enabling users of all skill levels to easily add subtitles to their videos.\nEnhance your video content with our Intelligent Media Player, making it more accessible and engaging for all viewers.",
        beforeImage: "/img/index/subtitle2.jpg",
        afterImage: "/img/index/subtitle1.jpg"
      },
      chat: {
        sections: [
          {
            title: "Intelligent Assistant",
            description: "From personalized workout plans to travel recommendations, professional development advice, and emotional well-being support, our assistants provide tailored assistance to meet your unique requirements",
            image: "/img/index/chat1.png"
          },
          {
            title: "Multiple Roles",
            description: "Whether you need a fitness coach, travel guide, career advisor, or emotional support companion, our intelligent assistants are here to help",
            image: "/img/index/chat3.png"
          },
          {
            title: "Versatile Applications",
            description: "From personalized workout plans to travel recommendations, professional development advice, and emotional well-being support, our assistants provide tailored assistance to meet your unique requirements",
            image: "/img/index/chat2.png"
          },
          {
            title: "File Analysis",
            description: "Effortlessly analyze data from PDF and Excel files, generating insightful reports and visualizations to enhance your productivity",
            image: "/img/index/chat3.png"
          }
        ]
      }
    }
  }),
  actions: {
    // 这里可以添加内容相关的方法
  }
})