import { defineStore } from 'pinia'
import md5 from 'md5'
import { saveUserToCookie, clearUserCookies, getUserFromCookie } from '@/utils/cookies.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 完整用户信息（包含敏感信息）
    user: null,
    // 敏感信息 - 仅存储在内存中
    token: localStorage.getItem('tk'), // 服务器access_token
    auth0IdToken: null, // Auth0 ID token
    // 设备信息
    device: null,
    isPhone: false
  }),
  getters: {
    isLoggedIn: (state) => !!state.user && !!state.token,
    getToken: (state) => state.token,
    // 获取非敏感用户信息（用于显示）
    getPublicUserInfo: (state) => {
      if (!state.user) return null
      return {
        id: state.user.id,
        email: state.user.email,
        name: state.user.name,
        nickname: state.user.nickname,
        uid: state.user.uid,
        vip: state.user.vip,
        vipExpiredAt: state.user.vipExpiredAt,
        loginTime: state.user.loginTime,
      }
    }
  },
  actions: {
    /**
     * 设置完整用户信息
     * @param {object} userData - 完整用户数据
     */
    setUser(userData) {
      this.user = userData
      
      // 保存非敏感信息到Cookie
      const publicUserInfo = {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        nickname: userData.nickname,
        uid: userData.uid,
        vip: userData.vip,
        vipExpiredAt: userData.vipExpiredAt,
        loginTime: userData.loginTime,
      }
      
      saveUserToCookie(publicUserInfo)
      console.log('用户信息已设置，非敏感信息已同步到Cookie')
    },

    /**
     * 设置服务器access token
     * @param {string} token - 服务器返回的access token
     */
    setToken(token) {
      this.token = token
      localStorage.setItem('tk', token)
      console.log('服务器token已设置')
    },

    /**
     * 设置Auth0 ID token
     * @param {string} idToken - Auth0 ID token
     */
    setAuth0IdToken(idToken) {
      this.auth0IdToken = idToken
      console.log('Auth0 ID token已设置')
    },

    /**
     * 完整登录 - 设置所有用户相关信息
     * @param {object} userData - 用户数据
     * @param {string} serverToken - 服务器access token
     * @param {string} auth0IdToken - Auth0 ID token
     */
    login(userData, serverToken, auth0IdToken = null) {
      this.setUser(userData)
      this.setToken(serverToken)
      if (auth0IdToken) {
        this.setAuth0IdToken(auth0IdToken)
      }
      console.log('用户登录成功')
    },

    /**
     * 完整退出登录
     */
    logout() {
      // 清除所有用户状态
      this.user = null
      this.token = null
      this.auth0IdToken = null
      
      // 清除localStorage
      localStorage.removeItem('tk')
      localStorage.removeItem('user')
      
      // 清除Cookie
      clearUserCookies()
      
      // 清除Auth0相关的localStorage（如果存在）
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i)
        if (key && key.includes('auth0')) {
          localStorage.removeItem(key)
        }
      }
      
      console.log('用户已完全退出登录')
    },

    /**
     * 从服务器刷新用户信息
     * @returns {Promise<boolean>} 是否成功刷新
     */
    async refreshUserInfo() {
      if (!this.token) {
        console.log('没有token，无法刷新用户信息')
        return false
      }

      try {
        // 动态导入API函数，避免循环依赖
        const { getUserInfo } = await import('@/api/http.js')
        
        console.log('正在从服务器刷新用户信息...')
        const serverUser = await getUserInfo()
        
        if (serverUser) {
          const userData = {
            id: serverUser.uid,
            email: serverUser.email,
            name: serverUser.name,
            nickname: serverUser.nickname,
            uid: serverUser.uid,
            vip: serverUser.vip,
            vipExpiredAt: serverUser.vip_expired_at,
            loginTime: new Date().toISOString(),
          }
          
          this.setUser(userData)
          console.log('用户信息已从服务器刷新')
          return true
        } else {
          console.warn('服务器返回的用户信息为空')
          return false
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error)
        
        // 如果是401错误，说明token已过期，需要清除用户状态
        if (error.response?.status === 401 || error.message?.includes('unlogin')) {
          console.log('token已过期，清除用户状态')
          this.logout()
        }
        
        return false
      }
    },

    /**
     * 从存储中恢复用户状态
     */
    restoreUserState() {
      // 优先从localStorage恢复token
      const storedToken = localStorage.getItem('tk')
      if (storedToken) {
        this.token = storedToken
      }

      // 从Cookie恢复非敏感用户信息
      const cookieUser = getUserFromCookie()
      if (cookieUser && this.token) {
        // 只有当有token时才恢复用户信息
        this.user = {
          ...cookieUser,
          // 敏感信息不从Cookie恢复
          serverAccessToken: undefined,
          auth0IdToken: undefined
        }
        console.log('用户状态已从存储中恢复')
      } else if (!this.token) {
        // 如果没有token，清除所有状态
        this.logout()
      }
    },

    initDevice() {
      try {
        this.device = md5(navigator.userAgent + navigator.language)
      } catch (error) {
        console.error('初始化设备ID失败', error)
      }
    },
    
    checkDeviceType() {
      this.isPhone = window.innerWidth < 768
      window.addEventListener('resize', () => {
        this.isPhone = window.innerWidth < 768
      })
    },
    
    async init() {
      // 恢复用户状态
      this.restoreUserState()
      
      this.initDevice()
      this.checkDeviceType()
      
      // 如果用户已登录，从服务器刷新最新用户信息
      if (this.isLoggedIn) {
        console.log('用户已登录，正在从服务器获取最新用户信息...')
        try {
          await this.refreshUserInfo()
        } catch (error) {
          console.error('初始化时刷新用户信息失败:', error)
          // 不抛出错误，让应用继续运行，使用缓存的用户信息
        }
      }
    }
  }
})