import { createRouter, createWebHistory } from 'vue-router'
import { useAuth0 } from '@auth0/auth0-vue'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  {
    path: '/imtrans',
    name: 'Imtrans',
    component: () => import('../views/Imtrans.vue')
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('../views/Test.vue')
  },
  // {
  //   path: '/upscale',
  //   name: 'Upscale',
  //   component: () => import('../views/Upscale.vue')
  // },
  // {
  //   path: '/outpaint',
  //   name: 'Outpaint',
  //   component: () => import('../views/Outpaint.vue')
  // },
  // {
  //   path: '/chat',
  //   name: 'Chat',
  //   component: () => import('../views/Chat.vue')
  // },
  {
    path: '/capgen/login',
    name: 'CapgenLogin',
    component: () => import('../views/capgen/login.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('../views/Contact.vue')
  },
  {
    path: '/technologies',
    name: 'Technologies',
    component: () => import('../views/Technologies.vue'),
    meta: {
      title: 'Technologies',
      showHeader: true,
      showFooter: true
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: () => import('../views/Products.vue')
  },
  {
    path: '/career',
    name: 'Career',
    component: () => import('../views/Career.vue')
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('../views/Privacy.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {    
    path: '/term',
    name: 'Term',
    component: () => import('../views/Term.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen',
    name: 'CapgenProduct',
    component: () => import('../views/capgen/index.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen/pricing',
    name: 'CapgenPricing',
    component: () => import('../views/capgen/pricing.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen/download',
    name: 'CapgenDownload',
    component: () => import('../views/capgen/download.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen/signin',
    name: 'CapgenSignin',
    component: () => import('../views/capgen/signin.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen/signup',
    name: 'CapgenSignup',
    component: () => import('../views/capgen/signup.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/capgen/payment-success',
    name: 'CapgenPaymentSuccess',
    component: () => import('../views/capgen/payment-success.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  },
  // 404 页面 - 必须放在所有路由的最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/404.vue'),
    meta: {
      hideHeader: true,
      hideFooter: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (to.hash) {
      return {
        selector: to.hash,
        behavior: 'smooth' // 可选，添加平滑滚动效果
      }
    }
    return { top: 0 }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const { isAuthenticated, loginWithRedirect } = useAuth0()

  if (to.meta.requiresAuth && !isAuthenticated.value) {
    loginWithRedirect({
      appState: { target: to.fullPath } // 保存目标路由
    })
  } else {
    next()
  }
})

export default router