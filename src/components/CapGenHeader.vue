<template>
  <header class="product-header">
    <div class="container">
      <div class="header-content">
        <div class="brand-section">
          <router-link to="/capgen" class="brand-link">
            <div class="logo-icon">
              <img src="/images/logo_capgen.png" alt="CapGen Logo" />
            </div>
            <h1 class="brand-title">CapGen</h1>
          </router-link>
        </div>
        <nav class="nav-links">
          <div class="nav-items">
            <router-link to="/capgen" class="nav-link" :class="{ active: currentPage === 'product' || currentPage === '' }">Product</router-link>
            <router-link to="/capgen/pricing" class="nav-link" :class="{ active: currentPage === 'pricing' }">Pricing</router-link>
            <!-- <a href="#support" class="nav-link">Support</a> -->
            <router-link to="/capgen/download" class="nav-link" :class="{ active: currentPage === 'download' }">Download</router-link>
          </div>
          <div class="auth-buttons">
            <!-- 未登录状态 -->
            <template v-if="!isLoggedIn">
              <!-- <router-link to="/capgen/signin" class="btn btn-s btn-gray">Sign In</router-link> -->
              <button @click="handleLogin" class="btn btn-s btn-gray">Sign In</button>
            </template>
            <!-- 已登录状态 -->
            <template v-else>
              <div class="user-menu">
                <div class="user-avatar" v-if="currentUser">
                    <i class="bi bi-person"></i>
                </div>
                <div class="user-dropdown">
                  <div class="user-info">
                    <div class="user-name">{{ currentUser?.name || currentUser?.nickname }}</div>
                    <!-- <div class="user-email">{{ currentUser?.email }}</div> -->
                    <div class="user-vip">
                      <span class="vip-badge" :class="vipBadgeClass">
                        {{ vipStatusText }}
                      </span>
                      <div v-if="vipExpiredText && isUserVipActive" class="vip-expired-info">
                        {{ vipExpiredText }}
                      </div>
                    </div>
                  </div>
                  <div class="dropdown-actions">
                    <a href="#" class="dropdown-item">Profile Settings</a>
                    <a href="#" @click.prevent="" class="dropdown-item billing-item">
                      <span>Billing</span>
                    </a>
                    <button @click="handleSignOut" class="dropdown-item signout-btn">Sign Out</button>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useAuth0 } from '@auth0/auth0-vue'
import { useUserStore } from '@/store/user'
import { 
  signinWithAuth0, 
  logout, 
  getVipStatusText, 
  getVipBadgeClass,
  isVipActive,
  getVipExpiredText 
} from '@/api/auth.js'

// 获取 Auth0 实例（必须在 setup 中调用）
const auth0 = useAuth0()
const userStore = useUserStore()

// Props to customize navigation links and buttons if needed
defineProps({
  currentPage: {
    type: String,
    default: ''
  }
})

// 响应式状态
const isBillingLoading = ref(false)

// 计算属性：判断是否已登录
const isLoggedIn = computed(() => {
  console.log('isLoggedIn:', userStore.isLoggedIn)
  return userStore.isLoggedIn
})

// 计算属性：当前用户信息
const currentUser = computed(() => {
  console.log('currentUser:', userStore.getPublicUserInfo)
  return userStore.getPublicUserInfo
})

// 计算属性：VIP状态文本
const vipStatusText = computed(() => {
  return getVipStatusText(currentUser.value)
})

// 计算属性：VIP徽章样式类
const vipBadgeClass = computed(() => {
  return getVipBadgeClass(currentUser.value)
})

// 计算属性：VIP是否有效
const isUserVipActive = computed(() => {
  return isVipActive(currentUser.value)
})

// 计算属性：VIP到期时间
const vipExpiredText = computed(() => {
  return getVipExpiredText(currentUser.value)
})

// 登录处理
const handleLogin = async () => {
  try {
    console.log('开始登录...')
    await signinWithAuth0('/capgen', auth0)
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 登出处理
const handleSignOut = async () => {
  try {
    await logout(auth0, '/capgen')
  } catch (error) {
    console.error('退出失败:', error)
  }
}
</script>

<style scoped>
/* 头部导航 */
.product-header {
  position: fixed;
  z-index: 10;
  background: rgba(247, 250, 252, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 237, 245, 0.5);
  width: 100%;
  top: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 16px;
  text-decoration: none;
  color: inherit;
}

.logo-icon {
  width: 40px;
  height: 40px;
}

.logo-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.brand-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: #0D141C;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-items {
  display: flex;
  align-items: center;
  gap: 77px;
}

.nav-link {
  font-size: 14px;
  /* font-weight: 500; */
  color: #0D141C;
  text-decoration: none;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #369EFF;
}

.nav-link.active {
  /* color: #369EFF; */
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #369EFF;
  border-radius: 1px;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 2px;
  background: #369EFF;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.auth-buttons {
  margin-left: 40px;
  display: flex;
  gap: 8px;
}

/* 用户菜单样式 */
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #E5EDF5;
  transition: border-color 0.3s ease;
}
.user-avatar i{
  font-size: 24px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1b1c1e;
} 

.user-menu:hover .user-avatar {
  border-color: #369EFF;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #369EFF, #5E8BFF);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 600;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: #FFFFFF;
  border: 1px solid #E5EDF5;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.3s ease;
  z-index: 100;
}

.user-menu:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-info {
  padding: 16px;
  border-bottom: 1px solid #F0F4F8;
}

.user-name {
  font-size: 14px;
  /* font-weight: 600; */
  color: #0D141C;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: #4573A1;
  margin-bottom: 4px;
}

.user-vip {
  margin-top: 4px;
}

.vip-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vip-badge.vip-free {
  background: #F3F4F6;
  color: #6B7280;
  border: 1px solid #E5E7EB;
}

.vip-badge.vip-basic {
  background: linear-gradient(135deg, #10B981, #059669);
  color: #FFFFFF;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.vip-badge.vip-pro {
  background: linear-gradient(135deg, #8B5CF6, #7C3AED);
  color: #FFFFFF;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.vip-badge.vip-business {
  background: linear-gradient(135deg, #F59E0B, #D97706);
  color: #FFFFFF;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.vip-badge.vip-premium {
  background: linear-gradient(135deg, #EF4444, #DC2626);
  color: #FFFFFF;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.vip-badge.vip-expired {
  background: #F5F5F5;
  color: #999999;
  border: 1px solid #E0E0E0;
}

.vip-expired-info {
  font-size: 10px;
  color: #6B7280;
  margin-top: 2px;
}

.dropdown-divider {
  height: 1px;
  background: #F0F4F8;
  margin: 8px 0;
}

.dropdown-actions {
  padding: 8px 0;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 16px;
  font-size: 14px;
  color: #0D141C;
  text-decoration: none;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background: #F7FAFC;
}

.signout-btn {
  color: #DC2626;
}

.signout-btn:hover {
  background: #FEF2F2;
}

.billing-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.external-link-icon {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.billing-item:hover .external-link-icon {
  opacity: 1;
}

.billing-item.loading,
.billing-item.disabled {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-items {
    gap: 20px;
  }
  
  .nav-links {
    gap: 16px;
  }
  
  .brand-title {
    font-size: 20px;
  }
  
  .user-dropdown {
    min-width: 180px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 16px 0;
  }
  
  .nav-items {
    display: none;
  }
  
  .auth-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .user-dropdown {
    right: -20px;
    min-width: 160px;
  }
  
  .user-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #FFFFFF;
  }
}
</style> 