<template>
  <footer :class="{ 'footer-visible': isReady }">
      <div class="left-area">
          <router-link to="/">
            <img src="@/assets/images/logo/littlegrass.png" width="150" class="brand-text" alt="LITTLE GRASS" />
          </router-link>
          <span class="fw-lighter">© Copyright {{ currentYear }} Little Grass Co. LIMITED</span>
       
        <div class="social-icons justify-content-center">
          <a href="#" class="social-icon" aria-label="Discord">
            <i class="bi bi-discord"></i>
          </a>
          <a href="#" class="social-icon" aria-label="YouTube">
            <i class="bi bi-youtube"></i>
          </a>
          <a href="#" class="social-icon" aria-label="X">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#" class="social-icon" aria-label="TikTok">
            <i class="bi bi-tiktok"></i>
          </a>
          <a href="#" class="social-icon" aria-label="LinkedIn">
            <i class="bi bi-linkedin"></i>
          </a>
          <a href="#" class="social-icon" aria-label="Instagram">
            <i class="bi bi-instagram"></i>
          </a>
        </div>
      </div>

      <div class="right-area d-flex justify-content-evenly">
        <div>
          <div class="c-white-60">Company</div>
          <ul class="">
            <li class="nav-item d-flex">
              <router-link to="/" class="link-fancy">About</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/career" class="link-fancy ">Career</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/contact" class="link-fancy ">Contact</router-link>
            </li>
          </ul>
        </div>

        <div>
          <div class="c-white-60">Products</div>
          <ul class="">
            <li class="nav-item d-flex">
              <router-link to="/imtrans" class="link-fancy ">Imtrans</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/capgen" class="link-fancy ">CapGen</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/aiagent" class="link-fancy ">Assistant</router-link>
            </li>
          </ul>
        </div>

        <div>
          <div class="c-white-60">Resources</div>
          <ul class="">
            <li class="nav-item d-flex">
              <router-link to="/contact#feedback"  class="link-fancy ">Support</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/privacy" class="link-fancy ">Privacy Policy</router-link>
            </li>
            <li class="nav-item d-flex">
              <router-link to="/term" class="link-fancy ">Terms of Service</router-link>
            </li>
          </ul>
        </div>
      </div>
  </footer>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 计算当前年份
const currentYear = computed(() => new Date().getFullYear())

// 控制footer显示
const isReady = ref(false)

onMounted(() => {
  // 延迟显示footer，确保内容已加载
  setTimeout(() => {
    isReady.value = true
  }, 300)
})
</script>

<style scoped>
footer {
  width: 100%;
  background: linear-gradient(120deg, #232723 0%, #181c1b 80%);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: 40px 0px;
  margin-top: 40px;
  border-top: 1px solid #333;
}
.footer-component {
  /* background: linear-gradient(120deg, #232723 0%, #181c1b 80%); */
  /* color: #fff; */
  transition: opacity 0.5s ease;
  opacity: 0;
  visibility: hidden;
  padding-top: 3.5rem !important;
  padding-bottom: 3.5rem !important;
  margin-top: 0;
  border-top: none;
}

.footer-visible {
  opacity: 1;
  visibility: visible;
  animation: fadeIn 0.5s ease-out;
}

.left-area {
  width: 300px; /* Original fixed width for larger screens */
  flex-shrink: 0; /* Prevent shrinking on larger screens if space is tight */
  margin-right: 40px; /* Gap between left and right areas on larger screens */
  margin-left: 50px; /* Initial margin for larger screens */
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Default alignment for larger screens */
  text-align: left; /* Default text alignment for larger screens */
}
.right-area {
  width: 500px; /* Original fixed width for larger screens */
  /* flex-grow: 1; */ /* Can be used if widths are not fixed */
  margin-top: 6px;
  margin-right: 40px; /* Initial margin for larger screens */
  display: flex;
  flex-wrap: wrap; /* Should not be needed if one child .d-flex... */
  justify-content: flex-start; /* Align the child .d-flex... container */
}
.brand-text {
  /* font-size: 27px; */
  /* font-weight: w500; */
  /* margin-bottom: 2px; */
  margin-top: 16px;
  margin-left: 11px;
}

.d-flex.justify-content-evenly { /* This is the container for the three link columns inside .right-area */
  display: flex;
  gap: 64px; /* Gap between link columns on larger screens */
  justify-content: flex-start; /* Default alignment for larger screens */
}

/* 分组标题 */
.c-white-60 {
  color: #fff;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 10px;
}

/* 链接样式 */
.link-fancy {
  color: #bdbdbd;
  text-decoration: none;
  transition: color 0.2s;
  font-size: 13px;
  line-height: 2.2;
  position: relative;
}
.link-fancy:hover {
  color: var(--hover-color);
}

.link-fancy::after {
  display: none;
}

/* 社交媒体图标样式 */
.social-icons {
  margin-top: 24px;
  margin-bottom: 24px;
  display: flex;
  gap: 18px;
}
.social-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #232723;
  color: #fff;
  font-size: 20px;
  transition: background 0.2s, color 0.2s, transform 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.social-icon:hover {
  background: var(--hover-color);
  color: #181c1b;
  transform: translateY(-2px) scale(1.08);
}

.social-icon i {
  font-size: 20px;
}

/* copyright */
.fw-lighter {
  color: #bdbdbd;
  font-size: 13px;
  margin-top: 5px;
  margin-left: 7px;
}

/* 响应式布局 */
@media (max-width: 900px) {
  footer {
    flex-direction: column; /* Stack left and right areas */
    align-items: center; /* Center the stacked areas */
    padding: 30px 20px; /* Adjust padding */
  }

  .left-area {
    width: 100%; /* Allow full width for centering content */
    max-width: 400px; /* Optional: constrain max width when centered */
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 30px; /* Space below left area when stacked */
    align-items: center; /* Center content within left-area */
    text-align: center; /* Center text within left-area */
  }

  .right-area {
    width: 100%; /* Allow full width for centering content */
    max-width: 500px; /* Optional: constrain max width */
    margin-left: 0;
    margin-right: 0;
    justify-content: center; /* Center the link groups container */
  }
  
  .d-flex.justify-content-evenly { /* Container of link columns in right-area */
    flex-direction: column; /* Stack link columns */
    gap: 30px; 
    align-items: center; /* Center the link columns themselves */
  }

  .d-flex.justify-content-evenly > div { /* Each individual link column */
    text-align: center; /* Center text within each link column */
    width: 100%; /* Ensure columns can take width if needed for centering text */
  }
  
  .d-flex.justify-content-evenly > div .nav-item { /* Ensure list items also align center if they have specific display properties */
    justify-content: center;
  }

  /* The .container and .w-40pc rules from original CSS are removed here as they don't seem to apply to the current footer structure.
     If they are needed for other elements within Footer.vue that are not part of the main layout, they should be reviewed. */
}

@media (max-width: 600px) {
  footer {
    padding: 20px 15px; /* Further reduce padding */
  }

  .left-area {
    margin-bottom: 20px; /* Adjust space */
  }
  
  .brand-text {
    width: 130px; /* Adjust logo size */
    margin-top: 10px;
    margin-left: 0; /* Centered by parent's text-align:center */
  }

  .fw-lighter {
    font-size: 12px; /* Smaller copyright text */
    margin-left: 0; /* Centered by parent's text-align:center */
  }

  .social-icons {
    gap: 12px; /* Reduce gap for social icons */
    justify-content: center; /* Ensure centering if not inherited */
  }
  .social-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }
  .social-icon i {
    font-size: 18px;
  }

  .c-white-60 { /* Group titles */
    font-size: 15px;
    margin-bottom: 12px;
  }

  .link-fancy {
    font-size: 12px;
    line-height: 2.0;
  }

  .d-flex.justify-content-evenly { /* Container of link columns */
    gap: 20px; /* Adjust gap for stacked link columns */
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>