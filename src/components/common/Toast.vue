<template>
  <Teleport to="body">
    <Transition
      enter-active-class="toast-enter-active"
      leave-active-class="toast-leave-active"
      enter-from-class="toast-enter-from"
      leave-to-class="toast-leave-to"
    >
      <div
        v-if="visible"
        class="toast-container"
        :class="[`toast-${type}`, positionClass]"
      >
        <div class="toast-content">
          <div class="toast-icon">
            <i v-if="type === 'success'" class="bi bi-check-circle-fill"></i>
            <i v-else-if="type === 'error'" class="bi bi-exclamation-circle-fill"></i>
            <i v-else-if="type === 'warning'" class="bi bi-exclamation-triangle-fill"></i>
            <i v-else-if="type === 'info'" class="bi bi-info-circle-fill"></i>
          </div>
          <div class="toast-message">
            <div v-if="title" class="toast-title">{{ title }}</div>
            <div class="toast-text">{{ message }}</div>
          </div>
          <button v-if="closable" @click="close" class="toast-close">
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  message: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  duration: {
    type: Number,
    default: 4000
  },
  position: {
    type: String,
    default: 'top-right',
    validator: (value) => ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'top-center', 'bottom-center'].includes(value)
  },
  closable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close'])

const visible = ref(false)
let timer = null

const positionClass = computed(() => `toast-${props.position}`)

const show = () => {
  visible.value = true
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const close = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  setTimeout(() => {
    emit('close')
  }, 300) // 等待动画完成
}

onMounted(() => {
  show()
})

defineExpose({
  show,
  close
})
</script>

<style scoped>
.toast-container {
  position: fixed;
  z-index: 9999;
  max-width: 400px;
  min-width: 300px;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 位置样式 */
.toast-top-right {
  top: 20px;
  right: 20px;
}

.toast-top-left {
  top: 20px;
  left: 20px;
}

.toast-bottom-right {
  bottom: 20px;
  right: 20px;
}

.toast-bottom-left {
  bottom: 20px;
  left: 20px;
}

.toast-top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.toast-bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 类型样式 */
.toast-success {
  background: rgba(34, 197, 94, 0.95);
  color: white;
}

.toast-error {
  background: rgba(239, 68, 68, 0.95);
  color: white;
}

.toast-warning {
  background: rgba(245, 158, 11, 0.95);
  color: white;
}

.toast-info {
  background: rgba(59, 130, 246, 0.95);
  color: white;
}

.toast-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.toast-text {
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.95;
}

.toast-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 动画 */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

/* 特殊位置的动画 */
.toast-top-left.toast-enter-from,
.toast-bottom-left.toast-enter-from {
  transform: translateX(-100%) scale(0.95);
}

.toast-top-left.toast-leave-to,
.toast-bottom-left.toast-leave-to {
  transform: translateX(-100%) scale(0.95);
}

.toast-top-center.toast-enter-from,
.toast-bottom-center.toast-enter-from {
  transform: translateX(-50%) translateY(-20px) scale(0.95);
}

.toast-top-center.toast-leave-to,
.toast-bottom-center.toast-leave-to {
  transform: translateX(-50%) translateY(-20px) scale(0.95);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .toast-container {
    max-width: calc(100vw - 40px);
    min-width: auto;
  }
  
  .toast-top-center,
  .toast-bottom-center {
    left: 20px;
    right: 20px;
    transform: none;
  }
}
</style> 