<template>
  <header>
    <div class="navbar">
      <!-- logo标题 -->
      <div class="logo">
        <!-- <router-link class="logo-link" to="/"> -->
          <img src="@/assets/images/logo/logo_lg.png" width="27" height="27" alt="LOGO" class="logo-img"> 
          <!-- <span class="logo-text">Little Grass</span> -->
          <img src="@/assets/images/logo/littlegrass.png" width="120" height="18" alt="Little Grass" class="">
        <!-- </router-link> -->
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="menu-toggle" @click="toggleMenu">
        <div class="hamburger" :class="{ 'active': menuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- 菜单 -->
      <div class="top-menu" :class="{ 'active': menuOpen }">
        <ul class="nav-list">
          <!-- <li class="dropdown-container">
            <a href="javascript:void(0);" class="dropdown-toggle">Products</a>
            <dl class="dropdown-menu">
              <dd v-for="product in contentStore.products" :key="product.url">
                <router-link :to="product.url" class="product-item">
                  {{ product.title }}
                </router-link>
              </dd>
            </dl>
          </li> -->
          <li v-for="item in contentStore.menuItems" :key="item.path">
            <router-link :to="item.path" :class="{ 'active-link': $route.path === item.path }" @click="closeMenu">{{ item.name }}</router-link>
          </li>
        </ul>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useContentStore } from '@/store/content'

// const userStore = useUserStore()
const contentStore = useContentStore()
// const user = computed(() => userStore.user)
// const title = ref('Little Grass')
const menuOpen = ref(false)

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value
  // 当菜单打开时，禁止页面滚动
  if (menuOpen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

const closeMenu = () => {
  menuOpen.value = false
  document.body.style.overflow = ''
}

onMounted(() => {
  // userStore.init()
  // 监听窗口大小变化，在大屏幕下自动关闭菜单
  window.addEventListener('resize', () => {
    if (window.innerWidth > 768 && menuOpen.value) {
      closeMenu()
    }
  })
})
</script>

<style scoped>
.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #FFFFFF;
  font-weight: 600;
  font-size: 1.2rem;
}

.logo-img {
  margin-right: 0.5rem;
}

/* 汉堡菜单按钮 */
.menu-toggle {
  display: none;
  cursor: pointer;
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.5s ease-in-out;
}

.hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #FFFFFF;
  border-radius: 9px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger span:nth-child(1) {
  top: 0px;
}

.hamburger span:nth-child(2) {
  top: 8px;
}

.hamburger span:nth-child(3) {
  top: 16px;
}

.hamburger.active span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.hamburger.active span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* 用户区域样式 */
.user-section {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }
  
  .menu-toggle {
    display: block;
    z-index: 1001;
  }
  
  .top-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(8, 8, 8, 0.95);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1000;
  }
  
  .top-menu.active {
    transform: translateX(0);
  }
  
  .nav-list {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
  
  .nav-list li a {
    font-size: 1.2rem;
  }
  
  .user-section {
    margin-left: 0;
    margin-top: 2rem;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 1rem;
  }
  
  .navbar {
    padding: 0.8rem;
  }
}
</style>