<template>
  <div class="position-relative d-flex" :id="id">
    <img class="position-absolute newimg" :src="beforeImage" :alt="alt" width="100%">
    <canvas class="position-absolute" :id="'canvas-' + id"></canvas>
    <div class="w-40-px h-100 position-absolute d-flex point" :style="{ left: pointPosition + 'px' }">
      <span class="e-point"></span>
    </div>
    <div class="w-100 h-100 position-absolute d-flex justify-content-between padding-8-21">
      <img src="/img/index/before.png" width="87" height="30" alt="before">
      <img src="/img/index/after.png" width="87" height="30" alt="after">
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  beforeImage: {
    type: String,
    required: true
  },
  afterImage: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: 'image'
  },
  fullWidth: {
    type: Boolean,
    default: false
  }
})

const pointPosition = ref(0)
let canvas = null
let ctx = null
let clipImage = null
let imgWidth = 0
let imgHeight = 0
let node = null

onMounted(() => {
  initializeCompare()
})

onUnmounted(() => {
  if (node) {
    node.removeEventListener('mousemove', handleMouseMove)
    node.removeEventListener('mouseleave', handleMouseLeave)
  }
})

function initializeCompare() {
  node = document.getElementById(props.id)
  if (!node) return
  
  const img = node.getElementsByClassName('newimg')[0]
  const point = node.getElementsByClassName('point')[0]
  canvas = document.getElementById('canvas-' + props.id)
  
  clipImage = new Image()
  clipImage.src = props.afterImage
  
  clipImage.onload = function() {
    ctx = canvas.getContext('2d')
    imgWidth = img.width
    imgHeight = img.height
    
    // 初始位置设为中间
    pointPosition.value = imgWidth / 2
    
    canvas.width = imgWidth / 2
    canvas.height = imgHeight
    
    if (props.fullWidth) {
      ctx.drawImage(clipImage, 0, 0, imgWidth, imgHeight)
    } else {
      ctx.drawImage(clipImage, 0, 0)
    }
    
    // 添加鼠标移动事件
    node.addEventListener('mousemove', handleMouseMove)
    node.addEventListener('mouseleave', handleMouseLeave)
  }
}

function handleMouseMove(event) {
  const rect = event.currentTarget.getBoundingClientRect()
  let offsetX = event.clientX - rect.left
  
  if (offsetX > imgWidth) {
    offsetX = imgWidth
  }
  
  canvas.width = offsetX
  canvas.height = imgHeight
  
  ctx.translate(0, 0)
  
  if (props.fullWidth) {
    ctx.drawImage(clipImage, 0, 0, imgWidth, imgHeight)
  } else {
    ctx.drawImage(clipImage, 0, 0, imgWidth, imgHeight)
  }
  
  pointPosition.value = offsetX
}

function handleMouseLeave(event) {
  const rect = event.currentTarget.getBoundingClientRect()
  let offsetX = event.clientX - rect.left
  
  if (offsetX < -30 || offsetX > imgWidth + 30) {
    offsetX = imgWidth / 2
    canvas.width = offsetX
    canvas.height = imgHeight
    
    if (props.fullWidth) {
      ctx.drawImage(clipImage, 0, 0, imgWidth, imgHeight)
    } else {
      ctx.drawImage(clipImage, 0, 0)
    }
    
    pointPosition.value = offsetX
  }
}
</script>