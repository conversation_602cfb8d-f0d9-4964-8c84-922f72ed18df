import { post, get } from './http.js';

export async function getProducts() {
    const serverResponse = await get('/ai-subtitle/payment/products')
    return serverResponse
}

export async function createPayment(product_id, success_url) {
    console.log('createPayment 开始执行:', {
        product_id,
        success_url,
        timestamp: new Date().toISOString()
    })
    
    const origin = (typeof window !== 'undefined' && window.location && window.location.origin) ? window.location.origin : ''
    const data = {}
    data.channel = 'stripe'
    data.type = 'vip_subscription'
    data.success_url = success_url || `${origin}/capgen/payment-success?status=success`
    data.cancel_url = `${origin}/capgen/payment-success?status=cancelled`
    data.product_id = product_id
    
    console.log('准备发送支付创建请求:', {
        url: '/ai-subtitle/payment/create',
        data: data,
        timestamp: new Date().toISOString()
    })
    
    try {
        const serverResponse = await post('/ai-subtitle/payment/create', data)
        console.log('支付创建API响应:', {
            code: serverResponse.code,
            msg: serverResponse.msg,
            data: serverResponse.data,
            timestamp: new Date().toISOString()
        })
        
        if (serverResponse.code === 0 && serverResponse.data && serverResponse.data.pay_url) {
            console.log('支付订单创建成功:', {
                pay_url: serverResponse.data.pay_url,
                pay_id: serverResponse.data.pay_id,
                third_pay_id: serverResponse.data.third_pay_id,
                timestamp: new Date().toISOString()
            })
            return {
                success: true,
                pay_url: serverResponse.data.pay_url,
                pay_id: serverResponse.data.pay_id,
                third_pay_id: serverResponse.data.third_pay_id
            }
        } else {
            console.error('支付订单创建失败 - 服务器返回错误:', {
                code: serverResponse.code,
                msg: serverResponse.msg,
                data: serverResponse.data,
                timestamp: new Date().toISOString()
            })
            return {
                success: false,
                error: serverResponse.msg || 'Failed to create payment order'
            }
        }
    } catch (error) {
        console.error('支付创建API调用异常:', {
            error: error.message,
            stack: error.stack,
            product_id,
            success_url,
            timestamp: new Date().toISOString()
        })
        throw error
    }
}

export async function queryPaymentStatus(pay_id) {
    try {
        const serverResponse = await get(`/ai-subtitle/payment/query?pay_id=${pay_id}`)
        
        if (serverResponse.code === 0 && serverResponse.data) {
            return {
                success: true,
                status: serverResponse.data.status,
                pay_id: serverResponse.data.pay_id,
                // status=1为支付成功
                isPaid: serverResponse.data.status === 1
            }
        } else {
            return {
                success: false,
                error: serverResponse.msg || 'Failed to query payment status'
            }
        }
    } catch (error) {
        return {
            success: false,
            error: error.message || 'Failed to query payment status'
        }
    }
}