import axios from 'axios'
import { useUserStore } from '@/store/user'

// API基础配置
export const API_BASE_URL = 'https://api.littlegrass.cc'
export const API_BASE_URL_TEST = 'https://api-test.littlegrass.cc'

// 创建axios实例
const http = axios.create({
  baseURL: API_BASE_URL_TEST,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

export default {
  get,
  post,
  upload,
  // 认证相关
  verifyAuth0Token,
  getUserInfo,

  // 反馈相关
  submitFeedback
}

// 请求拦截器
http.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    const token = userStore.token
    
    console.log('HTTP请求拦截器 - 准备发送请求:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      headers: config.headers,
      data: config.data,
      params: config.params,
      hasToken: !!token,
      timestamp: new Date().toISOString()
    })
    
    // 如果请求头中没有Authorization且store中有token，添加到请求头
    if (!config.headers['Authorization'] && token) {
      config.headers['Authorization'] = `Bearer ${token}`
      console.log('已添加认证token到请求头')
    }
    
    return config
  },
  error => {
    console.error('HTTP请求拦截器错误:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    })
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    const res = response.data
    
    console.log('HTTP响应拦截器 - 收到响应:', {
      url: response.config.url,
      method: response.config.method,
      status: response.status,
      statusText: response.statusText,
      responseData: res,
      timestamp: new Date().toISOString()
    })
    
    // 如果返回的状态码不是200，说明接口请求有误
    if (res.code !== 0) {
      console.error('HTTP响应拦截器 - 业务逻辑错误:', {
        code: res.code,
        msg: res.msg,
        url: response.config.url,
        timestamp: new Date().toISOString()
      })
      
      // 登录超时或未登录
      if (res.code === -1 && res.msg === 'unlogin') {
        console.log('检测到登录超时，执行登出操作')
        const userStore = useUserStore()
        userStore.logout()
        // 可以在这里添加路由跳转到登录页
      }
      
      // 显示错误消息
      console.error(res.msg || '请求失败')
      
      return Promise.reject(new Error(res.msg || '请求失败'))
    } else {
      console.log('HTTP响应拦截器 - 请求成功:', {
        code: res.code,
        url: response.config.url,
        timestamp: new Date().toISOString()
      })
      return res
    }
  },
  error => {
    console.error('HTTP响应拦截器 - 网络错误:', {
      error: error.message,
      stack: error.stack,
      config: error.config,
      response: error.response,
      request: error.request,
      timestamp: new Date().toISOString()
    })
    return Promise.reject(error)
  }
)

// 封装GET请求
export function get(url, params) {
  return http.get(url, { params })
}

// 封装POST请求
export function post(url, data, config = {}) {
  return http.post(url, data, config)
}

// 封装文件上传请求
export function upload(url, file, onProgress) {
  const formData = new FormData()
  formData.append('file', file)
  
  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: progressEvent => {
      if (onProgress && progressEvent.total) {
        const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentage)
      }
    }
  })
}

// ===== 认证相关API =====
/**
 * 校验Auth0 token并获取服务器端用户信息
 * @param {string} idToken - Auth0 id token
 * @returns {Promise<string>} access_token
 */
export async function verifyAuth0Token(idToken) {
  const serverResponse = await post('/ai-subtitle/user/login_auth0', {
    auth0_access_token: idToken,
    device_id: '',
    // udid: '1'
  })
  console.log('serverResponse:', serverResponse)
  return serverResponse.data.access_token
}

/**
 * 获取用户信息
 * @returns {Promise<object>} 用户信息响应
 */
export async function getUserInfo() {
  const serverResponse = await get('/ai-subtitle/user/info')
  return serverResponse.user
}

// 反馈相关
export function submitFeedback(data) {
  return post('/user/feedback', data)
}