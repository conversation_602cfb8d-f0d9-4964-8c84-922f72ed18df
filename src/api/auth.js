// 统一的认证API - 集中所有登录相关功能
import { verifyAuth0Token, getUserInfo } from './http.js'
import { getUserFromCookie, isLoggedInFromCookie } from '@/utils/cookies.js'
import { useUserStore } from '@/store/user'

// ===== Auth0相关功能 =====

/**
 * Auth0登录
 * @param {string} returnUrl - 登录成功后的跳转地址
 */
export const signinWithAuth0 = async (returnUrl = '/capgen', auth0Instance) => {
  try {
    const auth0 = auth0Instance
    await auth0.loginWithRedirect({
      appState: { target: returnUrl }
    })
  } catch (error) {
    console.error('Auth0登录失败:', error)
    throw error
  }
}

/**
 * Auth0注册
 * @param {string} returnUrl - 注册成功后的跳转地址
 */
export const signupWithAuth0 = async (returnUrl = '/capgen', auth0Instance) => {
  try {
    const auth0 = auth0Instance
    await auth0.loginWithRedirect({
      authorizationParams: {
        screen_hint: 'signup'
      },
      appState: { target: returnUrl }
    })
  } catch (error) {
    console.error('Auth0注册失败:', error)
    throw error
  }
}

/**
 * Google登录（通过Auth0）
 * @param {string} returnUrl - 登录成功后的跳转地址
 */
export const signinWithGoogle = async (returnUrl = '/capgen', auth0Instance) => {
  try {
    const auth0 = auth0Instance
    await auth0.loginWithRedirect({
      authorizationParams: {
        connection: 'google-oauth2'
      },
      appState: { target: returnUrl }
    })
  } catch (error) {
    console.error('Google登录失败:', error)
    throw error
  }
}

/**
 * Auth0退出登录
 * @param {string} returnUrl - 退出后的跳转地址
 */
export const signoutWithAuth0 = async (returnUrl = '/capgen', auth0Instance) => {
  try {
    // 先清除本地状态
    const userStore = useUserStore()
    userStore.logout()
    
    // 然后执行Auth0退出
    const auth0 = auth0Instance
    await auth0.logout({
      logoutParams: {
        returnTo: window.location.origin + returnUrl
      }
    })
  } catch (error) {
    console.error('Auth0退出登录失败:', error)
    throw error
  }
}

/**
 * 处理Auth0回调 - 完整的登录处理流程
 * @returns {Promise<object>} 处理结果
 */
export const handleAuth0Callback = async (auth0Instance) => {
  try {
    console.log('开始处理Auth0登录回调...')
    
    const auth0 = auth0Instance
    
    // 等待用户信息加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    const user = auth0.user
    const isAuthenticated = auth0.isAuthenticated
    
    if (!isAuthenticated || !user) {
      throw new Error('登录验证失败，未能获取用户信息')
    }

    try {
      console.log('Auth0用户信息:', user)
      
      // 1. 获取Auth0 ID Token
      const tokenResponse = await auth0Instance.getAccessTokenSilently({
        detailedResponse: true,
      })
      const idToken = tokenResponse.id_token
      
      // 2. 调用服务器登录接口获取服务器端access_token
      const serverAccessToken = await verifyAuth0Token(idToken)
      
      // 3. 获取用户详细信息
      let serverUser = null
      // console.log('serverAccessToken:', serverAccessToken)
      if (serverAccessToken) {
        const userStore = useUserStore()
        await userStore.setToken(serverAccessToken)
        
        // 获取服务器端用户信息
        const serverUserResponse = await getUserInfo()
        serverUser = serverUserResponse

        console.log('服务器用户信息:', serverUser)

        if (serverUser !== null) {
          const userData = {
            id: serverUser.uid,
            name: serverUser.name,
            nickname: serverUser.nickname,
            vip: serverUser.vip,
            vipExpiredAt: serverUser.vip_expired_at,
            loginTime: new Date().toISOString(),
          }

          await userStore.setUser(userData)
          console.log('登录成功')

          return { success: true, user: userData }
        } else {
          return { success: false, error: '服务器用户信息为空' }
        }
      } else {
        return { success: false, error: '服务器校验失败' }
      }
    } catch (serverError) {
        console.warn('服务器校验失败:', serverError)
        return { success: false, error: '服务器校验失败' }
    }
  } catch (error) {
    console.error('Auth0回调处理失败:', error)
    return { success: false, error }
  }
}

// ===== 用户状态管理 =====

/**
 * 获取当前用户信息（优先Store，备选Cookie）
 * @param {object} auth0 - useAuth0()返回的对象（可选）
 * @returns {object|null} 用户信息
 */
export const getCurrentUser = (auth0 = null) => {
  // 优先使用Store中的用户信息
  const userStore = useUserStore()
  if (userStore.user) {
    return userStore.getPublicUserInfo
  }
  
  // 如果Store中没有，且Auth0已登录，从Auth0获取基础信息
  if (auth0 && auth0.isAuthenticated.value && auth0.user.value) {
    const user = auth0.user.value
    return {
      id: user.sub,
      email: user.email,
      name: user.name || user.nickname,
      avatar: user.picture,
      provider: 'auth0'
    }
  }
  
  // 最后尝试从Cookie获取
  const cookieUser = getUserFromCookie()
  if (cookieUser) {
    return cookieUser
  }
  
  return null
}

/**
 * 检查用户是否已登录
 * @param {object} auth0 - useAuth0()返回的对象（可选）
 * @returns {boolean} 是否已登录
 */
export const isUserLoggedIn = (auth0 = null) => {
  // 优先检查Store状态（有token才算真正登录）
  const userStore = useUserStore()
  if (userStore.isLoggedIn) {
    return true
  }
  
  // 备选检查Auth0状态
  const auth0LoggedIn = auth0 ? auth0.isAuthenticated.value : false
  const cookieLoggedIn = isLoggedInFromCookie()
  
  return auth0LoggedIn || cookieLoggedIn
}

/**
 * 完整退出登录 - 处理所有状态清理
 * @param {object} auth0Instance - Auth0实例（可选）
 * @param {string} returnUrl - 退出后跳转地址
 */
export const logout = async (auth0Instance = null, returnUrl = '/capgen') => {
  try {
    // 1. 清除Store状态
    const userStore = useUserStore()
    userStore.logout()
    
    // 2. 如果有Auth0实例，执行Auth0退出
    if (auth0Instance) {
      await auth0Instance.logout({
        logoutParams: {
          returnTo: window.location.origin + returnUrl
        }
      })
    }
    
    console.log('用户已完全退出登录')
    return { success: true }
  } catch (error) {
    console.error('退出登录失败:', error)
    return { success: false, error }
  }
}

// ===== VIP状态相关 =====

/**
 * 获取VIP状态文本
 * @param {object} user - 用户信息
 * @returns {string} VIP状态文本
 */
export const getVipStatusText = (user) => {
  if (!user?.vip || user.vip === 0) return 'Free'
  
  // 根据VIP等级返回不同状态
  switch (user.vip) {
    case 1:
      return 'Basic'
    case 2: 
      return 'Pro'
    case 3:
      return 'Business'
    default:
      if (user.vip > 0) {
        return 'Premium'
      }
      return 'Free'
  }
}

/**
 * 获取VIP徽章样式类
 * @param {object} user - 用户信息
 * @returns {string} VIP徽章样式类
 */
export const getVipBadgeClass = (user) => {
  if (!user?.vip || user.vip === 0) return 'vip-free'
  
  // 检查是否过期
  if (user.vipExpiredAt) {
    const expiredDate = new Date(user.vipExpiredAt)
    const now = new Date()
    if (expiredDate <= now) {
      return 'vip-expired'
    }
  }
  
  // 根据VIP等级返回不同样式
  switch (user.vip) {
    case 1:
      return 'vip-basic'
    case 2:
      return 'vip-pro'
    case 3:
      return 'vip-business'
    default:
      return 'vip-premium'
  }
}

/**
 * 检查用户VIP是否有效
 * @param {object} user - 用户信息
 * @returns {boolean} VIP是否有效
 */
export const isVipActive = (user) => {
  if (!user?.vip || user.vip === 0) return false
  
  if (user.vipExpiredAt) {
    const expiredDate = new Date(user.vipExpiredAt)
    const now = new Date()
    return expiredDate > now
  }
  
  return true
}

/**
 * 获取VIP到期时间文本
 * @param {object} user - 用户信息
 * @returns {string} 到期时间文本
 */
export const getVipExpiredText = (user) => {
  if (!user?.vip || user.vip === 0) return ''
  
  if (!user.vipExpiredAt) return ''
  
  const expiredDate = new Date(user.vipExpiredAt)
  const now = new Date()
  
  if (expiredDate <= now) {
    return 'Expired'
  }
  
  const diffTime = expiredDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays <= 3) {
    return `Expires in ${diffDays} day${diffDays > 1 ? 's' : ''}`
  } else if (diffDays <= 30) {
    return `Expires in ${diffDays} days`
  } else {
    return expiredDate.toLocaleDateString('en-US')
  }
}

// ===== 错误处理相关 =====

/**
 * 处理认证错误
 * @param {Error} error - 错误对象
 * @returns {string} 用户友好的错误消息
 */
export const handleAuthError = (error) => {
  console.error('认证错误:', error)
  
  // Auth0特定错误
  if (error.error === 'access_denied') {
    return '登录被取消'
  } else if (error.error === 'invalid_state') {
    return '登录状态验证失败，请重新登录'
  } else if (error.error_description) {
    return error.error_description
  } else if (error.message?.includes('Invalid state')) {
    return '登录状态验证失败，请清除浏览器缓存后重试'
  } else {
    return '登录失败：' + (error.message || '未知错误')
  }
}

/**
 * 重试登录（清除缓存）
 * @param {object} router - Vue Router实例
 * @param {string} loginPath - 登录页面路径
 */
export const retryLogin = (router, loginPath = '/capgen/signin') => {
  // 使用store的logout方法清除所有状态
  const userStore = useUserStore()
  userStore.logout()
  
  // 跳转到登录页面
  router.push(loginPath)
}