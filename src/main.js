import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'
import { MotionPlugin } from '@vueuse/motion'
import { createAuth0 } from '@auth0/auth0-vue'

import App from './App.vue'
import router from './router'
import { getAuth0Config } from './config/auth0.js'
import { useUserStore } from './store/user.js'

// 创建实例
const pinia = createPinia()
const head = createHead()

// 创建Vue应用实例
const app = createApp(App)

// 使用插件
app.use(pinia)
app.use(router)
app.use(head)
app.use(MotionPlugin)

// 配置Auth0
const auth0Config = getAuth0Config()
app.use(createAuth0(auth0Config))

// 初始化用户状态
const userStore = useUserStore()
userStore.init().catch(error => {
  console.error('用户状态初始化失败:', error)
})

// 挂载应用
app.mount('#app')