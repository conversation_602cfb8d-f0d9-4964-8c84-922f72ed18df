/* 平板端响应式样式 */

/* 小型平板设备 (576px 至 767px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  /* 排版调整 */
  h1 {
    font-size: 2.25rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  /* 容器调整 */
  .container {
    max-width: 540px;
  }
  
  /* 导航调整 */
  .navbar-brand {
    margin-right: 0.5rem;
  }
  
  .navbar-logo {
    height: 35px;
  }
  
  /* 布局调整 */
  .row-sm {
    display: flex;
    flex-wrap: wrap;
  }
  
  /* 首页特定样式 */
  .hero-section {
    padding: 3rem 0;
  }
  
  .hero-title {
    font-size: 2.25rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .section-padding {
    padding: 3rem 0;
  }
  
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  /* 卡片调整 */
  .card-group .card {
    flex: 0 0 calc(50% - 1rem);
  }
  
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 按钮调整 */
  .btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* 中型平板设备 (768px 至 991px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* 排版调整 */
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  /* 容器调整 */
  .container {
    max-width: 720px;
  }
  
  /* 导航调整 */
  .navbar-nav {
    display: flex;
  }
  
  .navbar-toggler {
    display: none;
  }
  
  .nav-item {
    margin: 0 0.5rem;
  }
  
  /* 布局调整 */
  .row-md {
    display: flex;
    flex-wrap: wrap;
  }
  
  /* 首页特定样式 */
  .hero-section {
    padding: 4rem 0;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .section-padding {
    padding: 4rem 0;
  }
  
  .feature-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  /* 工具展示区域 */
  .tools-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  /* 卡片调整 */
  .card-md {
    padding: 1.5rem;
  }
  
  /* 表单调整 */
  .form-group-md {
    margin-bottom: 1.25rem;
  }
  
  /* 底部调整 */
  .footer-col {
    margin-bottom: 1.5rem;
  }
  
  /* 间距调整 */
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
}

/* 平板和小型桌面设备共享样式 */
@media (min-width: 576px) and (max-width: 991.98px) {
  /* 通用调整 */
  body {
    font-size: 0.95rem;
  }
  
  /* 按钮调整 */
  .btn {
    padding: 0.5rem 1.25rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
  }
  
  .btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
  }
  
  /* 图片调整 */
  .img-fluid-tablet {
    max-width: 90%;
    height: auto;
  }
  
  /* 间距调整 */
  .section-spacing {
    margin-bottom: 3rem;
  }
  
  /* 文本调整 */
  .text-tablet-center {
    text-align: center !important;
  }
  
  .text-tablet-left {
    text-align: left !important;
  }
  
  .text-tablet-right {
    text-align: right !important;
  }
  
  /* 显示调整 */
  .d-tablet-none {
    display: none !important;
  }
  
  .d-tablet-block {
    display: block !important;
  }
  
  .d-tablet-flex {
    display: flex !important;
  }
}