/* 移动端响应式样式 */
@media screen and (max-width: 768px) {
  /* 根变量 */
  :root {
    --w: tan(atan2(var(--_w), 1px));
  }

  /* 排版调整 */
  .f-56 {
    font-size: 28px;
  }

  .l-h-68 {
    line-height: 34px;
  }

  .f-20 {
    font-size: 14px;
  }

  .l-h-32 {
    line-height: 20px;
  }

  /* 尺寸调整 */
  .h-139px {
    height: 80px;
  }

  .w-539px {
    width: 90%;
  }

  .padding-w7 {
    padding: 5vw;
  }

  .w-601px {
    width: 95%;
  }

  .w-80px {
    width: 100%;
    height: 40px !important;
  }

  /* 导航调整 */
  .navbar-nav {
    flex-direction: column;
    width: 100%;
  }

  .navbar-collapse {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--dark-bg);
    z-index: 1000;
    padding: 2rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .navbar-collapse.show {
    transform: translateX(0);
  }

  /* 布局调整 */
  .container {
    padding: 0 10px;
  }

  .row {
    margin-left: -5px;
    margin-right: -5px;
  }
  .hero-subtitle {
      font-size: 0.9em;
      margin-bottom: 2rem;
      max-width: 80%;
  }
  .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
  .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    padding-left: 5px;
    padding-right: 5px;
  }

  /* 隐藏和显示元素 */
  .d-none-mobile {
    display: none !important;
  }

  .d-block-mobile {
    display: block !important;
  }

  /* 按钮调整 */
  .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .btn-lg {
    padding: 0.6rem 1.2rem;
    font-size: 1.1rem;
  }

  /* 表单调整 */
  .form-control {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }

  /* 间距调整 */
  .mt-5, .my-5 {
    margin-top: 2rem !important;
  }

  .mb-5, .my-5 {
    margin-bottom: 2rem !important;
  }

  .pt-5, .py-5 {
    padding-top: 2rem !important;
  }

  .pb-5, .py-5 {
    padding-bottom: 2rem !important;
  }
  .hero-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
}