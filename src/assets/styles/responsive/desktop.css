/* 桌面端响应式样式 */

/* 中等屏幕设备 (平板电脑, 768px 及以上) */
@media (min-width: 768px) {
  /* 排版调整 */
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  /* 容器调整 */
  .container {
    max-width: 720px;
  }
  
  /* 导航调整 */
  .navbar-nav {
    display: flex;
  }
  
  .navbar-toggler {
    display: none;
  }
  
  /* 布局调整 */
  .row {
    margin-left: -15px;
    margin-right: -15px;
  }
  
  .col, .col-md {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  /* 首页特定样式 */
  .hero-content {
    text-align: left;
    padding: 4rem 0;
  }
  
  .hero-image {
    display: block;
  }
  
  .feature-item {
    margin-bottom: 0;
  }
  .hero-title {
    font-size: 4rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }
}

/* 大屏幕设备 (桌面电脑, 992px 及以上) */
@media (min-width: 992px) {
  /* 排版调整 */
  h1 {
    font-size: 3rem;
  }
  
  h2 {
    font-size: 2.25rem;
  }
  
  /* 容器调整 */
  .container {
    max-width: 960px;
  }
  
  /* 导航调整 */
  .nav-item {
    margin: 4px 0;
  }
  
  /* 布局调整 */
  .row-lg {
    display: flex;
    flex-wrap: wrap;
  }
  
  /* 首页特定样式 */
  .hero-section {
    padding: 6rem 0;
  }
  
  .section-padding {
    padding: 5rem 0;
  }
  
  .feature-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 超大屏幕设备 (大型桌面电脑, 1200px 及以上) */
@media (min-width: 1200px) {
  /* 容器调整 */
  .container {
    max-width: 1140px;
  }
  
  /* 排版调整 */
  h1 {
    font-size: 3.5rem;
  }
  
  /* 布局调整 */
  .container-xl {
    max-width: 1320px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* 首页特定样式 */
  .hero-title {
    font-size: 100px;
  }
  
  .hero-subtitle {
    font-size: 1.5rem;
  }
  
  /* 工具展示区域 */
  .tools-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
  
  /* 卡片调整 */
  .card-xl {
    padding: 2rem;
  }
}

/* 超宽屏幕设备 (1400px 及以上) */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
  
  .container-xxl {
    max-width: 1500px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* 特殊布局调整 */
  .wide-section {
    padding-left: calc((100% - 1320px) / 2);
    padding-right: calc((100% - 1320px) / 2);
  }
}