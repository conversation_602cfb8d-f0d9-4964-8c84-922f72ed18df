/* Term页面样式 */

.term-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 20px;
  background-color: #111;
  color: #eee;
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

h2 {
  font-size: 2.5rem;
  color: #fff;
  margin-bottom: 1.5rem;
  text-align: center;
}

h3 {
  font-size: 1.5rem;
  color: #fff;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

h4 {
  font-size: 1.2rem;
  color: #fff;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
}

p {
  margin-bottom: 1rem;
  color: #ccc;
  font-size: 1rem;
}

ul {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
  color: #ccc;
}

li {
  margin-bottom: 0.5rem;
}

strong {
  color: #fff;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .term-container {
    padding: 20px 15px;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
  
  h4 {
    font-size: 1.1rem;
  }
  
  p, li {
    font-size: 0.95rem;
  }
}