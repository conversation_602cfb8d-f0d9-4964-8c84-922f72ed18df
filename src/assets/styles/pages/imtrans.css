/* Imtrans页面样式 */

/* 主要内容区域 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 顶部间距 */
.h-139px {
  height: 139px;
}

.h-80-px {
  height: 80px;
}

/* 布局类 */
.web-w-50 {
  width: 50%;
}

.web-align-items-end {
  align-items: flex-end;
}

.web-align-items-start {
  align-items: flex-start;
}

.web-w-537px {
  width: 537px;
}

.web-h-620px {
  height: 620px;
}

.phone-h-60px {
  height: 60px;
}

.f-50 {
  font-size: 50px;
}

.font-weight-800 {
  font-weight: 800;
}

.h-20-px {
  height: 20px;
}

.web-w-60 {
  width: 60%;
}

.f-20 {
  font-size: 20px;
}

.font-weight-400 {
  font-weight: 400;
}

.web-text-start {
  text-align: start;
}

.w-100 {
  width: 100%;
}

.h-30-px {
  height: 30px;
}

.web-h-120px {
  height: 120px;
}

/* 窗口标题部分 */
.w-90 {
  width: 90%;
}

.margin-left-p5 {
  margin-left: 5%;
}

.padding-top-30 {
  padding-top: 30px;
}

.w-50 {
  width: 50%;
}

.margin-lr-25 {
  margin-left: 25%;
  margin-right: 25%;
}

.text-center {
  text-align: center;
}

.f-40 {
  font-size: 40px;
}

.font-weight-700 {
  font-weight: 700;
}

.padding-10-0 {
  padding: 10px 0;
}

.margin-top-20 {
  margin-top: 20px;
}

.c-ccc {
  color: #ccc;
}

.margin-bottom-20 {
  margin-bottom: 20px;
}

.line-height-30 {
  line-height: 30px;
}

.font-weight-500 {
  font-weight: 500;
}

/* 工作原理部分 */
.h-60-px {
  height: 60px;
}

.c-white {
  color: white;
}

.font-16 {
  font-size: 16px;
}

.w-45 {
  width: 45%;
}

.font-size-50 {
  font-size: 50px;
}

.margin-top-40 {
  margin-top: 40px;
}

.bg-white {
  background-color: white;
}

.c-black {
  color: black;
}

.border-radio-30 {
  border-radius: 30px;
}

.font-style-italic {
  font-style: italic;
}

.f-18 {
  font-size: 18px;
}

/* 支持的语言部分 */
.bg-black {
  background-color: black;
}

.ul-1 {
  list-style: none;
  padding: 0;
}

.padding-30-100 {
  padding: 30px 100px;
}

.border-radius-10 {
  border-radius: 10px;
}

.font-weight {
  font-weight: bold;
}

.font-18 {
  font-size: 18px;
}

.line-height-40 {
  line-height: 40px;
}

.font-24 {
  font-size: 24px;
}

.padding-0-10 {
  padding: 0 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .web-w-50 {
    width: 100%;
  }
  
  .web-w-537px,
  .web-h-620px {
    width: 100%;
    height: auto;
  }
  
  .web-w-60 {
    width: 90%;
  }
  
  .web-text-start {
    text-align: center;
  }
  
  .web-h-120px {
    height: 60px;
  }
  
  .w-50,
  .margin-lr-25 {
    width: 90%;
    margin-left: 5%;
    margin-right: 5%;
  }
  
  .w-45 {
    width: 90%;
    margin-bottom: 20px;
  }
  
  .padding-30-100 {
    padding: 20px 30px;
  }
}