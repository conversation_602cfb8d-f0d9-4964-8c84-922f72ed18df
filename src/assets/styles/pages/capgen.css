/* CapGen 页面通用样式 */

.capgen-product-page,
.capgen-pricing-page {
  background-color: #F7FAFC;
  color: #0D141C;
  min-height: 100vh;
  overflow-x: hidden;
}

.top-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 998px;
  background: url("/images/capgen/bg.svg") no-repeat center center;
  z-index: 0;
}

/* 头部导航样式已移至 CapGenHeader 组件内 */

/* CapGen 页面特定按钮覆盖 - 仅在 CapGen 页面内生效 */

.capgen-product-page .btn-primary:hover,
.capgen-pricing-page .btn-primary:hover {
  transform: translateY(0px);
  color: #FFFFFF;
}

.capgen-product-page .btn-outline,
.capgen-pricing-page .btn-outline {
  background: #E5EDF5;
  color: #0D141C;
  border: 1px solid #CCDBEB;
}

.capgen-product-page .btn-outline:hover,
.capgen-pricing-page .btn-outline:hover {
  background: #D1E3F3;
  border-color: #369EFF;
  transform: translateY(-1px);
}

/* 主标题区域 */
.hero-section,
.pricing-hero-section {
  position: relative;
  padding: 140px 0 80px;
  z-index: 1;
  text-align: center;
}

.pricing-hero-section {
  padding: 120px 0 80px;
}

.hero-content,
.pricing-hero-content {
  max-width: 900px;
  margin: 0 auto;
}

.pricing-hero-content {
  max-width: 800px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  margin-bottom: 24px;
}

.badge-text {
  background: linear-gradient(135deg, #369EFF, #5E8BFF);
  color: #FFFFFF;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.hero-title,
.pricing-hero-title {
  font-size: 56px;
  font-weight: 900;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: #0D141C;
  margin-bottom: 24px;
}

.pricing-hero-title {
  font-size: 48px;
  letter-spacing: -0.04em;
  color: #111112;
}

.hero-description,
.pricing-hero-description {
  font-size: 16px;
  line-height: 24px;
  color: #111112;
  margin-bottom: 40px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.pricing-hero-description {
  font-size: 18px;
  line-height: 1.6;
  color: #4573A1;
  margin-bottom: 48px;
  max-width: 600px;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.hero-stats {
  display: flex;
  gap: 120px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  margin-bottom: 100px;
}

.stat-number {
  display: block;
  font-size: 50px;
  font-weight: 900;
  background: linear-gradient(135deg, #68C0FF, #333BC8);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  color: #4573A1;
  line-height: 24px;
  font-weight: 500;
}

/* 计费周期切换 - 定价页面特有 */
.billing-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.toggle-container {
  display: flex;
  background: #E5EDF5;
  border-radius: 12px;
  padding: 4px;
  position: relative;
}

.toggle-option {
  position: relative;
  padding: 12px 24px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #4573A1;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-option.active {
  background: #FFFFFF;
  color: #0D141C;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.save-badge {
  background: #369EFF;
  color: #FFFFFF;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 700;
}

/* 视频演示区域 */
.demo-section {
  padding: 80px 0;
  background: rgba(255, 255, 255, 0.5);
}

.demo-content {
  text-align: center;
}

.demo-video {
  max-width: 800px;
  margin: 0 auto;
}

.video-placeholder {
  background: linear-gradient(135deg, #F0F4F8, #E5EDF5);
  border-radius: 16px;
  padding: 80px 40px;
  position: relative;
  border: 2px solid #E5EDF5;
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-placeholder:hover {
  border-color: #369EFF;
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
}

.play-button {
  width: 80px;
  height: 80px;
  background: #369EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #FFFFFF;
  transition: all 0.3s ease;
}

.video-placeholder:hover .play-button {
  background: #2563EB;
  transform: scale(1.1);
}

.loading-spinner {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #369EFF;
}

.loading-spinner svg {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.demo-text {
  font-size: 16px;
  color: #4573A1;
  font-weight: 600;
  margin: 0;
}

/* 公共区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 40px;
  font-weight: 900;
  line-height: 1.25;
  color: #0D141C;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  line-height: 1.6;
  color: #4573A1;
  max-width: 600px;
  margin: 0 auto;
}

.section-description {
  font-size: 18px;
  color: #4573A1;
  margin: 0;
}

/* 价格方案 */
.pricing-plans-section {
  padding: 0 0 80px;
  position: relative;
  z-index: 1;
}

.pricing-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.pricing-plan {
  background: #FFFFFF;
  border: 2px solid #E5EDF5;
  border-radius: 16px;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-plan:hover {
  border-color: #369EFF;
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
}

.pricing-plan.featured {
  border-color: #369EFF;
  transform: scale(1.05);
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.2);
}

.popular-label {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #369EFF, #2563EB);
  color: #FFFFFF;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-header {
  text-align: center;
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  color: #0D141C;
  margin-bottom: 8px;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.price-amount {
  font-size: 48px;
  font-weight: 900;
  line-height: 1;
  color: #0D141C;
}

.price-period {
  font-size: 16px;
  /* font-weight: 500; */
  color: #4573A1;
}

.plan-description {
  font-size: 16px;
  color: #4573A1;
  margin: 0;
}

.plan-button {
  width: 100%;
  margin-bottom: 8px;
}

.featured-button {
  background: linear-gradient(135deg, #369EFF, #2563EB);
  border: none;
}

.featured-button:hover {
  background: linear-gradient(135deg, #2563EB, #1D4ED8);
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.feature-item i {
  color: #369EFF;
  font-size: 16px;
  font-weight: bold;
}

.feature-item span {
  font-size: 14px;
  line-height: 1.5;
  color: #0D141C;
}

/* 功能对比表 */
.feature-comparison-section {
  padding: 80px 0;
  background: #FFFFFF;
}

.comparison-table {
  max-width: 900px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  background: #F7FAFC;
  font-weight: 700;
  color: #0D141C;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  background: #FFFFFF;
  border-bottom: 1px solid #E5EDF5;
}

.table-row:last-child {
  border-bottom: none;
}

.feature-column,
.plan-column,
.feature-name,
.feature-value {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.feature-column,
.feature-name {
  justify-content: flex-start;
  text-align: left;
}

.plan-column.featured,
.feature-value:nth-child(4) {
  background: linear-gradient(135deg, rgba(54, 158, 255, 0.1), rgba(37, 99, 235, 0.05));
  color: #369EFF;
  font-weight: 600;
}

/* 特性展示 */
.features-section {
  padding: 100px 0;
  position: relative;
}

.features-showcase {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.feature-highlight {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 80px;
  align-items: center;
}

.feature-highlight.reverse {
  grid-template-columns: 3fr 2fr;
}

.feature-highlight.reverse > * {
  direction: ltr;
}

.feature-content {
  max-width: none;
}

.feature-badge {
  display: inline-block;
  background: rgba(54, 158, 255, 0.1);
  color: #369EFF;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 20px;
}

.feature-title {
  font-size: 32px;
  font-weight: 900;
  line-height: 1.25;
  color: #0D141C;
  margin-bottom: 16px;
}

.feature-description {
  font-size: 16px;
  line-height: 1.6;
  color: #4573A1;
  margin-bottom: 24px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  font-size: 15px;
  color: #0D141C;
  margin-bottom: 8px;
  padding-left: 24px;
  position: relative;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  top: 4px;
  width: 16px;
  height: 16px;
  /* background: #369EFF; */
  color: #369EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-visual {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-visual img {
  width: 100%;
  height: auto;
  border-radius: 16px;
  object-fit: cover;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-visual img:hover {
  /* transform: translateY(-4px); */
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.15);
}

/* 工作流程 */
.workflow-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #F0F4F8, #E5EDF5);
}

.workflow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 60px;
  margin-top: 60px;
}

.workflow-step {
  text-align: center;
  position: relative;
}

.step-visual {
  margin-bottom: 24px;
  position: relative;
}

.step-number {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #369EFF;
  color: #FFFFFF;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.step-icon {
  width: 64px;
  height: 64px;
  background: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border: 3px solid #369EFF;
  color: #369EFF;
  transition: all 0.3s ease;
}

.workflow-step:hover .step-icon {
  background: #369EFF;
  color: #FFFFFF;
  transform: scale(1.1);
}

.step-content {
  max-width: 280px;
  margin: 0 auto;
}

.step-title {
  font-size: 20px;
  font-weight: 700;
  color: #0D141C;
  margin-bottom: 12px;
}

.step-description {
  font-size: 15px;
  line-height: 1.6;
  color: #4573A1;
  margin-bottom: 16px;
}

.step-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.feature-tag {
  background: rgba(54, 158, 255, 0.1);
  color: #369EFF;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 使用案例 */
.use-cases-section {
  padding: 100px 0;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.use-case-card {
  background: #FFFFFF;
  border: 1px solid #E5EDF5;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.use-case-card:hover {
  /* transform: translateY(-8px); */
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
  border-color: #369EFF;
}

.use-case-icon {
  width: 64px;
  height: 64px;
  /* background: linear-gradient(135deg, #369EFF, #5E8BFF); */
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: #FFFFFF;
}

.use-case-title {
  font-size: 20px;
  font-weight: 700;
  color: #0D141C;
  margin-bottom: 12px;
}

.use-case-description {
  font-size: 16px;
  line-height: 1.6;
  color: #375E89;
  margin-bottom: 20px;
}

.use-case-benefits {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.benefit {
  background: rgba(54, 158, 255, 0.1);
  color: #369EFF;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 用户评价 */
.testimonials-section {
  padding: 100px 0;
  background: #E3F1FF;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.testimonial-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 40px 32px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.testimonial-card:hover {
  /* transform: translateY(-4px); */
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
}

.testimonial-content {
  margin-bottom: 24px;
}

.rating {
  margin-bottom: 16px;
}

.stars {
  color: #FFB800;
  font-size: 16px;
}

.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  color: #0D141C;
  margin: 0;
  font-style: italic;
  height: 180px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 700;
  color: #0D141C;
  margin: 0 0 4px 0;
}

.author-title {
  font-size: 14px;
  color: #4573A1;
  margin: 0 0 2px 0;
}

.author-company {
  font-size: 12px;
  color: #369EFF;
  font-weight: 600;
  margin: 0;
}

/* 定价预览 */
.pricing-preview-section {
  padding: 100px 0;
}

.pricing-cards {
  display: grid;
  gap: 32px;
  margin-top: 60px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  grid-template-columns: repeat(3, 1fr);
}

.pricing-card {
  background: #FFFFFF;
  border: 2px solid #E5EDF5;
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  /* transform: translateY(-4px); */
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
  border-color: #369EFF;
}

.plan-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  /* transform: translateX(-50%); */
  background: linear-gradient(135deg, #369EFF, #5E8BFF);
  color: #FFFFFF;
  padding: 6px 16px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
}

.currency {
  font-size: 48px;
  font-weight: 600;
  color: #0D141C;
}

.amount {
  font-size: 48px;
  font-weight: 900;
  color: #0D141C;
}

.period {
  font-size: 16px;
  color: #4573A1;
}

.plan-cta {
  width: 100%;
}

.pricing-footer {
  text-align: center;
  margin-top: 40px;
}

.pricing-link {
  color: #369EFF;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.pricing-link:hover {
  color: #2563EB;
}

/* 常见问题 */
.faq-section {
  background: #E3F1FF;
  padding: 100px 0;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 800px;
  margin: 60px auto 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 900px;
  margin: 0 auto;
}

.faq-item {
  background: #FFFFFF;
  border: 1px solid #E5EDF5;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.faq-item:hover {
  border-color: #369EFF;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  padding: 20px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  font-weight: 400;
  color: #0D141C;
  margin-bottom: 12px;
}

.faq-question:hover {
  background: rgba(54, 158, 255, 0.05);
}

.faq-question span {
  font-size: 16px;
  font-weight: 600;
  color: #0D141C;
}

.faq-question svg {
  transition: transform 0.3s ease;
  color: #4573A1;
}

.faq-item:hover .faq-question svg {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 24px 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #4573A1;
  margin: 0;
}

.faq-answer p {
  font-size: 15px;
  line-height: 1.6;
  color: #4573A1;
  margin: 16px 0 0 0;
}

/* CTA 区域 */
.pricing-cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #369EFF, #2563EB);
  color: #FFFFFF;
  text-align: center;
}

.cta-title {
  font-size: 36px;
  font-weight: 900;
  margin-bottom: 16px;
}

.cta-description {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.pricing-cta-section .btn-primary {
  background: #FFFFFF;
  color: #369EFF;
}

.pricing-cta-section .btn-primary:hover {
  background: #F7FAFC;
  color: #369EFF;
}

.pricing-cta-section .btn-outline {
  background: transparent;
  color: #FFFFFF;
  border-color: #FFFFFF;
}

.pricing-cta-section .btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 最终CTA */
.capgen-cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #0D141C, #1A202C);
  color: #FFFFFF;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-text {
  margin-bottom: 40px;
}

.capgen-cta-section .cta-title {
  font-size: 40px;
  font-weight: 900;
  line-height: 1.25;
  color: #FFFFFF;
  margin-bottom: 16px;
}

.capgen-cta-section .cta-description {
  font-size: 18px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 32px;
}

.cta-guarantee {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.cta-guarantee p {
  margin: 0;
}

/* 页脚样式已移至 CapGenFooter 组件内 */

/* 下载页面特有样式 */
.download-section {
  padding: 100px 0;
}

.download-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.download-card {
  background: #FFFFFF;
  border: 2px solid #E5EDF5;
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.download-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
  border-color: #369EFF;
}

.download-card.featured {
  border-color: #369EFF;
  transform: scale(1.02);
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.15);
  position: relative;
}

.download-card.featured .popular-label {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #369EFF, #2563EB);
  color: #FFFFFF;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.download-card.featured:hover {
  transform: scale(1.02) translateY(-4px);
}

.download-button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.download-button[disabled]:hover {
  transform: none;
  background-color: inherit;
}

.download-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 16px;
  /* background: linear-gradient(135deg, #369EFF, #5E8BFF); */
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
}

.download-title {
  font-size: 20px;
  /* font-weight: 700; */
  color: #0D141C;
  margin-bottom: 12px;
}

.download-description {
  font-size: 16px;
  line-height: 1.6;
  color: #4573A1;
  margin-bottom: 24px;
}

.download-features {
  list-style: none;
  padding: 0;
  margin: 0 0 32px 0;
  text-align: left;
}

.download-features li {
  font-size: 14px;
  color: #0D141C;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.download-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #369EFF;
  font-weight: 700;
}

.download-button {
  width: 100%;
  margin-bottom: 16px;
}

.download-meta {
  font-size: 12px;
  color: #4573A1;
  margin: 0;
}

.system-requirements {
  padding: 60px 0;
  background: rgba(255, 255, 255, 0.5);
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.requirement-card {
  background: #FFFFFF;
  border: 1px solid #E5EDF5;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
}

.requirement-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  background: rgba(54, 158, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #369EFF;
}

.requirement-title {
  font-size: 18px;
  font-weight: 700;
  color: #0D141C;
  margin-bottom: 8px;
}

.requirement-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.requirement-list li {
  font-size: 14px;
  color: #4573A1;
  margin-bottom: 4px;
}

/* 登录注册页面共用样式 */
.signin-section {
  position: relative;
  padding-top: 100px;
  z-index: 1;
  min-height: calc(100vh - 100px);
  display: flex;
  align-items: center;
}

.signin-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.signin-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 40px;
  max-width: 520px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(54, 158, 255, 0.1);
  border: 1px solid #E5EDF5;
}

.signin-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-section {
  margin-bottom: 16px;
}

.signin-logo {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.signin-title {
  font-size: 28px;
  font-weight: 700;
  color: #0D141C;
  margin: 0 0 8px 0;
}

.signin-description {
  font-size: 14px;
  color: #4573A1;
  margin: 0;
  line-height: 1.5;
}

/* 表单样式 */
.signin-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 14px;
  color: #0D141C;
}

.form-input {
  padding: 10px 10px;
  border: 1px solid #E5EDF5;
  border-radius: 8px;
  font-size: 14px;
  color: #0D141C;
  background: #FFFFFF;
  transition: all 0.3s ease;
  width: 100%;
}

.form-input:focus {
  outline: none;
  border-color: #369EFF;
  box-shadow: 0 0 0 3px rgba(54, 158, 255, 0.1);
}

.form-input::placeholder {
  color: #9CA3AF;
}

/* 密码输入框 */
.password-input-wrapper {
  position: relative;
  width: 100%;
}

.password-input-wrapper .form-input {
  width: 100%;
  padding-right: 40px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 60%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #4573A1;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #369EFF;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 8px 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #0D141C;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #E5EDF5;
  border-radius: 3px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  /* color: #0D141C; */
  cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: #369EFF;
  border-color: #369EFF;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FFFFFF;
  font-size: 10px;
  font-weight: 700;
}

.forgot-password {
  font-size: 12px;
  color: #369EFF;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #2563EB;
}

/* 提交按钮 */
.signin-submit {
  width: 100%;
  margin-top: 8px;
}

.signin-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.signin-submit:disabled:hover {
  transform: none;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 页脚 */
.signin-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #E5EDF5;
}

.signup-prompt {
  font-size: 14px;
  color: #4573A1;
  margin: 0;
}

.signup-link {
  color: #369EFF;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link:hover {
  color: #2563EB;
}

/* Google登录按钮 */
.btn-google-signin {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 24px;
  border: 2px solid #E5EDF5;
  border-radius: 8px;
  background: #FFFFFF;
  color: #0D141C;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.btn-google-signin:hover {
  border-color: #369EFF;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(54, 158, 255, 0.1);
}

.google-icon {
  flex-shrink: 0;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #E5EDF5;
}

.divider-text {
  padding: 0 16px;
  font-size: 14px;
  color: #4573A1;
  background: #FFFFFF;
}

/* 注册页面特有样式 */
.terms-link {
  color: #369EFF;
  text-decoration: none;
  font-weight: 600;
}

.terms-link:hover {
  color: #2563EB;
  text-decoration: underline;
}

/* Auth0相关样式 */
.auth0-info {
  text-align: center;
  margin-bottom: 32px;
}

.auth0-description {
  font-size: 15px;
  color: #4573A1;
  line-height: 1.6;
  margin: 0;
  padding: 20px;
  background: rgba(54, 158, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #369EFF;
}

.signin-actions,
.signup-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Universal Login按钮样式 */
.signin-submit[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.signin-submit[disabled]:hover {
  transform: none !important;
}

/* 登录注册页面响应式设计 */
@media (max-width: 768px) {
  .signin-card {
    padding: 32px 24px;
    margin: 0 20px;
  }
  
  .signin-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .signin-section {
    padding: 100px 0 60px;
  }
  
  .signin-card {
    padding: 24px 20px;
    margin: 0 16px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title,
  .pricing-hero-title {
    font-size: 32px;
  }
  
  .pricing-plans-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .pricing-plan.featured {
    transform: none;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 1px;
  }
  
  .feature-column,
  .plan-column {
    display: none;
  }
  
  .feature-name {
    font-weight: 600;
    background: #F7FAFC;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .feature-highlight {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .workflow-steps {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-cards {
    grid-template-columns: 1fr;
  }
  
  /* 页脚响应式样式已移至 CapGenFooter 组件内 */
}

@media (max-width: 480px) {
  .container {
    padding: 0 20px;
  }
  
  .hero-section,
  .pricing-hero-section {
    padding: 80px 0 60px;
  }
  
  .toggle-container {
    flex-direction: column;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .hero-stats {
    gap: 40px;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-actions .btn {
    width: 100%;
    max-width: 280px;
  }
}
