/* 辅助工具类 */

/* 显示属性 */
.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

/* 文本对齐 */
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

/* 文本颜色 */
.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-light {
  color: var(--light-text) !important;
}

.text-gray {
  color: var(--gray-text) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-error {
  color: var(--error-color) !important;
}

/* 背景颜色 */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-dark {
  background-color: var(--dark-bg) !important;
}

.bg-80-5 {
  background-color: rgba(80,80,80,0.5) !important;
}

.bg-80-8 {
  background-color: rgba(80,80,80,0.8) !important;
}

/* 边框 */
.border {
  border: 1px solid var(--border-color) !important;
}

.border-top {
  border-top: 1px solid var(--border-color) !important;
}

.border-right {
  border-right: 1px solid var(--border-color) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

.border-left {
  border-left: 1px solid var(--border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-radius-10 {
  border-radius: 10px !important;
}

/* 间距 - Margin */
.m-0 {
  margin: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.ml-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

/* 间距 - Padding */
.p-0 {
  padding: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}