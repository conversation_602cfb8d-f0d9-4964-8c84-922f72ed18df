@use './variables.scss' as *;

/* 主样式文件 */
/* 这个文件导入所有其他样式文件 */

/* 基础样式 */
@import './base/reset.css';
@import './base/typography.css';
@import './main.css';

/* 布局样式 */
@import './layout/grid.css';
@import './layout/header.css';
@import './layout/footer.css';

/* 组件样式 */
@import './components/buttons.css';
@import './components/forms.css';
@import './components/cards.css';

/* 页面特定样式 */
// @import './pages/home.css';
// @import './pages/about.css';
// @import './pages/career.css';
// @import './pages/imtrans.css';
// @import './pages/privacy.css';
// @import './pages/support.css';
// @import './pages/term.css';

/* 工具类 */
@import './utils/helpers.css';
@import './utils/animations.css';

/* 第三方库 */
@import 'bootstrap-icons/font/bootstrap-icons.css';
// @import './vendors/bootstrap.css';
// @import './vendors/icons.css';

/* 响应式样式 - 移动优先设计 */
@import './responsive/mobile.css';
@import './responsive/tablet.css';
@import './responsive/desktop.css';