/* 全局样式 */
:root {
  --primary-color: #3772FF;
  --secondary-color: #7FAEFF;
  --dark-bg: #080808;
  --light-text: #ffffff;
  --gray-text: #9e9e9e;
  --border-color: #2d2e32;
  --hover-color: #00EEFF;
  --error-color: #ff3737;
  --success-color: #45B36B;
  --accent-color: #98F8FF;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--dark-bg);
  color: var(--light-text);
  line-height: 1.5;
}

/* main {
  padding-top: 72px; 
} */

a {
  color: var(--light-text);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--hover-color);
}

/* 通用布局类 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(90deg, var(--light-text) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.top-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1200px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.hero-section {
  height: 100vh;
  min-height: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 100px;
  font-weight: 900;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
  line-height: 1.2;
  text-align: center;
}

.hero-subtitle {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 56px;
  font-weight: 900;
  margin-bottom: 40px;
  text-align: center;
  color: #fff;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--gray-text);
  margin: 0 auto;
  line-height: 1.6;
}

.inline-text {
  font-size: 16px;
  color: #9e9e9e;
  line-height: 24px;
  font-weight: 400;
  letter-spacing: 0;
  vertical-align: middle;
}

/* Call to Action Section */
.cta-section {
  padding: 8rem 0;
  text-align: center;
  height: 60vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-content {
  margin: 0 auto;
  height: 100%;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #FFFFFF;
  line-height: 1.5;
}

.cta-subtitle {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
  
  .mobile-flex-column {
    flex-direction: column !important;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
}

/* 从原有CSS迁移的类 */
.position-relative {
  position: relative;
}

/* 字体大小类 */
.f-56 {
  font-size: 56px;
}

.f-20 {
  font-size: 20px;
}

.l-h-68 {
  line-height: 68px;
}

.l-h-32 {
  line-height: 32px;
}

.m-t-10 {
  margin-top: 10px;
}