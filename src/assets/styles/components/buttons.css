/* 按钮样式 */

/* 基础按钮 */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 1rem;
  display: inline-block;
  font-weight: 500;
  text-align: center;
  /* vertical-align: middle; */
  user-select: none;
  line-height: 1.5;
  /* transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; */
}

/* 主要按钮 */
.btn-primary {
  background: linear-gradient(90deg, #3772FF 0%, #37D5C3 100%);
  color: #FFFFFF;
}

.btn-primary:hover {
  /* background: linear-gradient(90deg, #2855E6 0%, #4A77FF 100%); */
  transform: translateY(-2px) scale(1.04);
  /* box-shadow: 0 4px 12px rgba(51, 102, 255, 0.3); */
}
.btn-xs:hover {
  transform: translateY(0px) scale(1.04);
}

/* 次要按钮 */
.btn-secondary {
  color: #1b1c1e;
  background-color: #fff;
  opacity: 0.8;
  /* border: 1px solid var(--secondary-color); */
}

.btn-secondary:hover {
  opacity: 1;
  transform: translateY(-2px) scale(1.04);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: #FFFFFF;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
  border-color: #FFFFFF;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px) scale(1.04);
}
.btn-blue {
  background: #369EFF;
  color: #FFFFFF;
  border-color: transparent;
}
.btn-blue:hover {
  transform: translateY(-2px) scale(1.04);
}
.btn-black {
  background: #000000;
  color: #FFFFFF;
  border-color: transparent;
}
.btn-black:hover {
  transform: translateY(-2px) scale(1.04);
}
/* 纯文本按钮 */
.btn-text {
  background: transparent;
  border: none;
  color: #FFFFFF;
  padding: 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-text:hover {
  opacity: 0.8;
  transform: translateY(-2px) scale(1.04);
}

.btn-cta {
  background: #FFF;
  color: #080808;
  border-color: transparent;
  border-radius: 48PX;
  font-size: 16PX;
  font-weight: 500;
  line-height: 1.5;
  height: 42PX;
  min-width: 120PX;
  text-transform: uppercase;
  letter-spacing: 4%;
}


.btn-text-arrow i {
  font-size: 0.9rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.btn-text-arrow:hover{
  text-decoration: underline;
  color: #fff;
}

/* 按钮尺寸 */
.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}
.btn-m {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}
.btn-s {
  padding: 8px 16px;
  font-size: 13px;
}
.btn-xs {
  padding: 6px 12px;
  font-size: 12px;
}
.btn-gray {
  background: #dfe0e0;
  color: #0D141C;
  border-color: #E5EDF5;
}


/* 按钮组 */
.btn-group {
  display: inline-flex;
  position: relative;
  vertical-align: middle;
}

.btn-group > .btn {
  position: relative;
  flex: 1 1 auto;
}

.btn-group > .btn:not(:first-child) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group > .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}