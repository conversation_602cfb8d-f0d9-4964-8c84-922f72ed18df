/* 卡片样式 */

.card {
  background-color: var(--card-bg);
  border-radius: 10px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(255, 255, 255, 0.02);
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--light-text);
}

.card-subtitle {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--gray-text);
}

/* 卡片内容 */
.card-body {
  padding: 1.25rem;
  flex: 1 1 auto;
}

.card-text {
  color: var(--gray-text);
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* 卡片底部 */
/* .card-footer {
  padding: 1.25rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(255, 255, 255, 0.02);
} */

/* 卡片图片 */
.card-img-top {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.card-img-bottom {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* 卡片变体 */
.card-primary {
  border-color: var(--primary-color);
}

.card-primary .card-header {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.card-secondary {
  border-color: var(--secondary-color);
}

.card-secondary .card-header {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* 卡片组 */
.card-group {
  display: flex;
  flex-wrap: wrap;
  margin: -0.5rem;
}

.card-group .card {
  margin: 0.5rem;
  flex: 1 0 calc(33.333% - 1rem);
  min-width: 300px;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-group .card {
    flex: 1 0 calc(50% - 1rem);
    min-width: 250px;
  }
  
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 576px) {
  .card-group .card {
    flex: 1 0 100%;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
}