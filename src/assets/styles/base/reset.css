/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  background: #000;
  /* 确保页面导航时立即跳转到顶部，不使用平滑滚动 */
  scroll-behavior: auto;
}

img {
  max-width: 100%;
  height: auto;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 移除列表样式 */
ul, ol {
  list-style: none;
}

/* 移除表单元素的默认样式 */
button, input, select, textarea {
  background: none;
  border: none;
  outline: none;
}

/* 确保表格布局一致 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 移除iframe的滚动条 */
iframe::-webkit-scrollbar {
  display: none;
}