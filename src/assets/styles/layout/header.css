/* 头部样式 */

/* 导航栏 */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(8, 8, 8, 0.1);
  /* box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1); */
}
.navbar {
  width: 100%;
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 40px;
  height: 72px;
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
}

.nav-list {
  display: flex;
  align-items: center;
  /* height: 72px; */
  gap: 60px;
  /* 增加菜单项之间的间距 */
  margin: 0;
  padding: 0;
}

.logo {
  color: #fff;
  font-weight: 500;
  /* letter-spacing: 2px; */
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 24px;
}

.logo a:hover {
  color: #fff;
}

.top-menu {
  display: flex;
  align-items: center;
  height: 72px;
  /* margin-top: 20px; */
  padding: auto;
  font-size: 14px;
}

.dropdown-container {
  position: relative;
  display: flex;
  align-items: center;
  height: 72px;
}

.dropdown-container>a,
.nav-list li a {
  display: flex;
  align-items: center;
  height: 100%;
  /* padding-top: 12px; */
}

.nav-list li a:hover {
  color: #00EEFF;
}

.logo-img {
  object-fit: contain;
  display: block;
  margin-right: 10px;
}

.active-link {
  color: #00EEFF !important;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .navbar-nav {
    display: none;
  }

  .navbar-toggler {
    display: block;
  }

  .navbar-collapse {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(18, 18, 18, 0.98);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 2rem;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }

  .navbar-collapse.show {
    transform: translateX(0);
  }
}