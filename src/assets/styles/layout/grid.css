/* 网格系统 */

/* 容器 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-stretch {
  align-items: stretch;
}

/* 间距类 */
.padding-w7 {
  padding: 7vw;
}

.padding-10-0 {
  padding: 10px 0;
}

.padding-20-10 {
  padding: 20px 10px;
}

.padding-top-20 {
  padding-top: 20px;
}

.padding-bottom-1 {
  padding-bottom: 4rem;
}

.padding-bottom-2 {
  padding-bottom: 2rem;
}

.padding-5-0 {
  padding: 5px 0;
}

/* 宽度类 */
.w-100 {
  width: 100%;
}

.w-75 {
  width: 75%;
}

.w-50 {
  width: 50%;
}

.w-25 {
  width: 25%;
}

.w-50-px {
  width: 50px;
}

.w-70-px {
  width: 70px;
}

.w-400-px {
  width: 400px;
}

.w-539px {
  width: 539px;
}

.w-601px {
  width: 601px;
}

/* 高度类 */
.h-100 {
  height: 100%;
}

.h-139px {
  height: 139px;
}

.h-652px {
  height: 652px;
}

.min-height-30rem {
  min-height: 30rem;
}