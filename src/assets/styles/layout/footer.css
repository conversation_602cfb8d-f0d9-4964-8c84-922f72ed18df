/* 底部样式 */

.footer {
  background-color: var(--dark-bg);
  border-top: 1px solid var(--border-color);
  padding: 3rem 0 2rem;
  margin-top: 2rem;
}

/* 底部列 */
.footer-col {
  margin-bottom: 2rem;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--light-text);
}

/* 底部链接列表 */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link-item {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: var(--gray-text);
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
}

.footer-link:hover {
  color: var(--primary-color);
}

/* 底部社交媒体 */
.footer-social {
  display: flex;
  margin-top: 1.5rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-text);
  margin-right: 1rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.social-icon {
  font-size: 1.25rem;
}

/* 底部版权信息 */
.footer-bottom {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.footer-copyright {
  color: var(--gray-text);
  font-size: 0.9rem;
}

.footer-copyright a {
  color: var(--primary-color);
  text-decoration: none;
}

/* 底部徽标 */
.footer-logo {
  display: block;
  margin-bottom: 1.5rem;
  max-width: 150px;
}

.footer-description {
  color: var(--gray-text);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1.5rem;
  }
  
  .footer-col {
    margin-bottom: 1.5rem;
  }
  
  .footer-title {
    margin-bottom: 1rem;
  }
  
  .footer-bottom {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }
}