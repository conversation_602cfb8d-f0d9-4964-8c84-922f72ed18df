import { createApp } from 'vue'
import Toast from '@/components/common/Toast.vue'

class ToastService {
  constructor() {
    this.toasts = []
  }

  show(options) {
    const toastContainer = document.createElement('div')
    document.body.appendChild(toastContainer)

    const toastApp = createApp(Toast, {
      ...options,
      onClose: () => {
        toastApp.unmount()
        document.body.removeChild(toastContainer)
        
        // 从数组中移除
        const index = this.toasts.indexOf(toastApp)
        if (index > -1) {
          this.toasts.splice(index, 1)
        }
      }
    })

    toastApp.mount(toastContainer)
    this.toasts.push(toastApp)

    return toastApp
  }

  success(message, options = {}) {
    return this.show({
      message,
      type: 'success',
      title: options.title || 'Success',
      ...options
    })
  }

  error(message, options = {}) {
    return this.show({
      message,
      type: 'error',
      title: options.title || 'Error',
      duration: options.duration || 6000, // 错误消息显示更久
      ...options
    })
  }

  warning(message, options = {}) {
    return this.show({
      message,
      type: 'warning',
      title: options.title || 'Warning',
      ...options
    })
  }

  info(message, options = {}) {
    return this.show({
      message,
      type: 'info',
      title: options.title || 'Info',
      ...options
    })
  }

  // 清除所有Toast
  clear() {
    this.toasts.forEach(toast => {
      if (toast.exposed && toast.exposed.close) {
        toast.exposed.close()
      }
    })
    this.toasts = []
  }
}

// 创建全局实例
const toast = new ToastService()

export default toast

// 便捷方法
export const showToast = toast.show.bind(toast)
export const showSuccess = toast.success.bind(toast)
export const showError = toast.error.bind(toast)
export const showWarning = toast.warning.bind(toast)
export const showInfo = toast.info.bind(toast) 