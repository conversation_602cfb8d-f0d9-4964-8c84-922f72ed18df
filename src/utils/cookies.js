// Cookie操作工具函数

// 设置Cookie
export const setCookie = (name, value, days = 30) => {
  const expires = new Date()
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000)
  const secure = window.location.protocol === 'https:' ? ';secure' : ''
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/${secure};samesite=strict`
}

// 获取Cookie
export const getCookie = (name) => {
  const nameEQ = name + "="
  const ca = document.cookie.split(';')
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1, c.length)
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
  }
  return null
}

// 删除Cookie
export const deleteCookie = (name) => {
  // 删除当前域名的cookie
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
  // 如果是子域名，也尝试删除主域名的cookie
  const hostname = window.location.hostname;
  if (hostname.includes('.')) {
    const mainDomain = hostname.split('.').slice(-2).join('.');
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;domain=.${mainDomain};`;
  }
}

/**
 * 保存非敏感用户信息到Cookie
 * @param {object} userData - 用户数据（仅包含非敏感信息）
 */
export const saveUserToCookie = (userData) => {
  try {
    // 确保只保存非敏感信息
    const safeUserData = {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      nickname: userData.nickname,
      uid: userData.uid,
      vip: userData.vip,
      vipExpiredAt: userData.vipExpiredAt,
      loginTime: userData.loginTime,
    }
    
    const userJson = JSON.stringify(safeUserData)
    setCookie('capgen_user', userJson, 30) // 30天过期
    setCookie('capgen_logged_in', 'true', 30)
    console.log('非敏感用户信息已保存到Cookie')
    return true
  } catch (error) {
    console.error('保存用户信息到Cookie失败:', error)
    return false
  }
}

/**
 * 从Cookie获取非敏感用户信息
 * @returns {object|null} 非敏感用户信息
 */
export const getUserFromCookie = () => {
  try {
    const userJson = getCookie('capgen_user')
    const isLoggedIn = getCookie('capgen_logged_in') === 'true'
    
    if (userJson && isLoggedIn) {
      const userData = JSON.parse(userJson)
      
      return userData
    }
    return null
  } catch (error) {
    console.error('从Cookie获取用户信息失败:', error)
    return null
  }
}

// 清除用户登录信息
export const clearUserCookies = () => {
  deleteCookie('capgen_user')
  deleteCookie('capgen_logged_in')
  console.log('用户登录信息已清除')
}

// 检查是否已登录（基于Cookie）
export const isLoggedInFromCookie = () => {
  return getCookie('capgen_logged_in') === 'true' && getCookie('capgen_user') !== null
} 