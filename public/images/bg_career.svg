<svg width="1920" height="1237" viewBox="0 0 1920 1237" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_4710_3283)">
<circle cx="960" cy="537" r="506" transform="rotate(-90 960 537)" fill="url(#paint0_linear_4710_3283)" fill-opacity="0.8"/>
</g>
<circle cx="960.002" cy="536.998" r="332.033" transform="rotate(-90 960.002 536.998)" fill="url(#paint1_linear_4710_3283)" fill-opacity="0.2" stroke="url(#paint2_linear_4710_3283)" stroke-opacity="0.7"/>
<circle cx="960.002" cy="536.998" r="265.627" transform="rotate(-90 960.002 536.998)" fill="url(#paint3_linear_4710_3283)" fill-opacity="0.2" stroke="url(#paint4_linear_4710_3283)" stroke-opacity="0.7"/>
<circle cx="959.997" cy="537.003" r="199.22" transform="rotate(-90 959.997 537.003)" fill="url(#paint5_linear_4710_3283)" fill-opacity="0.2" stroke="url(#paint6_linear_4710_3283)" stroke-opacity="0.7"/>
<circle cx="959.999" cy="537.001" r="132.813" transform="rotate(90 959.999 537.001)" fill="url(#paint7_linear_4710_3283)" fill-opacity="0.2" stroke="url(#paint8_linear_4710_3283)" stroke-opacity="0.7"/>
<defs>
<filter id="filter0_f_4710_3283" x="286" y="-137" width="1348" height="1348" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="84" result="effect1_foregroundBlur_4710_3283"/>
</filter>
<linearGradient id="paint0_linear_4710_3283" x1="454" y1="537" x2="1466" y2="537" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF"/>
<stop offset="0.506944" stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint1_linear_4710_3283" x1="627.969" y1="536.998" x2="1292.04" y2="536.998" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF"/>
<stop offset="0.506944" stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint2_linear_4710_3283" x1="627.969" y1="567.863" x2="1315.42" y2="567.863" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint3_linear_4710_3283" x1="694.375" y1="536.998" x2="1225.63" y2="536.999" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF"/>
<stop offset="0.506944" stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint4_linear_4710_3283" x1="694.375" y1="561.69" x2="1244.33" y2="561.691" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint5_linear_4710_3283" x1="760.777" y1="537.003" x2="1159.22" y2="537.003" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF"/>
<stop offset="0.506944" stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint6_linear_4710_3283" x1="760.777" y1="555.522" x2="1173.25" y2="555.522" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint7_linear_4710_3283" x1="827.186" y1="537.001" x2="1092.81" y2="537.001" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF"/>
<stop offset="0.506944" stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<linearGradient id="paint8_linear_4710_3283" x1="827.186" y1="549.347" x2="1102.17" y2="549.347" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E40AF" stop-opacity="0"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
</defs>
</svg>
