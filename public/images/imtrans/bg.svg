<svg width="1920" height="1333" viewBox="0 0 1920 1333" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Shapes">
<g id="Vector 26" filter="url(#filter0_f_4710_6117)">
<path d="M-17.7422 157.913V931.144V1174.34H2124.23C2103.45 1067.29 1972.7 853.198 1616.02 853.198C1170.16 853.198 934.766 853.197 658.835 441.639C468.609 157.914 148.544 154.796 -17.7422 157.913Z" fill="url(#paint0_radial_4710_6117)"/>
</g>
<g id="Vector 27" style="mix-blend-mode:screen" filter="url(#filter1_f_4710_6117)">
<path d="M562.241 748.292C178.026 683.767 -45.1211 173.926 -108.668 -72.9297V1232.23H2406.32C2390.44 1144.24 2344 951.398 2285.34 883.94C1926.06 473.329 1042.51 828.948 562.241 748.292Z" fill="url(#paint1_radial_4710_6117)" fill-opacity="0.7"/>
</g>
</g>
<defs>
<filter id="filter0_f_4710_6117" x="-117.742" y="57.4219" width="2341.97" height="1216.91" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_4710_6117"/>
</filter>
<filter id="filter1_f_4710_6117" x="-208.668" y="-172.93" width="2714.99" height="1505.16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_4710_6117"/>
</filter>
<radialGradient id="paint0_radial_4710_6117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1053.25 545.427) rotate(90) scale(628.91 1324.7)">
<stop stop-color="#818CF8"/>
<stop offset="1" stop-color="#4F46E5" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_4710_6117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(958.187 1364.21) rotate(-32.0054) scale(830.078 4006.4)">
<stop stop-color="white"/>
<stop offset="0.418539" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
