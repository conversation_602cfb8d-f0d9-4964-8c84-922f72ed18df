const options = {
	domain: 'mytoon-littlegrass.us.auth0.com',
	clientId: 'dk1rK4e8wXmfcbDuz68obF6vXksDsfvs',
	authorizationParams: {
		redirect_uri: window.location.origin+"/manga/login.html",
	}
};

function callFlutter(token,user){
    Toaster.postMessage(JSON.stringify({token:token,user:user}));
}

function logininit(){
	auth0.createAuth0Client(options).then(async (auth0Client) => {
		if (location.search.includes("state=") && 
		      (location.search.includes("code=") || 
		      location.search.includes("error="))) {
		    await auth0Client.handleRedirectCallback();
		    window.history.replaceState({}, document.title, "/");
		}
		const isAuthenticated = await auth0Client.isAuthenticated();
		const userProfile = await auth0Client.getUser();
		if(!userProfile){
			auth0Client.loginWithRedirect();
		}else{
			const token = await auth0Client.getIdTokenClaims();
			const idToken = token.__raw;
			
			const accessToken = await auth0Client.getTokenSilently();
			if(typeof(Toaster)!=="undefined"){
			    callFlutter(idToken,userProfile);
			}
		}
	})
}

logininit();

