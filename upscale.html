<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
		<link rel="apple-touch-icon" sizes="180x180" href="img/icon/apple-touch-icon96.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="32x32" href="img/icon/32.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="16x16" href="img/icon/16.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="96x96" href="img/icon/96.png?v=1.0.104">
		<link rel="stylesheet" href="./css/main.css?v=1.0.113">
		<script src="frame/jquery.js"></script>
		<script src="frame/jquery.js"></script>
<script src="frame/layui/layui.js"></script>
		<script src="frame/FileSaver.js"></script>
		<script src="frame/jszip.js"></script>
		<script src="frame/vue.js"></script>
		<title>Little Grass - AI Tools & Image Enhancement</title>
	</head>
	<body class="bg-1b1c1e text-white mt-0" data-bs-spy="scroll" data-bs-target="#navScroll">
		<!--header-->
		<header class="fixed-top header"></header>

		<!--main-->
		<main class="container px-vw-5 position-relative" id="enhancement">
			<div class="w-100 h-139px"></div>

			<div class="w-100 d-flex flex-column justify-content-center align-items-center">
				<h2 class="text-mid web-flex justify-content-center">Upscale</h2>
				<div style="height: 20px"></div>
				<div class="w-100 d-flex align-items-center f-wrap web-h-480px" id="upscale_content" v-if="transbegin===0">
					<img class="object-fit-contain w-p-50" src="img/upscale_ex.png" alt="img origin" id="showimg"/>
					<div class="w-20px"></div>

					<button class="w-p-50 d-flex flex-column align-items-center justify-content-center bg-none border-50fff border-radius-20" :style="'height:'+leftHeight+'px'" id="img_original">
						<div class="d-flex flex-column align-items-center justify-content-center">
							<img src="img/nodata.png" width="80" height="78" alt="download image">
							<div class="f-18 c-white l-h-26" style="white-space: pre-line;">
								Please upload images.
								image supports up to 5120x10240.
								image supports up to 5M.
								Supports formats such as jpg, jpeg, png, etc.
							</div>
						</div>
					</button>
				</div>
				<div style="height: 20px"></div>
			  	<div class="web-w-80 d-flex align-items-center justify-content-center f-wrap" v-if="transbegin!==0&&getImgLength()>0">
					<div class="w-100 d-flex justify-content-center bg-none border-50fff border-radius-20 h-100">
						<div class="w-100 d-flex justify-around align-items-start flex-wrap web-padding-20 web-row-gap-10px web-min-500-px overflow-y-scroll">
							<div class="d-flex web-w-p-10 position-relative margin-right-10" v-for="(item,index) in uppics" :key="index">
								<img class="object-fit-cover border-radius-5" width="100%" height="100%" :src="transbegin!==2?item.chooseimg:item.preview" @click="showImage()" alt="image">
								<div class="position-absolute w-100 h-100 bg-black-5 d-flex align-items-center justify-content-center" v-if="item.status!==1&&item.status!==-1">
									<img class="load" src="img/loading.png" width="30" height="30" alt="loading">
								</div>
								<div class="position-absolute w-100 h-100 d-flex align-items-start justify-content-end" v-if="item.status===1">
									<div class="position-relative bg-green padding-5-10 border-top-right-5px border-bottom-left-10px">Successes</div>
								</div>
								<div class="position-absolute w-90 h-100 d-flex align-items-end justify-content-end z-2" v-if="item.status===1">
									<a class="d-flex justify-content-center padding-5" :href="item.download" :download="item.download">
										<i class="layui-icon layui-icon-download-circle c-white"></i>
									</a>
								</div>
								<div class="position-absolute w-100 h-100 d-flex align-items-start justify-content-end" v-if="item.status===-1">
									<div class="position-relative bg-red padding-5-10 border-top-right-5px border-bottom-left-10px">Failed</div>
								</div>
							</div>
						</div>
					</div>
			  </div>

			  <div class="w-100 z-index-9999999 align-items-center justify-content-center phone-position-fixed h-60-px phone-bg-black phone-flex" v-if="transbegin!==0">
				  <button type="submit" class="btn-upscale d-flex align-items-center justify-content-center h-40-px w-80 c-white" @click="downAll()" :disabled="checkUploadDown()">
					  Download All
				  </button>
			  </div>
			  <div class="w-100 z-index-9999999 align-items-center justify-content-center phone-position-fixed h-60-px phone-bg-black web-flex margin-top-40" v-if="transbegin!==0">
				<button type="submit" class="btn-upscale d-flex align-items-center justify-content-center h-40-px w-80 c-white" @click="downAll()" :disabled="checkUploadDown()">
					Download All
				</button>
			  </div>
			  <div class="web-h-150px"></div>
				<div class="h-60-px"></div>
		  </div>
		</main>

		<!-- footer -->
		<footer class="py-vh-4 border-top border-dark footer"></footer>

		<div class="position-fixed left-0 top-0 login"></div>


		<script src="js/loadJS.js"></script>
		<script>
			let searchObj=getHtmlSearchMap();
			let enhancement = new Vue({
				el: "#enhancement",
				data:{
					uppics:{

					},
					timers: {},
					transbegin:0,
					user:null,
					isphone:isPhone(),
					offsetX:0,
					node:null,
					ism:false,
					tipIndex:0,
					leftHeight:0,
					showlogin:false
				},
				mounted(){
					onLoad();
					currentVue=this;
					this.show=true;
					this.node=document.getElementById("imgmeng");
				},
				methods:{
					getImageKey(){
						let keys=Object.keys(this.uppics);
						return keys[0];
					},
					setUser(user){
						if(user){
							this.user=user;
						}
						this.reStep();
					},
					reStep(){
						let url=window.location.href;
						let data=getPageData(url);
						removePageData(url);
						if(data){
							this.uppics=data.uppics;
							this.transbegin=data.transbegin;
							//document.getElementById("prompt").value=data.prompt;
							let senddata=data.senddata;
							if(searchObj["yes"]){
								this.createTask(data.index,senddata);
							}else{
								this.transbegin=0;
							}
						}
					},
					savePageData(key,senddata){
						let url=window.location.href;
						let data={
							transbegin:this.transbegin,
							uppics:this.uppics,
							prompt:"",
							index:0,
							senddata:senddata
						}
						savePageData(url,data);
					},
					mouseMove(){
						let rect = event.currentTarget.getBoundingClientRect();
						this.offsetX=event.clientX - rect.left;
						
						cancelAnimationFrame(this.node.animFrameID);
						let that=this;
						this.node.animFrameID = requestAnimationFrame(function() {
						    that.node.style.width = that.offsetX+"px"; // 设置新宽度
						});
					},
					mouseOut(){
						this.offsetX=document.getElementById("showimg").offsetWidth/2;
						cancelAnimationFrame(this.node.animFrameID);
						let that=this;
						this.node.animFrameID = requestAnimationFrame(function() {
						    that.node.style.width = that.offsetX+"px"; // 设置新宽度
						});
					},
					downAll(){
						downloadAll(this.uppics,this);
					},
					reset(){
						reset(this);
					},
					checkUploadDown(){
						return !checkUploadDown(this);
					},
					getImgLength(){
						return getImgLength(this);
					},
					getImage(){
						return getImage(this);
					},
					uploadAll(){
						for(let key in this.uppics){
							this.uploadData(key);
						}
					},
					uploadData(key){
						let item=this.uppics[key];
						let upload=item["upload"];
						item.senddata={
							task_type:"IMAGE_ENHANCEMENT",
							input:{
								img_original:this.uppics[key].path,
								prompt:""
							}
						};
						this.uppics[key]=item;

						let token=localStorage.getItem("tk");
						if(token!=null&&token!==""){
							upload.obj.upload(upload.index, upload.file);
							this.uppics[key]["upload"]=null;
						}else if(!this.showlogin){
							this.showlogin=true;
							loginDialog(this);
						}
					}
				},
			});
			function setUser(user){
				enhancement.setUser(user);
			}

			function resetInit(){
				let node=document.getElementById("showimg");
				if(node){
					enhancement.offsetX=node.offsetWidth/2;
					enhancement.leftHeight=node.offsetHeight;
					if(enhancement.isphone){
						enhancement.leftHeight=node.offsetHeight+40;
					}
					enhancement.$forceUpdate();
				}else{
					node=document.getElementById("upscale_content");
					if(node){
						enhancement.leftHeight=node.offsetHeight;
					}
				}
			}

			maininit(enhancement,"img_original-multiple",function (key){
				if(enhancement.transbegin!==1){
					enhancement.transbegin=1;
				}
				enhancement.$forceUpdate();
				enhancement.uploadData(key);
			});

			setFooterPadding();
			window.onresize=function (){
				setTimeout(function (){
					resetInit();
				},100);
			};
			resetInit();
		</script>
		<script type="module" src="js/firebase.js"></script>
	</body>
</html>