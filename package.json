{"name": "lg-home-web-vue3", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@auth0/auth0-vue": "^2.4.0", "@vueuse/head": "^2.0.0", "@vueuse/motion": "^3.0.3", "axios": "^1.6.2", "bootstrap-icons": "^1.13.1", "md5": "^2.3.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "sass-embedded": "^1.87.0", "vite": "^6.3.5"}}