# Little Grass 官网

Https://littlegrass.cc

## 技术栈

- **前端框架**：Vue 3
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **样式处理**：SASS
- **HTTP客户端**：Axios
- **图标库**：Bootstrap Icons

## 安装与运行

### 前提条件

- Node.js (推荐v16或更高版本)
- npm或yarn包管理器

### 安装步骤

1. 克隆项目到本地
   ```bash
   git clone [项目仓库URL]
   cd lg-home-web
   ```

2. 安装依赖
   ```bash
   npm install
   # 或
   yarn install
   ```

3. 启动开发服务器
   ```bash
   npm run dev
   # 或
   yarn dev
   ```
   开发服务器将在 http://localhost:3000 启动

4. 构建生产版本
   ```bash
   npm run build
   # 或
   yarn build
   ```

## 项目结构

```
├── public/             # 静态资源目录
├── src/                # 源代码目录
│   ├── api/            # API请求封装
│   ├── assets/         # 资源文件(图片、样式等)
│   │   └── styles/     # 样式文件
│   ├── components/     # 公共组件
│   │   └── common/     # 通用组件(Header, Footer等)
│   ├── router/         # 路由配置
│   ├── store/          # Pinia状态管理
│   ├── views/          # 页面视图组件
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── .eslintrc.js        # ESLint配置
├── .gitignore          # Git忽略文件
├── index.html          # HTML模板
├── package.json        # 项目依赖和脚本
├── README.md           # 项目说明文档
└── vite.config.mjs     # Vite配置文件
```

## 开发指南

### 添加新页面

1. 在`src/views`目录下创建新的Vue组件
2. 在`src/router/index.js`中添加新的路由配置

### 样式开发

项目使用SASS预处理器，全局样式在`src/assets/styles/main.scss`中定义。组件特定样式可以在各组件中使用`<style>`标签定义。

### 使用Bootstrap Icons

项目已集成Bootstrap Icons，可以直接在组件中使用：

```html
<i class="bi bi-heart"></i>
```

## 部署

构建项目后，将`dist`目录中的文件部署到Web服务器即可。

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 自动部署
预览分支: preview
域名：www-pre.littlegrass.cc

生产分支: release
域名：www.littlegrass.cc

这两个分支代码均每分钟自动更新到服务器一次

## 代码规范

项目使用ESLint进行代码规范检查，可以运行以下命令进行检查和修复：

```bash
npm run lint
```


stripe订阅调用流程

1、获取订阅配置列表 /ai-subtitle/payment/products
重点是要用返回值里面的product_id， 这些id对应不同的订阅方案。 也可以不调接口直接写死在代码里。

2、创建支付单 /ai-subtitle/payment/create
在 Header 添加参数 Authorization，其值为在 Bearer 之后拼接 Token
示例：
Authorization: Bearer ********************
参数：
{
    "channel": "stripe", //固定
    "type": "vip_subscription", //固定
    "product_id": "pro_annual",  // capgenjs中的product_id
    "success_url": "", //支付成功跳转地址
    "cancel_url":"" //支付取消跳转地址
}
返回值
{
    "code": 1,
    "msg": "SUCCESS",
    "data": {
        "pay_id": "PAY2024052614451593769372", //支付单号
        "third_pay_id": "", //第三方支付单号
        "pay_url": "" //支付跳转URL
    }
}

3、查询支付状态 /ai-subtitle/payment/query
当跳转到success_url后， 前端可以调用这个接口来判断是否真的支付成功。 status=1为支付成功。
或者不用这个查询接口， 使用获取用户信息接口也行，vip>0表示订阅成功，是会员状态。

测试域名是：api-test.littlegrass.cc
以上接口apifox里有文档。

线上暂时配置的是stripe测试环境，可以随意支付测试。 测试卡号：4242 4242 4242 4242， 日期、cvc、邮箱、姓名随意填。

对于客户端，无论生产还是测试都是用相同的product_id， 仅在api域名上做区分就行。

以后若有stripe相关变更，开发流程是：
1、在stripe的测试环境和生产环境分别建立（修改）产品和price
2、通知服务端，服务端修改相关配置。
3、服务端发布到测试环境，客户端测试。
4、服务端发布到生产环境。
5、客户端发布到生产环境。
