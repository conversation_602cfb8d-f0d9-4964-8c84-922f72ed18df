
<div class="w-100" id="payvip">
  <div class="container padding-bottom-1">
		<div class="w-100 col-12 col-xl-10 d-flex justify-content-center" v-if="!isphone">
			<h2 class="padding-bottom-2 padding-top-20">Buy Vip</h2>
		</div>
	  <div class="container-i-6 d-flex" :class="isphone?'w-100 padding-top-30 ':''"  v-if="!isphone">
		  <div class="d-flex flex-column" :class="isphone?'padding-0':''">
				<span class="padding-10" v-for="(text,index) in list" v-if="index<list.length/2">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
		  </div>
		  <div class="d-flex flex-column">
				<span class="padding-10" v-for="(text,index) in list" v-if="index>=list.length/2">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
		  </div>
	  </div>

	  <div class="container-i-6 d-flex flex-column w-100 padding-top-30 "  v-if="isphone">
		  <div class="d-flex flex-column padding-0 w-auto">
				<span class="padding-10 d-flex justify-content-center" v-for="(text,index) in list" :key="index">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
		  </div>

	  </div>
  </div>
  <div class="container container-i-7 d-flex flex-wrap" v-if="!isphone">
		<div class="pading-20-1 d-flex flex-column align-items-center position-relative margin-bottom-20" :class="index==curindex?'border-1E9FFF':''"
		v-for="(item,index) in paylist" @click="selectItem(index)" :style="'width: '+(98)/paylist.length+'%'">
			<h2 class="c-black-666-1 font-weight padding-top-20" :class="index==curindex?'c-black':''">{{item.name}}</h2>
			<span class="font-28 c-black-666-1 font-weight" :class="index==curindex?'c-black':''">
				{{item.currency+" "+(isMonth(item.name)?item.first_price:item.price)}}
				<span class="font-16 c-black-666-1 text-line-through font-weight-nomarl">
					{{isMonth(item.name)?(item.currency+" "+item.price):" "}}
				</span>
			</span>
			<span class="font-14-0 c-white font-weight position-absolute top-0 left-0 padding-4-10 border-left-top-radio-20 bg-red border-right-bottom-radio-20">
				{{item.currency+" "+item.first_price_day}}/day
			</span>
			<span class="font-14-0 c-black-888 font-weight h-16-px">
				{{isMonth(item.name)?(item.des0+" "+item.currency+" "+item.price+item.des1+" "+item.currency+" "+item.oprice):""}}
			</span>
		</div>
  </div>
  <div class="container container-i-7 d-flex flex-column" v-if="isphone">
	  <div class="w-100-1 d-flex flex-column position-relative h-80-px justify-content-center" v-for="(item,index) in paylist" :class="index==curindex?'border-grey1 bg-80-5':''"
	   @click="selectItem(index)">
		<div class="w-100 d-flex align-items-center position-relative justify-space-between">
			<span class="font-16 font-weight">{{item.name}}</span>
			<span class="font-20 c-white font-weight">
				{{item.currency+" "+(isMonth(item.name)?item.first_price:item.price)}}
				<span class="font-14 c-black-666 text-line-through font-weight-nomarl">
					{{isMonth(item.name)?(item.currency+" "+item.price):""}}
				</span>
			</span>
		</div>
		<span class="font-12 c-black-888 margin-top-10">
			{{isMonth(item.name)?(item.des0+" "+item.currency+" "+item.first_price+item.des1+" "+item.currency+" "+item.price):""}}
		</span>
		<span class="font-12 c-white position-absolute top--10 left-0 padding-0-10 border-radius-10 bg-red">
			{{item.currency+" "+item.first_price_day}}/day
		</span>
	  </div>
  </div>
  <div class="container container-i-7 d-flex flex-wrap justify-content-center padding-bottom-1">
	  <div class="w-100 h-50-px d-flex flex-column align-items-center justify-content-center" v-for="(item,index) in channels" :key="index"
		   :class="index>0&&isphone?'':' margin-top-20'">
		  <button class="w-100 border-radius-10 font-18 font-weight h-100 d-flex align-items-center justify-content-center" @click="Payment(index)">
			  <i class="bi bi-stripe c-blue h-100 d-flex align-items-center margin-right-10"></i>
			  {{item.name}}
		  </button>
	  </div>
  </div>
</div>


<script>
	let payvip = new Vue({
		el: "#payvip",
		data:{
			list:[
				"VIP discounts",//"vip优惠"
				"Multiple photo support",//"支持批量翻译图片"
				"Max photos 50MB",//"单次最多可上传50M的图片"
				"Multiple languages supports",//"支持英语、印尼语、中文等多国语言"
				"New features to experience first",//"新功能抢先体验"
				"Process priority queues"//"处理优先队列"
			],
			paylist:[

			],
			channels:[
				{name:"Buy Now",value:"stripe"}
			],
			showpay:false,
			isphone:false,
			curindex:0,
		},
		mounted(){
			this.show=true;
			this.getProducts();
			this.isphone=isPhone();
		},
		methods:{
			isMonth(name){
				return name.indexOf("Month") !== -1 || name.indexOf("month") !== -1;
			},
			showPayment(){
				this.showpay=true;
				this.$forceUpdate();
			},
			hidePayment(){
				this.showpay=false;
			},
			selectItem(index){
				this.curindex=index;
			},
			Payment(index){
				let data=this.paylist[this.curindex];
				let channel=this.channels[index];
				this.payCreate(data.product_id,channel.value);
			},
			getProducts(){
				let that=this;
				get("/home/<USER>/products",null,"GET",function(res){
					console.log(res);
					let list=res.products.stripe.vip_subscription;
					for(let i=0;i<list.length;i++){
						let item=list[i];
						if(item.name==="Premium Monthly"){
							item.des0="First "+item.name;
							item.des1=" then";
						}
					}
					that.paylist=list;

				});
			},
			payCreate(product_id,channel){
				let data={
					channel: channel,
					type: "vip_subscription",
					product_id: product_id,
					success_url: getHistory()+addAutoCrateTask(),
					cancel_url: window.location.origin+"/payvip.html"
				}
				get("/home/<USER>/create",data,null,function(res){
					if(res.code===1){
						let data=res.data;
						window.location.href=data.pay_url;
					}
				});

			}
		}
	});
</script>