
<div class="w-100 d-flex" id="information">
    <div class="w-100 h-100">
        <div class="w-100 h-100">
            <div class="w-100 web-h-screen--159px d-flex flex-column align-items-center" :class="isphone?'margin-top-20':''">
                <div class="w-80 margin-top-40 border-444 border-radius-10 padding-20">
                    <div class="w-100 h-60-px d-flex align-items-center justify-content-between border-bottom-444">
                        <p class="h-100 c-white-60 h-60-px line-height-60px">Name</p>
                        <p class="h-100 c-white-60 h-60-px line-height-60px">{{user?user.nickname:""}}</p>
                    </div>
                    <div class="w-100 h-60-px d-flex align-items-center justify-content-between border-bottom-444">
                        <p class="h-100 c-white-60 h-60-px line-height-60px">Birthday</p>
                        <p class="h-100 c-white-60 h-60-px line-height-60px">{{user?(user.birth?user.birth:user.brith??"2020-01-01"):"2020-01-01"}}</p>
                    </div>
                    <div class="w-100 h-60-px d-flex align-items-center justify-content-between border-bottom-444">
                        <p class="h-100 c-white-60 line-height-60px">Coin</p>
                        <p class="h-100 c-white-60 line-height-60px">{{user?user.coin:""}}</p>
                    </div>
                    <div class="w-100 h-60-px d-flex align-items-center justify-content-between border-bottom-444">
                        <p class="h-100 c-white-60 line-height-60px">Vip</p>
                        <p class="h-100 c-white-60 line-height-60px">{{user?(user.vip===1?user.vip_expired_at:"Not Vip"):""}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let information = new Vue({
        el: "#information",
        data:{
            user:{},
            isphone:isPhone(),
        },
        mounted(){
            let user=localStorage.getItem("user");
            if(user){
                this.user=JSON.parse(user);
            }
        },
        methods:{
            setUser(user){
                if(user){
                    this.user=user;
                    this.$forceUpdate();
                }
            },
        }
    });

    function setUser(user){
        if(typeof user!=="undefined"){
            information.setUser(user);
        }
    }
</script>