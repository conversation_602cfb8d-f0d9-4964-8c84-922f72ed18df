
<div class="w-100 d-flex align-content-center position-relative" :class="isphone?'flex-column':''" id="task">
	<div class="w-100 min-height-48rem d-flex align-content-center margin-top-20" id="main-right" :style="getMarginLeft()" :class="isphone?'min-height-400-px':''">
		<div class="w-100 h-100 border-radius-10 position-relative d-flex flex-column align-items-center">
			<div class="w-100" v-if="!isphone">
				<div class="w-100 d-flex flex-column align-items-center" v-if="!isphone&&list[pageIndex]&&list[pageIndex][page-1]&&list[pageIndex][page-1].length>0">
					<div class="w-90 border-444" v-if="pageIndex<3">
						<div class="w-100 h-50-px d-flex align-items-center">
							<p class="h-100 w-15 border-right-444 text-center" :class="getClassName()">Time</p>
							<p class="h-100 w-20 border-right-444 text-center" :class="getClassName()">Original image</p>
							<p class="h-100 w-20 border-right-444 text-center" v-if="menus[pageIndex].id===1">Translate to</p>
							<p class="h-100 w-20 border-right-444 text-center" v-if="menus[pageIndex].id===3">Outpainting dimensions</p>
							<p class="h-100 w-20 border-right-444 text-center" :class="getClassName()">Status</p>
							<p class="h-100 w-25 text-center" :class="getClassName()">Result</p>
						</div>
						<div class="w-100 h-50-px d-flex align-items-center border-top-444" v-for="(item,index) in list[pageIndex][page-1]" :key="index">
							<p class="h-100 w-15 border-right-444 text-center c-white-60 text-clip padding-0-5" :class="getClassName()">{{item.created_at}}</p>
							<img class="border-right-444 w-20 h-100--2 object-fit-contain" :class="getClassName()" :src="item.input.img_original" onerror="this.src='img/error.jpg'" @click="showImage(index,true)"/>
							<p class="h-100 w-20 border-right-444 text-center c-white-60" v-if="menus[pageIndex].id===1">{{parseLang(item.input.target_lang)}}</p>
							<p class="h-100 w-20 border-right-444 text-center c-white-60 word-wrap line-height-25" v-if="menus[pageIndex].id===3" v-html="parseSizes(item.input)"></p>
							<p class="h-100 w-20 border-right-444 text-center c-white-60" :class="getClassName()" v-html="parseStatus(item.status)"></p>
							<div class="h-100 w-25 d-flex justify-content-center align-items-center position-relative" :class="getClassName()" v-if="item.output">
								<img class="w-100 h-100--2 object-fit-contain" :src="item.output.img_result??item.input.img_original" onerror="this.src='img/error.jpg'" @click="showImage(index,false)"/>
								<a class="download layui-icon layui-icon-download-circle c-black-666 margin-left-5 margin-right-10" :href="item.output.img_result_download" :download="item.output.img_result_download" v-if="pageIndex!=0"></a>
							</div>
						</div>
					</div>
					<div class="position-relative w-100 d-flex align-items-center justify-content-center margin-top-20 laypage" id="web-laypage"></div>
				</div>
				<div class="w-100 d-flex flex-column align-items-center" v-if="!list[pageIndex] || !list[pageIndex][page-1] || list[pageIndex][page-1].length==0">
					<img class="margin-top-116" src="../img/nodata.png" width="100" alt="nodata">
					<p class="f-18 font-weight-700 margin-top-20">No {{emptyTip}} history yet</p>
				</div>
			</div>

			<div class="w-100" v-if="isphone">
				<div class="w-100 col-12 col-xl-10 d-flex justify-content-center align-items-center">
					<h3 class="text-mid">{{menus[pageIndex].title}}</h3>
					<i class="bi bi-list margin-left-10 font-20 text-mid c-ccc" @click="open"></i>
				</div>
				<div class="w-100" v-if="list[pageIndex]&&list[pageIndex][page-1]&&list[pageIndex][page-1].length>0">
					<div class="w-100 d-flex flex-column align-items-center">
						<div class="w-100 padding-10">
							<div class="w-100 d-flex flex-column align-items-center border-aaa padding-0-10 margin-bottom-30 border-radius-10" v-for="(item,index) in list[pageIndex][page-1]" :key="index">
								<div class="w-100 h-40-px d-flex align-items-center justify-content-center border-bottom-444">
									<p class="w-30-0 h-100 text-center c-white-60 border-right-444 d-flex align-items-center justify-content-center">Time</p>
									<p class="w-70 h-100 text-center c-white-60">{{item.created_at}}</p>
								</div>
								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.img_original">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444 line-height-25">Original image</p>
									<img class="w-70 h-90 object-fit-contain text-center c-white-60" :src="item.input.img_original" onerror="this.src='img/error.jpg'" @click="showImage(index,true)"/>
								</div>
								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.target_lang">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444 line-height-25">Translate to</p>
									<p class="w-70 h-100 text-center c-white-60 line-height-25 d-flex align-items-center justify-content-center">{{parseLang(item.input.target_lang)}}</p>
								</div>
								<div class="w-100 h-40-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.provider">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444">Model</p>
									<p class="w-70 h-100 text-center c-white-60">{{item.input.provider}}</p>
								</div>
								<div class="w-100 h-60-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.left">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444 line-height-25">Outpainting dimensions</p>
									<p class="w-70 h-100 text-center c-white-60 d-flex align-items-center justify-content-center line-height-30" v-html="parseSizes(item.input)"></p>
								</div>
								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.dimensions">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444">Attribute</p>
									<p class="w-70 h-100 c-white-60 line-height-25 text-center" v-html="parseAttribute(item.input)"></p>
								</div>
								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.input.prompt&&pageIndex!=1">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444">Content</p>
									<p class="w-70 h-100 c-white-60 line-height-25 text-center maxcolum-3 d-flex align-items-center justify-content-center" @click="showContent(item.input.prompt)">{{item.input.prompt}}</p>
								</div>
								<div class="w-100 h-40-px d-flex align-items-center justify-content-center border-bottom-444">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444">Status</p>
									<p class="w-70 h-100 text-center c-white-60" v-html="parseStatus(item.status)"></p>
								</div>
								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.output&&item.output.img_result">
									<p class="w-30-0 h-100 text-center d-flex align-items-center justify-content-center c-white-60 border-right-444">Result</p>
									<img class="w-70 h-90 object-fit-contain" :src="item.output?item.output.img_result:'img/tou.png'" onerror="this.src='img/error.jpg'" @click="showImage(index,false)" alt="result"/>
								</div>


								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.output&&item.output.img_result">
									<a class="layui-btn layui-btn-primary w-80 c-white border-radius-10"  :href="item.output.img_result_download" download>Download</a>
								</div>

								<div class="w-100 h-80-px d-flex align-items-center justify-content-center border-bottom-444" v-if="item.output&&item.output.video">
									<a class="layui-btn layui-btn-primary w-80 c-white border-radius-10"  :href="item.output.img_result_download" download>Download</a>
								</div>
							</div>
						</div>
						<div class="position-relative w-100 d-flex align-items-center justify-content-center margin-top-20 laypage" id="phone-laypage"></div>
					</div>
				</div>
			</div>
			<div class="w-50 h-60 d-flex flex-column justify-content-center align-items-center opacity-50 padding-20" v-if="list[pageIndex]&&list[pageIndex].length==0">
				<img src="../img/nodata.png" class="object-fit-contain" width="318" height="309" alt="nodata"/>
				<p class="margin-top-20" :class="!isphone?'font-20':''">Temporarily No Data</p>
			</div>
		</div>
	</div>

	<div class="position-fixed left-0 top-0 d-flex justify-content-center align-items-center" style="width:100vw;height:100vh;z-index:999999999" @click="closeContent()" v-if="textOpen">
		<p class="line-height-25 font-18 bg-black padding-20 border-radius-20" :class="isphone?'w-80':'w-360-px'">{{content}}</p>
	</div>
</div>

<script>
	let task = new Vue({
		el: "#task",
		data:{
			pageIndex:0,
			isphone:isPhone(),
			menus:JSON.parse(JSON.stringify(task_menus)),
			list:[],
			totals:[0,0,0,0],
			pageNums:[9,9,9,6],
			isopen:false,
			textOpen:false,
			content:"",
			page:1,
			emptyTip:"translate",
		},
		mounted(){
			let pageIndex=getHtmlKey();
			if(pageIndex!==-1){
				this.pageIndex=pageIndex;
				this.touchEvent(pageIndex);
			}else{
				this.touchEvent(this.pageIndex,true);
			}
		},
		methods:{
			showContent(content){
				this.content=content;
				this.textOpen=true;
			},
			closeContent(){
				event.stopPropagation();
				this.textOpen=false;
				return false;
			},
			parseLang(lang){
				let s={
					en:"English",
					id:"Indonesian",
					zh:"Chinese"
				};
				return s[lang];
			},
			getMarginLeft(){
					if(!this.isphone){
					let node=document.getElementById("main-left");
					if(node){
						let left=node.offsetWidth+10;
						if(left<224){
							left=224;
						}
						return "margin-left: "+left+"px !important";
					}else{
						return "";
					}
				}
			},
			getClassName(){
				let id=this.menus[this.pageIndex].id;
				return (id===1||id===3)?'':'w-25';
			},
			parseSizes(input){
				if(!this.isphone){
					return "Left: "+input.left+" Right: "+input.right+"<br>Up: "+input.up+" Down: "+input.down;
				}else{
					return "Left: "+input.left+" Right: "+input.right+"<br>Up: "+input.up+" Down: "+input.down;
				}
			},
			parseAttribute(input){
				let d=input.dimensions.replace("_",":");
				return "Style: "+input.style+"<br>Dimensions: "+d+"<br>Motion enhancement: "+input.motion_enhancement;
			},
			down(src){
				down(src);
			},
			None(){
				event.stopPropagation();
				return false;
			},
			open(){
				this.isopen=true;
			},
			close(){
				this.isopen=false;
			},
			touchEvent(index,is){
				this.pageIndex=index;
				let arr=["translate","upscale","outpainting"];
				this.emptyTip=arr[this.pageIndex];
				this.getTaskList(1,is);

			},
			changeEvent(){

			},
			initLayPage(page,count){
				let that=this;
				let id=this.isphone?"phone-laypage":"web-laypage";
				layui.use('laypage', function(){
					let laypage = layui.laypage;
					laypage.render({
						elem: id,
						curr:page,
						count: count,
						limit:that.pageNums[that.pageIndex],
						prev:"Previous",
						next:"Next",
						groups:that.isphone?3:5,
						jump: function (obj,first){
							if(!first) {
								let page = obj.curr;
								that.getTaskList(page);
							}
						}
					});
				});
			},
			getTaskList(page,is){
				let types=["IMAGE_TRANSLATE","IMAGE_ENHANCEMENT","IMAGE_OUTPAINT","VIDEO_GENERATE"];
				let type=types[this.pageIndex];
				let data={
					page:page,
					page_size:this.pageNums[this.pageIndex],
					task_type:type
				}
				let that=this;
				let winWidth=window.innerWidth;
				let winHeight=window.innerHeight;

				if(!that.list[that.pageIndex]||that.list[that.pageIndex].length<=page-1){
					let index=-1;
					console.log(is);
					if(layui.layer&&!is){
						index= layui.layer.load(1,{offset:[winHeight/2+"px",(winWidth+340)/2+"px"]});
					}
					get("/home/<USER>/list",data,"GET",function (res){
						if(res.code===0){
							that.page=page;
							that.totals[that.pageIndex]=res.total;
							if(!that.list[that.pageIndex]){
								that.list[that.pageIndex]=[];
							}
							that.list[that.pageIndex][page-1]=[];
							that.list[that.pageIndex][page-1]=res.list;

							that.initLayPage(page,that.totals[that.pageIndex]);
							that.$forceUpdate();
						}
						if(index!==-1){
							layui.layer.close(index);
						}
					});
				}else{
					that.page=page;
					that.initLayPage(page,that.totals[that.pageIndex]);
					that.$forceUpdate();
				}
			},
			parseStatus(status){
				let s=["Processing...","<font color='#8f8'>Succeed</font>","<font color='#f88'>Failed</font>"];
				return s[status];
			},
			showImage(index,or){
				//if(item.task_type==="IMAGE_TRANSLATE"&&!or){
					let winWidth=window.innerWidth;
					let winHeight=window.innerHeight;
					localStorage.removeItem("mangaimage");
					localStorage.setItem("mangaimages",JSON.stringify(this.list[this.pageIndex][this.page-1]));
					layer.config({
						extend: '../../../css/manga.css' //同样需要先加载新皮肤
					});
					layer.open({
						type: 2,
						skin: 'layer-manga-skin',
						closeBtn:0,
						area: [winWidth+'px', winHeight+'px'],
						content: ['../common/mangaimage.html?o='+or+"&index="+index+"&type="+this.pageIndex,"no"] //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
					});
				// }else{
				// 	let src=item.output["img_result"]??item.input["img_original"];
				// 	if(src&&src!==""){
				// 		let photos={
				// 			title:"",
				// 			id:"1",
				// 			start:0,
				// 			data:[{src:src}]
				// 		}
				// 		layer.photos({
				// 			photos:photos,
				// 		})
				// 	}
				// }
			}
		}
	});

	function touchEvent(index){
		task.touchEvent(index);
	}
</script>