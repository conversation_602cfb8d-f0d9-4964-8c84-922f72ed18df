
<div class="w-100" id="paycoin">
  <div class="container padding-bottom-1">
		<div class="w-100 col-12 col-xl-10 d-flex justify-content-center" v-if="!isphone">
			<h2 class="padding-bottom-2 padding-top-20">Buy Coin</h2>
		</div>
		<div class="container-i-6 d-flex flex-wrap" v-if="!isphone">
			<div class="d-flex flex-column flex-wrap">
				<span class="padding-10" v-for="(text,index) in list" v-if="index<list.length/2">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
			</div>
			<div class="d-flex flex-column flex-wrap">
				<span class="padding-10" v-for="(text,index) in list" v-if="index>=list.length/2">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
			</div>
		</div>
	  <div class="container-i-6 d-flex flex-column w-100 padding-top-30" v-if="isphone">
		  <div class="d-flex flex-column padding-0 w-auto">
				<span class="padding-10 d-flex justify-content-center" v-for="(text,index) in list" :key="index">
					<i class="bi bi-check-lg margin-right-5 font-18"></i>
					{{text}}
				</span>
		  </div>
	  </div>
  </div>
  <div class="container container-i-7 d-flex flex-wrap" v-if="!isphone">
		<div class="pading-20-1 d-flex flex-column align-items-center position-relative margin-bottom-20" :class="index==curindex?'border-1E9FFF':''"
		v-for="(item,index) in paylist" @click="selectItem(index)" :style="'width: '+(98)/paylist.length+'%'">
			<h2 class="c-black-666-1 font-weight padding-top-20" :class="index==curindex?'c-black':''">{{item.name}}</h2>
			<span class="font-28 c-black-666-1 font-weight" :class="index==curindex?'c-black':''">
				{{item.currency+" "+item.price}}
			</span>

		</div>
  </div>
  <div class="container container-i-7 d-flex flex-column" v-if="isphone">
	  <div class="w-100-1 d-flex flex-column h-60-px justify-content-center position-relative" v-for="(item,index) in paylist" :class="index==curindex?'border-grey1 bg-80-5':''"
	   @click="selectItem(index)">
		<div class="w-100 d-flex align-items-center position-relative justify-space-between padding-0-10">
			<span class="font-16 font-weight" :class="index==curindex?'c-white':'c-black-888'">{{item.name}}</span>
			<span class="font-20 font-weight" :class="index==curindex?'c-white':'c-black-888'">{{item.currency+" "+item.price}}</span>
		</div>
	  </div>
  </div>
  <div class="container container-i-7 d-flex flex-wrap justify-content-center padding-bottom-1">
	  <div class="w-100 h-50-px d-flex flex-column align-items-center justify-content-center" v-for="(item,index) in channels" :key="index"
		   :class="index>0&&isphone?'':' margin-top-20'">
		  <button class="w-100 border-radius-10 font-18 font-weight h-100 d-flex align-items-center justify-content-center" @click="Payment(index)">
			  <i class="bi bi-stripe c-blue h-100 d-flex align-items-center margin-right-10"></i>
			  {{item.name}}
		  </button>
	  </div>
  </div>

</div>


<script>
	let paycoin = new Vue({
		el: "#paycoin",
		data:{
			list:[
				"Are used for AI tools",
				"Can be used for new features"
			],
			initpaylist:[
				{nums:"100",name:"100 coin",price:6,currency:"USD",oprice:12,des0:"First time ",des1:" then "},
				{nums:"500",name:"500 coin",price:25,currency:"USD",oprice:50,des0:"First time ",des1:" then "},
				{nums:"1000",name:"1000 coin",price:48,currency:"USD",oprice:96,des0:"First time ",des1:" then "}
			],
			paylist:[

			],
			channels:[
				{name:"Buy Now",value:"stripe"}
			],
			showpay:false,
			isphone:false,
			curindex:0,
			type:"vip"
		},
		mounted(){
			this.show=true;
			this.getProducts();
			this.isphone=isPhone();
		},
		methods:{
			showPayment(){
				this.showpay=true;
				this.$forceUpdate();
			},
			hidePayment(){
				this.showpay=false;
			},
			selectItem(index){
				this.curindex=index;
			},
			Payment(index){
				let data=this.paylist[this.curindex];
				let channel=this.channels[index];
				this.payCreate(data.product_id,channel.value);
			},
			getProducts(){
				let that=this;
				get("/home/<USER>/products",null,"GET",function(res){
					that.paylist=res.products.stripe.coin;
				});
			},
			payCreate(product_id,channel){
				let data={
					channel: channel,
					type: "coin",
					product_id: product_id,
					success_url: getHistory()+addAutoCrateTask(),
					cancel_url: window.location.origin+"/paycoin.html"
				}
				get("/home/<USER>/create",data,null,function(res){
					if(res.code===1){
						let data=res.data;
						window.location.href=data.pay_url;
					}
				});

			}
		}
	});

</script>