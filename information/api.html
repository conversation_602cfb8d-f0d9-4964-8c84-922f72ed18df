
<!--main-->
<main class="w-100" id="api">
    <div class="container padding-bottom-1">
        <div class="w-100 col-12 col-xl-10 d-flex justify-content-center" v-if="!isphone">
            <h2 class="padding-bottom-2 padding-top-20">API</h2>
        </div>
        <div class="w-100 d-flex flex-column align-items-center" v-if="!isphone">
            <div class="w-90 border-444">
                <div class="w-100 h-50-px d-flex align-items-center border-bottom-444 c-ccc">
                    <p class="h-100 w-30-0 border-right-444 text-center">Api</p>
                    <p class="h-100 w-50 border-right-444 text-center">Token</p>
                    <p class="h-100 w-10 border-right-444 text-center">Times</p>
                    <p class="h-100 w-10 border-right-444 text-center">Balance</p>
                </div>
                <div class="w-100 h-50-px d-flex align-items-centerr border-bottom-444 c-white-60">
                    <p class="h-100 w-30-0 border-right-444 text-center">/api/home/<USER>/create/translate</p>
                    <div class="h-100 w-50 padding-0-10 border-right-444">
                        <p class="h-100 w-100 text-center text-hidden maxcolum-2">ZAGbUUSZgcwk4WOoMOlTGx4HZRrWsmldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9</p>
                    </div>
                    <p class="h-100 w-10 border-right-444 text-center">{{count1}}</p>
                    <p class="h-100 w-10 border-right-444 text-center">{{user==null?0:user["coin"]}}</p>
                </div>
                <div class="w-100 h-50-px d-flex align-items-centerr border-bottom-444 c-white-60">
                    <p class="h-100 w-30-0 border-right-444 text-center">/api/home/<USER>/create/upscale</p>
                    <div class="h-100 w-50 padding-0-10 border-right-444">
                        <p class="h-100 w-100 text-center text-hidden maxcolum-2">smldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9PQ3brRhfcmMjh76ypmOgBP0Dwf9A</p>
                    </div>
                    <p class="h-100 w-10 border-right-444 text-center">{{count2}}</p>
                    <p class="h-100 w-10 border-right-444 text-center">{{user==null?0:user["coin"]}}</p>
                </div>
                <div class="w-100 h-50-px d-flex align-items-centerr border-bottom-444 c-white-60">
                    <p class="h-100 w-30-0 border-right-444 text-center">/api/home/<USER>/create/outpaint</p>
                    <div class="h-100 w-50 padding-0-10 border-right-444">
                        <p class="h-100 w-100 text-center text-hidden maxcolum-2">oMOlTGx4HZRrWsmldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9PQ3brRhfcmMjh76y</p>
                    </div>
                    <p class="h-100 w-10 border-right-444 text-center">{{count3}}</p>
                    <p class="h-100 w-10 border-right-444 text-center">{{user==null?0:user["coin"]}}</p>
                </div>
                <div class="w-100 h-50-px d-flex align-items-centerr border-bottom-444 c-white-60">
                    <p class="h-100 w-30-0 border-right-444 text-center">/api/home/<USER>/create/chat</p>
                    <div class="h-100 w-50 padding-0-10 border-right-444">
                        <p class="h-100 w-100 text-center text-hidden maxcolum-2">bUUSZgcwk4WOoMOlTGx4HZRrWsmldGxhsVonU8A2M9dZ6LsiWH9PhfcmMjhgBP0Dwf9A</p>
                    </div>
                    <p class="h-100 w-10 border-right-444 text-center">{{count4}}</p>
                    <p class="h-100 w-10 border-right-444 text-center">{{user==null?0:user["coin"]}}</p>
                </div>
            </div>
        </div>
        <div class="w-100 d-flex flex-column align-items-center" v-if="isphone">
            <div class="w-90 h-200-px margin-top-20 d-flex">
                <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center padding-10 border-444 c-white border-radius-10">
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Api</p>
                        <p class="w-90 text-right c-white-60">/api/home/<USER>/create/translate</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Token</p>
                        <p class="w-90 text-right text-nowrap text-clip margin-left-20 c-white-60 font-12">ZAGbUUSZgcwk4WOoMOlTGx4HZRrWsmldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Times</p>
                        <p class="w-90 text-right c-white-60">{{count1}}</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Balance</p>
                        <p class="w-90 text-right c-white-60">{{user==null?0:user["coin"]}}</p>
                    </div>
                </div>
            </div>
            <div class="w-90 h-200-px margin-top-20 d-flex">
                <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center padding-10 border-444 c-white border-radius-10">
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Api</p>
                        <p class="w-90 text-right c-white-60">/api/home/<USER>/create/upscale</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Token</p>
                        <p class="w-90 text-right text-nowrap text-clip margin-left-20 c-white-60 font-12">oMOlTGx4HZRrWsmldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9PQ3brRhfcmMjh76y</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Times</p>
                        <p class="w-90 text-right c-white-60">{{count2}}</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Balance</p>
                        <p class="w-90 text-right c-white-60">{{user==null?0:user["coin"]}}</p>
                    </div>
                </div>
            </div>
            <div class="w-90 h-200-px margin-top-20 d-flex">
                <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center padding-10 border-444 c-white border-radius-10">
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Api</p>
                        <p class="w-90 text-right c-white-60">/api/home/<USER>/create/outpaint</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Token</p>
                        <p class="w-90 text-right text-nowrap text-clip margin-left-20 c-white-60 font-12">bUUSZgcwk4WOoMOlTGx4HZRrWsmldGxhsVonU8A2M9dZ6LsiWH9PhfcmMjhgBP0Dwf9A</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Times</p>
                        <p class="w-90 text-right c-white-60">{{count3}}</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Balance</p>
                        <p class="w-90 text-right c-white-60">{{user==null?0:user["coin"]}}</p>
                    </div>
                </div>
            </div>
            <div class="w-90 h-200-px margin-top-20 d-flex">
                <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center padding-10 border-444 c-white border-radius-10">
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Api</p>
                        <p class="w-90 text-right c-white-60">/api/home/<USER>/create/chat</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Token</p>
                        <p class="w-90 text-right text-nowrap text-clip margin-left-20 c-white-60 font-12">smldGxhsBKCZBv4VonU8A2M1OqVrf9dZ6LsiWH9PQ3brRhfcmMjh76ypmOgBP0Dwf9A</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Times</p>
                        <p class="w-90 text-right c-white-60">{{count4}}</p>
                    </div>
                    <div class="h-100 w-100 d-flex text-left border-bottom-444 align-items-center">
                        <p class="w-10 text-left">Balance</p>
                        <p class="w-90 text-right c-white-60">{{user==null?0:user["coin"]}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</main>

<script>
    let api = new Vue({
        el: "#api",
        data:{
            show:false,
            isphone:false,
            count1:0,
            count2:0,
            count3:0,
            count4:0,
            user: null
        },
        mounted(){

            this.show=true;
            this.isphone=isPhone();
            let c1=localStorage.getItem("count1");
            if(c1==null){
                this.count1=100+Math.floor(Math.random()*1000);
                localStorage.setItem("count1",this.count1+"");
            }else{
                this.count1=c1;
            }

            let c2=localStorage.getItem("count2");
            if(c1==null){
                this.count2=50+Math.floor(Math.random()*5000);
                localStorage.setItem("count2",this.count2+"");
            }else{
                this.count2=c2;
            }

            let c3=localStorage.getItem("count3");
            if(c3==null){
                this.count3=88+Math.floor(Math.random()*3000);
                localStorage.setItem("count3",this.count3+"");
            }else{
                this.count3=c3;
            }

            let c4=localStorage.getItem("count4");
            if(c4==null){
                this.count4=66+Math.floor(Math.random()*8888);
                localStorage.setItem("count4",this.count4+"");
            }else{
                this.count4=c4;
            }

        },
        methods:{
            setUser(user){
                if(user){
                    this.user=user;
                    this.$forceUpdate();
                }
            },
        }
    });


</script>