<div class="w-100 d-flex flex-column align-items-center min-h-100--320px" id="active">
	<div class="w-90 border-444">
		<div class="w-100 h-50-px d-flex align-items-center">
			<p class="h-100 w-25 border-right-444 text-center">Time</p>
			<p class="h-100 w-50--45 border-right-444 text-center">Content</p>
			<p class="h-100 w-15 border-right-444 text-center">Cost</p>
			<p class="h-100 w-15 text-center">Balance</p>
		</div>
		<div class="w-100 h-50-px d-flex align-items-center border-top-444" v-for="(item,index) in curlist" :key="index">
			<p class="h-100 w-25 border-right-444 text-center c-white-60">{{item.trade_time}}</p>
			<p class="h-100 w-50--45 border-right-444 text-center line-height-20 d-flex align-items-center justify-content-center c-white-60">{{item.content}}</p>
			<p class="h-100 w-15 border-right-444 text-center c-white-60" :class="getColor(item.amount)">{{item.amount}}</p>
			<p class="h-100 w-15 text-center c-white-60">{{item.balance}}</p>
		</div>
		<div class="w-100 d-flex align-items-center justify-content-center border-top-444 laypage" id="active-laypage"></div>
	</div>
</div>

<script>
	let active = new Vue({
		el: "#active",
		data:{
			coinlist:[],
			coinsize:10,
			curlist:[],
			isfirst: true,
			total: 0,

		},
		mounted(){
			this.getCoinTrade(1,true);
		},
		methods:{
			getColor(amount){
				if(amount<0){
					return "c-red-60";
				}else if(amount>0){
					return "c-green-60"
				}
				return "";
			},
			getCoinTrade(page,is){
				let curlist=this.coinlist[page-1];
				if(!curlist){
					curlist=[];
					this.coinlist[page-1]=[];
				}
				if(curlist.length===0){
					var that=this;
					let index=-1;
					if(layui.layer&&!is){
						let winWidth=window.innerWidth;
						let winHeight=window.innerHeight;
						index= layui.layer.load(1,{offset:[winHeight/2+"px",(winWidth+340)/2+"px"]});
					}
					get("/home/<USER>/coin_trade",{page:page,page_size:this.coinsize},"GET",function (res){
						that.total=res.total;
						curlist=res.list;
						that.curlist=curlist;
						that.coinlist[page-1]=curlist;
						if(that.isfirst){
							that.isfirst=false;
						}
						that.initLayPage(page,that.total);
						if(index!==-1){
							layui.layer.close(index);
						}
					});
				}else{
					this.curlist=curlist;
					this.initLayPage(page,this.total);
				}

			},
			initLayPage(page,count){
				let that=this;
				layui.use('laypage', function(){
					let laypage = layui.laypage;
					laypage.render({
						elem: "active-laypage",
						count: count,
						curr: page,
						limit:that.coinsize,
						prev:"Previous",
						next:"Next",
						groups:that.isphone?3:5,
						jump: function (obj,first){
							if(!first) {
								let page = obj.curr;
								that.getCoinTrade(page);
							}
						}
					});
				});
			}
		}
	});
</script>