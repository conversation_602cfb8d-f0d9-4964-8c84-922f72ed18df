<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
		<link rel="apple-touch-icon" sizes="180x180" href="img/icon/apple-touch-icon96.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="32x32" href="img/icon/32.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="16x16" href="img/icon/16.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="96x96" href="img/icon/96.png?v=1.0.104">
		<link rel="stylesheet" href="./css/main.css?v=1.0.113">
		<script src="frame/jquery.js"></script>
		<script src="frame/layui/layui.js"></script>
		<script src="frame/vue.js"></script>
		<title>Little Grass - AI Tools & Chat</title>
	</head>
	<body class="bg-1b1c1e text-white mt-0">
		<!--header-->
		<header class="fixed-top header"></header>

		<!--main-->
		<main class="position-relative w-100" id="chat" :style="getMainHeight()">
			<div class="w-100 h-92px"></div>
			<div class="container" :class="isphone?'h-100--10px padding-0':'h-100-92px border-radius-10 padding-1-3rem'">
				<div class="w-100 d-flex position-relative" :class="isphone?'flex-column align-items-center min-height-100':'align-items-stretch justify-content-around h-100'">
					  <div class="d-flex flex-column align-items-center a-left w-350-px position-relative margin-right-0 h-100" id="model"  :class="isphone?'border-radius-0':''">
						  <div class="w-100 col-12 col-xl-10 d-flex justify-content-center" v-if="!isphone">
							  <p class="font-20 font-weight margin-bottom-20">AI Chat</p>
						  </div>

						  <div class="w-100 col-12 col-xl-10 d-flex">
							  <p class="font-14 font-weight-700 margin-bottom-20 text-left">Assistant</p>
						  </div>
						  <div class="w-100 col-12 col-xl-10 d-flex flex-wrap overflow-y-scroll" style="height: 200px">
							  <div class="d-flex justify-content-center margin-bottom-10" :class="index%4===3?'':'padding-right-10px'" v-for="(item,index) in assistants" :key="index">
								<div class="d-flex flex-column justify-content-center align-items-center web-border-5px bg-393D49 overflow-clip position-relative" style="width: 70px;" @click="selectAssistant(index)">
								  	<img class="object-fit-cover object-top" :src="item.icon??'img/tou.png'" width="70" height="90" alt="as">
									<div class="position-absolute bottom-0 w-100 h-20-px bg-black-5 web-border-bottom-5px d-flex justify-content-center align-items-center padding-0-5">
										<p class="font-12 text-nowrap text-clip" v-if="item.name">{{item.name}}</p>
									</div>
								</div>
							  </div>
						  </div>


						  <div class="w-100 col-12 col-xl-10 d-flex">
							  <p class="font-14 font-weight-700 margin-bottom-10 text-left margin-top-20">History</p>
						  </div>
						  <div class="w-100 col-12 col-xl-10 d-flex flex-column overflow-y-scroll margin-bottom-10" :style="currentKey!==''?'height: 340px':'height: 380px'">
							  <div class="w-100 h-60-px d-flex align-items-center margin-bottom-10 bg-393D49 web-border-5px padding-0-10 position-relative" v-for="(key,index) in historyKeys" :key="index"
								   @click="selectChat(key)" @mouseenter="historyEnter(key)" @mouseleave="historyLeave">
								  <div class="web-border-radius-20 d-flex align-items-center justify-content-center overflow-hidden bg-while-20 margin-right-10" style="width: 40px;height: 40px" v-if="historys[key].time">
								   	<img class="object-fit-cover object-center" :src="(historys[key].assistant&&historys[key].assistant.icon)?historys[key].assistant.icon:'img/aibot.png'" width="24" height="24" alt="c-icon">
								  </div>
								  <div class="d-flex flex-column justify-content-between margin-right-40" v-if="historys[key].time">
									  <p class="font-14 line-height-25 text-nowrap text-clip">{{key}}</p>
									  <p class="font-12 c-ccc line-height-25">{{historys[key].time}}</p>
								  </div>
								  <i class="position-absolute right-10 layui-icon layui-icon-delete f-30" @click="removeHistory(key)" v-if="mouseMoveKey===key&&historys[key].time"></i>
							  </div>
							  <div class="w-100 h-100 d-flex flex-column align-items-center" v-if="JSON.stringify(historys)==='{}'">
								  <img class="margin-top-50" src="img/nodata.png" width="100" alt="nodata">
								  <p class="f-18 font-weight-700 margin-top-20">No Chat history yet</p>
							  </div>
						  </div>

						  <div class="w-100 col-12 col-xl-10 d-flex" v-if="currentKey!==''">
							  <button class="w-100 h-40-px c-white border-radius-10 btn-upscale line-height-40 font-family-Bangers" @click="newChat">New Chat</button>
						  </div>
					  </div>
					  <div class="position-relative d-flex flex-column align-items-center a-right padding-0" :class="isphone?'border-radius-0':'border-radius-10'" :style="getChatHeight()">
						  <div class="d-flex flex-column w-100 h-100 bg-80-5 position-relative border-aaa border-radius-10 overflow-hidden">
							<div class="w-100 h-60-px bg-white-30 d-flex align-items-center justify-content-center font-weight-700 f-20">
								<div class="web-border-radius-20 d-flex align-items-center justify-content-center overflow-hidden bg-while-20 margin-right-10" style="width: 30px;height: 30px">
									<img class="object-fit-cover object-center" :src="(currentAssistant&&currentAssistant.icon)?currentAssistant.icon:'img/aibot.png'" width="20" height="20" alt="c-icon">
								</div>
								{{currentKey===""?'What can i do for you?':currentKey}}
							</div>
							<div class="w-100 d-flex flex-column h-100 position-relative overflow-y-scroll padding-20" @click="touchChat">
								<!--问题与回答-->
								<div class="w-100 position-relative" id="chat_content">
									<div class="w-100 d-flex flex-column" v-for="(item,index) in chats" :key="index">
										<!--问题-->
										<div class="w-100 d-flex justify-content-end" :class="index>0?'margin-top-20':''">
											<div class="d-flex flex-column justify-content-end">
<!--												<p class="text-right margin-bottom-6">{{user?user.nickname:''}}</p>-->
												<div class="d-flex justify-content-end">
													<span class="line-height-30 text-right bg-80 padding-10 border--top-right font-18">{{item.question.text}}</span>
												</div>
												<p class="text-right line-height-30 c-white-60">{{item.question.time}}</p>
											</div>
											<div class="margin-left-10" :class="isphone?'w-30-px h-30-px':'w-40-px'">
												<img class="object-fit-cover border-radius-20" src="img/head.png" :src="getHead()" onerror="this.src='img/head.png'" :width="isphone?'30px':'40px'" :height="isphone?'30px':'40px'" alt="head"/>
											</div>
										</div>
										
										<!--回答-->
										<div class="w-100 d-flex margin-top-20">
											<div class="w-40-px h-40-px margin-right-10 bg-120 d-flex justify-content-center align-items-center" :class="isphone?'w-30-px h-30-px':''">
												<img :src="(currentAssistant&&currentAssistant.icon)?currentAssistant.icon:'img/aibot.png'" width="24" alt="ai">
											</div>
											<div class="w-100--50px d-flex flex-column">
												<div class="d-flex">
													<div class="d-flex justify-content-end padding-10 border--top-left bg-80">
														<span class="line-height-30 text-left font-18" :class="opens[index]?'':'maxcolum-3'" v-html="item.answer.text"></span>
														<i class="layui-icon" :class="opens[index]?'layui-icon-up':'layui-icon-down'" @click="chatOpen(index)" v-if="statuss[index]===1"></i>
													</div>
<!--													<span class="w-100 line-height-30 text-left font-18" :class="opens[index]?'':'maxcolum-3'" v-html="item.answer.text"></span>-->

												</div>
												<p class="text-left line-height-30 c-white-60" v-if="item.answer.time">{{item.answer.time}}</p>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="d-flex align-items-center w-100 h-49-px border-top-1px bg-80 padding-0-10 border-bottom-radius-10" v-if="!isphone">
								<input class="w-100 h-90 border-none padding-0-10 c-white bg-80-5 border-radius-10" id="input_chat" v-model="curchat" @keyup="keyEvent()"/>
								<button class="layui-btn margin-left-10 c-white border-radius-10" @click="send">Send</button>
							</div>
							  <div class="position-absolute bottom-0 d-flex align-items-center w-100 h-50-px border-top-1px bg-80 padding-0-10" v-if="isphone">
								  <input class="w-100 h-90 border-none padding-0-10 c-white bg-80-5 border-radius-10" id="input_chat" v-model="curchat" @keyup="keyEvent()"/>
								  <button class="layui-btn margin-left-10 c-white border-radius-10" @click="send">Send</button>
							  </div>
						</div>
						  <div id="chat_end" style="height: 0;overflow: hidden;"></div>
					  </div>
				</div>
			</div>

		</main>



		<!-- footer -->
		<footer class="py-vh-2 border-top border-dark footer"></footer>


		<script src="js/loadJS.js"></script>
		<script>
			let searchObj=getHtmlSearchMap();
			var chat = new Vue({
				el: "#chat",
				data:{
					btns:[
						"Proportional",
						"Freely"
					],
					showfloat:false,
					user:null,
					isphone:isPhone(),
					assistants:[
						{},{},{},{},{},{},{},{},{}
					],
					chats:[],
					opens:[],
					statuss:[],
					curchat:"",
					issend:false,
					startX:0,
					isTouch:false,
					initLefts:{},
					touchchat:false,
					resizeObserver: null,
					isget: false,
					historys:{
						"0":{},
						"1":{},
						"2":{},
						"3":{},
					},
					currentKey:"",
					currentAssistant: null,
					mouseMoveKey:"",
					historyKeys:[],
				},
				mounted(){
					onLoad();
					this.show=true;
					this.getAssistant();
					//this.cleanHistory();
					this.getHistory();
					//this.getModel();

					//this.listenChat();
				},
				methods:{
					getChatHeight(){
						if(this.isphone){
							let winH=window.innerHeight;
							let h=winH-(isIphone()?134:116);
							return "height: "+h+"px !important";
						}
						return "";
					},
					initHistoryKeys(){
						let keys=Object.keys(this.historys);
						let arr=[];
						for(let i=0;i<keys.length;i++){
							arr[i]={key: keys[i],time: this.historys[keys[i]].time};
						}
						arr.sort(function (a,b){
							if(a.time<b.time){
								return 1;
							}
							return -1;
						});
						for(let i=0;i<arr.length;i++){
							this.historyKeys[i]=arr[i].key;
						}

						console.log(this.historyKeys);
					},
					selectAssistant(index){
						let as=this.assistants[index];
						if(as.id){
							this.currentKey=as.name;
							this.currentAssistant=as;
							if(this.historys[this.currentKey]){
								this.chats=this.historys[this.currentKey].chat
							}else{
								this.chats=[];
							}

							for(let i=0;i<this.chats.length;i++){
								this.opens[i]=false;
								this.statuss[i]=1;
							}
						}
					},
					historyEnter(key){
						this.mouseMoveKey=key;
					},
					historyLeave(){
						this.mouseMoveKey="";
					},
					newChat(){
						this.currentAssistant=null;
						this.currentKey="";
						this.chats=[];
						this.opens=[];
						this.statuss=[];
						this.historyKeys=[];
						this.initHistoryKeys();
					},
					selectChat(key){
						if(this.historys[key]){
							this.currentKey=key;
							this.chats=this.historys[key].chat;
							this.currentAssistant=this.historys[key].assistant;
							for(let i=0;i<this.chats.length;i++){
								this.opens[i]=false;
								this.statuss[i]=1;
							}
						}
					},
					listenChat(){
						let that=this;
						let node=document.getElementById("chat_content");
						if (!this.resizeObserver) {
							this.resizeObserver = new ResizeObserver((entries ) => {
								if(this.isphone&&!that.touchchat){
									for( let item of entries){
										if(node===item.target){
											let endNode=document.getElementById("chat_end");
											if(endNode){
												endNode.scrollIntoView(false)
											}
										}
									}
								}else if(!this.isphone){
									for( let item of entries){
										if(node===item.target){
											let endNode=document.getElementById("chat_end");
											if(endNode){
												endNode.scrollIntoView(false)
											}
										}
									}
								}
							})
							this.resizeObserver.observe(node)
						}
					},
					getMainHeight(){
						return getMainHeight(false,true);
					},
					getHead(){
						if(this.user&&this.user.avatar){
							return this.user.avatar;
						}else{
							return "img/head.png";
						}

					},
					touchChat(){
						this.touchchat=true;
					},
					setUser(user){
						if(user){
							this.user=user;
						}
					},
					reStep(){
						let url=window.location.href;
						let data=getPageData(url);
						removePageData(url);
						if(data){
							this.curchat=data.curchat;
							this.chats=data.chats;
							//this.tokens=data.tokens;
							let senddata=data.senddata;

							if(searchObj["yes"]){
								this.send(null,senddata);
							}else{
								this.transbegin=0;
							}

						}
					},
					savePageData(key,senddata){
						let url=window.location.href;
						let data={
							curchat:this.curchat,
							chats:this.chats,
							senddata:senddata
						}
						savePageData(url,data);
					},
					Copy(answer){
						let textarea = document.createElement('textarea');
						  textarea.style.position = 'fixed';
						  textarea.style.opacity = 0;
						  textarea.value = answer;
						  document.body.appendChild(textarea);
						  textarea.select();
						  document.execCommand('copy');
						  document.body.removeChild(textarea);
						  showToast("Success copied to clipboard");
					},
					keyEvent(){
						if(event.key==="Enter"){
							this.send();
						}
					},
					chatOpen(index){
						let open=this.opens[index];
						this.opens[index]=!open;
						this.$forceUpdate();
					},
					getAssistant(){
						let that=this;
						get("/home/<USER>/characters",{},"GET",function (res){
							if(res["code"]===0&&res["characters"]){
								that.assistants=res["characters"];
							}
						});
					},
					saveHistory(){
						localStorage.setItem("lg-chat-history",JSON.stringify(this.historys));
					},
					getHistory(){
						let s=localStorage.getItem("lg-chat-history");
						if(s){
							this.historys=JSON.parse(s);
							this.$forceUpdate();
						}else{
							this.historys={};
						}
						this.initHistoryKeys();
					},
					removeHistory(key){
						event.preventDefault();
						event.stopPropagation();
						delete this.historys[key];
						this.saveHistory();
						this.newChat();
						this.$forceUpdate();
					},
					cleanHistory(){
						this.historys={};
						localStorage.removeItem("lg-chat-history");
					},
					chatCreate(data){
						this.listenChat();
						let item={question:{text: data.message[0].text,time: getDate()},answer:{text: "Thinking..."}};
						let chats=JSON.parse(JSON.stringify(this.chats));
						let curchat=""+this.curchat;
						this.opens.push(false);
						this.statuss.push(0);
						this.chats.push(item);

						this.curchat="";
						if(this.currentKey===""){
							this.currentKey=data.message[0].text;
						}
						this.historys[this.currentKey]={chat: this.chats,assistant:this.currentAssistant,time:getDate()};
						this.saveHistory();
						this.initHistoryKeys();
						let that=this;
						post("/home/<USER>/send",data,function(res){
							if(res&&res.code===0){
								if(that.historys[that.currentKey]) {
									let answers = res.answers;
									let typeWriter = {};
									for (let k in answers) {
										let rd = 20 + Math.floor(Math.random() * 10);
										let arr = answers[k]["choices"];
										let content = "";
										for (let i = 0; i < arr.length; i++) {
											let msg=arr[i].message.content;
											let con="";
											try{
												msg=JSON.parse(msg);
												for(let i=0;i<msg.length;i++){
													con+=msg[i].text;
													content += "<br>";
												}
											}catch(e){
												console.log(e);
												con=msg;
											}
											content += msg;
											content += "<br>"
										}
										let len = content.length;
										item.answer.text = "";
										item.answer.time = getDate();
										that.statuss[that.statuss.length - 1] = 1;

										let chats = JSON.parse(JSON.stringify(that.chats));
										let ct = JSON.parse(JSON.stringify(item));
										ct.answer.text = content;
										chats[chats.length - 1] = ct;
										that.historys[that.currentKey].chat = chats;

										let index = 0;
										typeWriter[k] = setInterval(function () {
											if (index < len) {
												//item.list[k].answer+=content[index];
												item.answer.text += content[index];
												that.chats[that.chats.length - 1] = item;
												index++;
											} else {
												clearInterval(typeWriter[k]);
												//item.list[k].status=1;
												that.chats[that.chats.length - 1] = item;
												that.saveHistory();
											}
											that.$forceUpdate();
										}, rd);

									}
								}
							}else if(res.code===-1){
								that.chats=chats;
								that.curchat=curchat;
								that.savePageData();
								if(res.msg==="unlogin"){
									window.location.href="./login.html";
								}else{
									window.location.href="./paycoin.html";
								}
							}else{
								that.chats=chats;
								that.curchat=curchat;
								showToast(res.msg);
							}
							that.issend=false;
						});
						
						
					},
					send(event,senddata){
						if(!this.issend){
							this.touchchat=false;
							this.issend=true;
							if(!senddata){
								let data={
									message:[{
										type: "text",
										text: this.curchat
									}],
								}
								if(this.currentAssistant){
									data["character_id"]=this.currentAssistant.id;
								}
								this.chatCreate(data);
							}else{
								this.chatCreate(senddata);
							}
						}
					}
				}
			});
			function setUser(user){
				chat.setUser(user);
			}
			maininit(chat);
		</script>
		<script type="module" src="js/firebase.js"></script>
	</body>
</html>