<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
		<link rel="apple-touch-icon" sizes="180x180" href="img/icon/apple-touch-icon96.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="32x32" href="img/icon/32.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="16x16" href="img/icon/16.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="96x96" href="img/icon/96.png?v=1.0.104">
		<link rel="stylesheet" href="./css/main.css?v=1.0.113">
		<script src="frame/jquery.js"></script>
<script src="frame/layui/layui.js"></script>
		<script src="frame/FileSaver.js"></script>
		<script src="frame/jszip.js"></script>
		<script src="frame/vue.js"></script>
		<title>Little Grass - Free online manga translator</title>
	</head>
	<body class="bg-1b1c1e text-white mt-0">
		<!--header-->
		<header class="fixed-top header"></header>

		<!--main-->
		<main class="w-100 position-relative" id="transpic">
			<div class="w-100 h-139px"></div>
			<div class="container px-vw-5 d-flex flex-column align-items-center">
				<!--window title-->
				<div class="w-100 col-12 col-xl-10 web-flex justify-content-center">
					<h2 class="text-mid" style="font-weight: 700">Ai Manga</h2>
				</div>
				<div style="height: 40px"></div>
				<p class="web-w-50 text-center f-24 line-height-30 c-ccc">Upload manga and translate them into multiple languages quickly using our cutting-edge AI translator.</p>
				<div style="height: 40px"></div>
				<div class="d-flex web-w-90 justify-content-center flex-wrap">
					<div class="border-radius-10 c-ccc h-100 d-flex flex-column align-items-center web-w-90">
						<div class="w-100 d-flex flex-column justify-content-start align-items-center border-radius-20 bg-005fff padding-bottom-50 web-min-500-px">
							<!--select language-->
							<div style="height: 30px"></div>
							<div class="container d-flex align-items-center w-100 justify-content-center h-40-px" style="font-weight: 700">
								<label class="font-14 c-white h-40-px line-height-40">Auto</label>
								<div style="width: 40px"></div>
								<img src="img/exchange.png" width="24" height="24" alt="ArrowExchange">
								<div style="width: 40px"></div>
								<label class="d-flex align-items-center">
									<select class="font-14 c-white border-none padding-0 magin-0 h-40-px line-height-40 bg-none hide-appearance" @change="changeLang">
										<option class="bg-black" v-for="(item,index) in radios" :key="index" :value="item.value">{{item.name}}</option>
									</select>
									<img class="margin-left-5" src="img/down.png" width="10" height="6" alt="down">
								</label>
							</div>

							<div style="height: 30px"></div>



							<!--upload-->
							<button class="position-relative btn-translate-upload web-w-80 flex-column align-items-center justify-content-center web-h-360px" id="img_original" :class="getImgLength()===0?'d-flex':'d-none'">
								<div class="d-flex flex-column align-items-center justify-content-center">
									<img src="img/nodata.png" width="80" height="77" alt="select image">

									<div class="f-18 c-white line-height-40" style="white-space: pre-line;">
										Drop Your Images Or Files Here!
										(MAX FILE SIZE:  5MB)
									</div>
								</div>
							</button>


							<!--upload image-->
							<div class="position-relative w-100 d-flex flex-wrap padding-0-20" v-if="getImgLength()>0" id="imgs">
								<div class="web-translate-pic d-flex justify-content-center align-items-center" v-for="(item,index) in uppics" :key="index">
									<div class="position-relative bg-white border-radius-5 w-90 h-90" @mouseenter="imgEnter($event,index)" @mouseleave="imgLeave($event,index)" @click="showImage(index,false)">
										<img class="object-fit-cover w-100 h-100 border-radius-5" :src="transbegin!==2?item.chooseimg:item.preview" onerror="this.src='img/error.jpg'"/>
										<div class="position-absolute bg-80-5 w-100 h-100 z-1 top-0 d-flex justify-content-center align-items-center" v-if="hoverIndex===index">
											<p class="w-80 c-white font-weight line-height-20 text-clip">{{item.name}}</p>
										</div>
										<div class="position-absolute right--9 top--9 bg-white w-18-px h-18-px border-radius-9px z-2" v-if="transbegin===0">
											<button class="layui-icon layui-icon-close bg-none border-none font-18 c-red padding-0 w-100 h-100" @click="deleteUp(index,this)"></button>
										</div>

										<div class="position-absolute left-0 top-0 w-100 h-100 bg-black-5 d-flex align-items-center justify-content-center" v-if="transbegin===1&&item.status!==1&&item.status!==-1">
											<img class="load" src="img/loading.png" width="30" height="30" alt="loading">
										</div>
										<div class="position-absolute left-0 top-0 w-100 h-100 d-flex align-items-start justify-content-end" v-if="transbegin!==0&&item.status===1">
											<div class="position-relative bg-green padding-5-10 border-top-right-5px border-bottom-left-10px">Successes</div>
										</div>
<!--										<div class="position-absolute left-0 top-0 w-100 h-100 d-flex align-items-end justify-content-end z-2" v-if="transbegin!==0&&item.status===1">-->
<!--											<a class="d-flex justify-content-center padding-5" :href="item.download" :download="item.download">-->
<!--												<i class="layui-icon layui-icon-download-circle c-white"></i>-->
<!--											</a>-->
<!--										</div>-->
										<div class="position-absolute left-0 top-0 w-100 h-100 d-flex align-items-start justify-content-end" v-if="transbegin!==0&&item.status===-1">
											<div class="position-relative bg-red padding-5-10 border-top-right-5px border-bottom-left-10px">Failed</div>
										</div>

									</div>
								</div>
							</div>

						</div>


						<!-- button-->
						<div class="w-100 align-items-center flex-wrap justify-content-center margin-top-20 margin-bottom-30 web-flex" v-if="getImgLength()>0">
							<button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px w-100 font-family-Bangers" @click="uploadAll()" v-if="transbegin!==2" :disabled="transbegin===1">
								TRANSLATE ({{getFinishCount()+ "/"+getImgLength()}})
							</button>

<!--							<button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px w-100 font-family-Bangers" @click="downloadAll()"  v-if="transbegin===2" :disabled="checkUploadDown()">-->
<!--								Download All-->
<!--							</button>-->
						</div>
					</div>
				</div>
			</div>

			<!-- button-->
			<div class="position-fixed w-100 h-60-px align-items-center justify-content-center phone-flex bottom-0 z-index-9999999 bg-black" v-if="getImgLength()>0">
				<button class="w-80 btn-upscale d-flex align-items-center justify-content-center h-40-px font-family-Bangers" @click="uploadAll()" v-if="transbegin!==2" :disabled="transbegin===1">
					TRANSLATE ({{getFinishCount()+ "/"+getImgLength()}})
				</button>

<!--				<button class="w-80 btn-upscale d-flex align-items-center justify-content-center h-40-px font-family-Bangers" @click="downAll()" v-if="transbegin===2" :disabled="checkUploadDown()">-->
<!--					Download All-->
<!--				</button>-->
			</div>
			<div class="h-60-px"></div>
		</main>

		<!-- footer -->
		<footer class="py-vh-4 border-top border-dark footer"></footer>


		<script src="js/loadJS.js"></script>
		<script>
			let searchObj=getHtmlSearchMap();
			let transpic = new Vue({
				el: "#transpic",
				data:{
					uppics:{
						// "1":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:1},
						// "2":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:-1},
						// "3":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:1},
						// "4":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:-1},
						// "5":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:1},
						// "6":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:0},
						// "7":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "8":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "9":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "11":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "21":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:0},
						// "31":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "41":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:0},
						// "51":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "61":{preview:"img/n1.jpg",chooseimg:"img/o1.jpg",status:0},
						// "71":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "81":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
						// "91":{preview:"img/n.jpg",chooseimg:"img/o.jpg",status:0},
					},
					timers: {},
					size:0,
					transbegin:0,
					radios:[{name:"English",value:"en"},{name:"Indonesian",value:"id"},{name:"Chinese",value:"zh"}],
					lang:"en",
					user:null,
					isphone:isPhone(),
					percents:{},
					downfailed:false,
					hoverIndex:-1,
					originOpen:false,
					isFull:false,
					imgIndex:0,
					tasks:{},
				},
				mounted(){
					onLoad();
					currentVue=this;
					this.show=true;
					this.initForm();
					this.reStep();
				},
				methods:{
					test(){
						coinDialog(this);
					},
					prevEvent(){
						this.imgIndex=(this.imgIndex-1)<0?0:(this.imgIndex-1);
					},
					nextEvent(){
						let len=this.getImgLength();
						this.imgIndex=(this.imgIndex+1)>=len?len-1:(this.imgIndex+1);
					},
					getResultClassName(){
						let name=this.transbegin!==0?'d-flex':'d-none';
						if(this.isFull){
							name+=" position-fixed left-0 top-0 z-index-9999999 h-100";
						}else{
							if(this.isphone){
								name+=" main-h";
							}else{
								name+=" h-640-px";
							}
						}
						return name;
					},
					getPro(){
						return (this.getFinishCount()+1)+" / "+this.getImgLength();
					},
					getImageName(src){
						let arr=src.split("/");
						console.log(arr);
						return arr[arr.length - 1];
					},
					showRest(){
						this.downfailed=true;
					},
					imgEnter(event,index){
						this.hoverIndex=index;
					},
					imgLeave(event,index){
						this.hoverIndex=-1;
					},
					Full(){
						this.isFull=!this.isFull;
					},
					initForm(){
						let that=this;
						layui.use('form', function(){
							let form = layui.form;
							form.on('switch(open)', function(data){
								that.originOpen=this.checked;
								return false;
							});
						});
					},
					getClassName(){
						let name="justify-content-center";
						if(this.getImgLength()>0){
							name="justify-content-start";
						}
						if(this.isphone){
							name+=" margin-top-20"
						}
						if(this.transbegin===1){
							name+=" pointer-events-none";
						}
						return name;
					},
					showBatch(){
						return this.user&&this.user.vip>0&&this.transbegin===0&&this.getImgLength()<20;
					},
					setUser(user){
						if(user){
							this.user=user;
							this.$forceUpdate();
						}
						//this.reStep();
					},
					reStep(){
						let url=window.location.href;
						let data=getPageData(url);
						removePageData(url);
						if(data){
							this.uppics=data.uppics;
							this.transbegin=data.transbegin;
							this.lang=data.lang;
							this.size=data.size;
							let index=data.index;
							let senddata=data.senddata;
							if(searchObj["yes"]){
								this.Translate(index,senddata);
							}else{
								this.transbegin=0;
							}
						}
					},
					savePageData(key,senddata){
						let url=window.location.href;
						let data={
							transbegin:this.transbegin,
							uppics:this.uppics,
							lang:this.lang,
							index:getIndex(key),
							size:this.size,
							senddata:senddata
						}
						savePageData(url,data);
					},
					getIndex(key){
						let keys=Object.keys(this.uppics);
						for(let i=0;i<keys.length;i++){
							if(key===keys[i]){
								return i;
							}
						}
						return 0;
					},
					getImgLength(){
						return getImgLength(this);
					},
					parseSize(){
						return parseSize(this.size);
					},
					parseImgStatus(key) {
						return parseImgStatus(key, this);
					},
					getImageStatus(key){
						return this.uppics[key].status;
					},
					scrollIntoKuang(){
						setTimeout(function (){
							const targetElement = document.getElementById("kuang");
							if(targetElement){
								targetElement.scrollIntoView(false);
							}
						},10)
					},
					uploadData(key){
						let item=this.uppics[key];
						let upload=item["upload"];
						item.senddata={
							task_type:"IMAGE_TRANSLATE",
							input:{img_original:"",target_lang:this.lang}
						};
						this.uppics[key]=item;

						let token=localStorage.getItem("tk");
						if(token!=null&&token!==""){
							this.uppics[key].status=2;
							if(upload!==null&&upload!==undefined){
								upload.obj.upload(upload.index, upload.file);
							}else{
								createTask(item["senddata"],key,this,function(){
									this.$forceUpdate();
								});
							}
						}else if(!this.showlogin){
							this.showlogin=true;
							loginDialog(this);
						}
					},
					uploadAll(){
						this.transbegin=1;
						let keys= Object.keys(this.uppics);
						for(let i=0;i<keys.length;i++){
							let key=keys[i];
							this.uploadData(key);
						}
						loadProgress();
						this.scrollIntoKuang();
						this.$forceUpdate();
					},
					changeLang(e){
						this.lang=e.target.value;
					},
					
					deleteUp(index){
						return deleteUp(index,this);
					},
					downAll(){
						downloadAll(this.uppics,this);
					},
					getMengClass(item){
						return getMengClass(item,this);
					},
					checkUploadDown(){
						return !checkUploadDown(this);
					},
					Download(){
						Download(this);
					},
					Reset(){
						this.hoverIndex=-1;
						this.originOpen=false;
						this.isFull=false;
						this.imgIndex=0;
						reset(this);
					},
					getImage(){
						let keys=Object.keys(this.uppics);
						if(keys.length>0){
							return this.uppics[keys[this.imgIndex]];
						}
						return 0;
					},
					getFinishCount(){
						let count=0;
						for(let k in this.uppics){
							let item=this.uppics[k];
							if(item.status===1||item.status===-1){
								count++;
							}
						}
						return count;
					},
					showImage(key1,or){
						event.stopPropagation();
						if(this.tasks[key1]){
							let winWidth=window.innerWidth;
							let winHeight=window.innerHeight;
							localStorage.removeItem("mangaimage");
							let list=[];
							let index=0;
							for(let key in this.tasks){
								if(key1===key){
									index=list.length;
								}
								list.push(this.tasks[key]);
							}
							localStorage.setItem("mangaimages",JSON.stringify(list));
							layer.config({
								extend: '../../../css/manga.css' //同样需要先加载新皮肤
							});

							layer.open({
								type: 2,
								skin: 'layer-manga-skin',
								closeBtn:0,
								area: [winWidth+'px', winHeight+'px'],
								content: ['../common/mangaimage.html?o='+or+"&index="+index+"&type=0","no"] //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
							});
						}

						// let node=event.srcElement;
						// let src=node.src;
						// if(src&&src!==""){
						// 	let photos={
						// 		title:"",
						// 		id:"1",
						// 		start:0,
						// 		data:[{src:src}]
						// 	}
						// 	layer.photos({
						// 		photos:photos,
						// 	})
						// }
						return false;
					}
				}
			});
			function setUser(user){
				transpic.setUser(user);
			}
			maininit(transpic,"img_original-multiple");
			
		</script>
		<script type="module" src="js/firebase.js"></script>
	</body>
</html>