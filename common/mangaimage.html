<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
    <link rel="stylesheet" href="../css/main.css?v=1.0.113">
    <script src="../frame/layui/layui.js"></script>
    <script src="../frame/vue.js"></script>
    <title>Little Grass - Setting</title>
</head>
<body class="vw100 bg-opacity-0 text-white">
    <div class="w-100-vw h-100-vh d-flex justify-content-center align-items-center position-relative" id="manga-image" @click="close">
        <div class="position-relative h-80 d-flex justify-content-center" @click="noClose" v-if="item" id="downview">
            <img class="object-fit-contain" :src="(type===0||ori)?(item.input['img_original']):(item.output['img_result'])" height="100%" alt="image" id="image">
<!--            <img class="object-fit-contain" src="https://storage.googleapis.com/storage/v1/b/lg-mytoon-pub-asia/o/home%2F600334%2F53dfe532-abea-48a2-aa5f-d04d85d139fb.png?alt=media" height="100%" alt="image" id="image">-->
            <div class="position-absolute w-100 h-100" :class="ori?'opacity-0':'opacity-100'">
                <div class="position-absolute bg-white text-bg border-radius-5" :style="'left:'+item['bound'][3].x+'px;top:'+item['bound'][0].y+'px;width: '+(item['bound'][1].x-item['bound'][0].x+10)+'px;height:'+(item['bound'][2].y-item['bound'][1].y+10)+'px'"
                     v-for="(item,index) in translates" :key="index">
                    <span class="c-black font-family-Bangers word-wrap user-select-none" id="text" :class="showText?'opacity-100':'opacity-0'">{{item.text}}</span>
                </div>
            </div>
        </div>
        <img class="position-absolute bottom-50 left-50px" src="../img/showLeft.png" width="60" alt="ori" @click="changeIndex(-1)">
        <img class="position-absolute bottom-50 right-50px" src="../img/showRight.png" width="60" alt="ori" @click="changeIndex(1)">
        <img class="position-absolute bottom-50--60px right-50" @click="changeOri" :src="ori?'../img/img_open.png':'../img/img_close.png'" width="60" alt="ori">
        <img class="position-absolute bottom-50--120px right-50" @click="download()" src="../img/download.png" width="60" alt="down" v-if="type===0">
        <a :href="item.output['img_result_download']" :download="item.output['img_result_download']" @click="noClose" v-if="type!==0&&item">
            <img class="position-absolute bottom-50--120px right-50" src="../img/download.png" width="60" alt="down">
        </a>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.2/html2canvas.min.js"></script>
    <script>
        function getParameterByName(name, url) {
            let map={};
            url=url.substring(1);
            let list=url.split("&");
            for(let i=0;i<list.length;i++){
                let arr=list[i].split("=");
                map[arr[0]]=arr[1];
            }
            return map[name];
        }
        let ori=getParameterByName("o",window.location.search);
        ori=ori === "true";
        let index=getParameterByName("index",window.location.search)??"0";
        index=parseInt(index);

        let type= getParameterByName("type",window.location.search)??"1";
        type=parseInt(type);

        let isCover= getParameterByName("isCover",window.location.search)??"false";
        isCover=isCover === "true";

        let item = localStorage.getItem("mangaimage");
        if(item!=null){
            item=JSON.parse(item);
        }

        let list=localStorage.getItem("mangaimages");
        if(list!=null){
            list=JSON.parse(list);
        }else{
            list=[];
        }
        let mangaImage = new Vue({
            el: "#manga-image",
            data:{
                item: null,
                imageOWidth:0,
                imageOHeight:0,
                imageWidth:0,
                imageHeight:0,
                scale:-1,
                translates:[],
                showText:false,
                ori: ori,
                list:list.length===0?[item]:list,
                currentIndex: index,
                type: type
            },
            mounted(){
                this.init();
            },
            methods:{
                init(){
                    this.item=this.list[this.currentIndex];
                    if(this.type===0){
                        let that=this;
                        setTimeout(function (){
                            that.getSize();
                        },10)
                    }
                },
                download(){
                    event.stopPropagation();
                    let name=this.item["input"]["img_original"];
                    name=name.substring(name.lastIndexOf("/")+1);

                    html2canvas(document.getElementById('downview'), {
                        useCORS: true, // 设置useCORS为true，开启CORS支持
                        //proxy: '/proxy', // 如果需要，可以设置一个代理URL
                        onrendered: function(canvas) {

                        },
                        //logging: true // 启用日志记录，以便查看可能出现的错误
                    }).then(canvas => {
                        let img = canvas.toDataURL('image/png');
                        // 创建一个链接元素
                        let link = document.createElement('a');
                        link.href = img;
                        link.download = name; // 文件名
                        link.click();
                    });

                    // html2canvas(document.getElementById('downview')).then(canvas => {
                    //     // 创建一个图片元素
                    //     let img = canvas.toDataURL('image/png');
                    //
                    //     // 创建一个链接元素
                    //     let link = document.createElement('a');
                    //     link.href = img;
                    //     link.download = name; // 文件名
                    //     link.click();
                    // });
                },
                getSize(){
                    let arr=JSON.parse(JSON.stringify(this.item["output"]["translated_text"]));
                    if(arr){
                        this.getImageSize(this.item.input["img_original"],arr);
                        let img=document.getElementById("image");
                        this.imageWidth=img.width;
                        this.imageHeight=img.height;
                    }
                },
                changeIndex(offset){
                    event.stopPropagation();
                    if(isCover){
                        return;
                    }
                    this.currentIndex=(this.currentIndex+offset)%this.list.length;
                    this.showText=false;
                    this.scale=-1;
                    this.translates=[];
                    this.init();
                },
                changeOri(){
                    event.stopPropagation();
                    if(isCover){
                        return;
                    }
                    this.ori=!this.ori;
                },
                noClose(){
                    event.stopPropagation();
                },
                close(){
                    if(isCover){
                        return;
                    }
                    parent.layer.close(parent.layer.getFrameIndex(window.name));
                },
                fitText(container, textElement,fontSize) {
                    const containerHeight = container.offsetHeight;
                    let textOriginalHeight = textElement.offsetHeight;
                    textElement.style.fontSize = fontSize + 'px';
                    textOriginalHeight=textElement.offsetHeight;
                    if (textOriginalHeight <= containerHeight) {
                        return;
                    }

                    this.fitText(container, textElement, fontSize-1);
                },
                getImageSize(url,arr) {

                    let that=this;
                    let img = new Image();
                    img.onload = function() {
                        that.imageOWidth=img.width;
                        that.imageOHeight= img.height;
                        let scale=that.imageWidth/that.imageOWidth;

                        for(let i=0;i<arr.length;i++){
                            let text=arr[i]["translated_text"]["en"];
                            if(!text){
                                text=arr[i]["translated_text"]["zh"];
                                if(!text){
                                    text=arr[i]["translated_text"]["id"];
                                }
                            }
                            let bound=arr[i].bound;
                            for(let j=0;j<bound.length;j++){
                                bound[j].y=bound[j].y*scale;
                                bound[j].x=bound[j].x*scale;
                            }
                            let a={
                                bound: bound,
                                text: text,
                            };
                            that.translates.push(a);
                        }
                        that.scale=scale;
                        setTimeout(function (){
                            let nodes=document.getElementsByClassName("text-bg");
                            for(let k=0;k<nodes.length;k++){
                                let node=nodes[k];
                                let text=node.children[0];
                                that.fitText(node, text, 50);
                            }
                            that.showText=true;
                        },500);
                    };
                    img.onerror = function() {

                    };
                    img.src = url;
                }
            }
        });
    </script>

</body>
</html>