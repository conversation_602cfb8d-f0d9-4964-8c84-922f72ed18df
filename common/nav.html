<div id="nav" class="vw100 h-92px bg-1b1c1e fixed-top">
	<div class="h-100 px-vw-5 d-flex justify-content-between align-items-center">
		<!--h5 menu icon-->
		<div class="w-60-px h-100 align-items-center display-flex-h5">
			<i class="font-24 c-white layui-icon layui-icon-spread-left" @click="open()"></i>
		</div>

		<!--logo title-->
		<div class="h-100 d-flex align-items-center">
			<a class="font-24 c-white font-weight text-center phone-flex align-items-center" href="/index.html?key=1" target="_top">{{title}}</a>
			<a class="font-24 c-white text-center web-flex align-items-center" href="/index.html?key=1" target="_top">
				<img src="/img/logo/180.png" width="32" height="32" alt="icon" class="margin-right-10">Little Grass
			</a>
		</div>

		<!--menu-->
		<div class="h-100 align-items-center justify-content-between display-flex-web">
			<ul class="layui-nav h-100 bg-none align-items-center justify-content-between display-flex-web" v-if="user">
				<li class="layui-nav-item f-20 col-12 col-md-auto text-center margin-right-20 align-items-center pointer-events-auto d-flex c-white">
					<a href="#" class="c-white f-20" style="font-weight: 600">Products</a>
					<dl href="../index.html#index-manga?key=0" class="layui-nav-child flex-column bg-1b1c1e border-none border-radius-5 bg-2C2D31" style="font-weight: 400">
						<dd class="bg-none"><a href="/manga.html?key=1" class="font-16 font-weight-400 c-white d-flex justify-content-start product-item h-40-px">Ai Manga</a></dd>
						<dd class="bg-none"><a href="/upscale.html?key=1" class="font-16 font-weight-400 c-white d-flex justify-content-start product-item h-40-px">Ai Upscale</a></dd>
						<dd class="bg-none"><a href="/outpaint.html?key=1" class="font-16 font-weight-400 c-white d-flex justify-content-start product-item h-40-px">Ai Outpainting</a></dd>
						<dd class="bg-none"><a href="/chat.html?key=1" class="font-16 font-weight-400 c-white d-flex justify-content-start product-item h-40-px">Ai Chat</a></dd>
					</dl>
				</li>
				<li class="layui-nav-item f-20 col-12 col-md-auto text-center margin-right-20 align-items-center pointer-events-auto d-flex c-white">
					<a href="../about.html?key=1" class="c-white f-20" style="font-weight: 600">About Us</a>
				</li>
				<li class="layui-nav-item f-20 col-12 col-md-auto text-center margin-right-20 align-items-center pointer-events-auto d-flex c-white">
					<a href="../support.html?key=1" class="c-white f-20" style="font-weight: 600">Support</a>
				</li>
				<li class="layui-nav-item f-20 col-12 col-md-auto text-center margin-right-20 align-items-center pointer-events-auto d-flex c-white">
					<a href="../contact.html?key=1" class="c-white f-20" style="font-weight: 600">Contact Us</a>
				</li>
			</ul>
		</div>

		<!--login and head-->
		<div class="w-150-px h-100 d-flex align-items-center justify-content-center" v-if="!user">
			<a href="/login.html" class="h-30-px line-height-30 bg-cdee2d border-radius-20 c-black font-12 font-weight-600 display-flex-web padding-0-16">
				Login
			</a>
			<a href="/login.html" class="w-60-px bg-cdee2d c-black font-12 font-weight-600 display-flex-h5 line-height-30 border-radius-20">
				Login
			</a>
		</div>

		<div class="w-150-px h-100 d-flex align-items-center justify-content-center" v-if="user">
			<div class="position-relative d-flex">
				<a :href="isPhone?'javascript:;':'setting.html'" target="_top" class="btn d-flex position-relative padding-0">
					<img class="head" :class="user?'d-flex':'d-none'" src="/img/head.png" id="head" onerror="this.src='img/head.png'" alt="head">
				</a>
			</div>
		</div>
	</div>

	<!--h5 menus-->
	<div class="w-100 fixed-top h-100-vh z-index-99999999" id="phonemenu" v-if="isOpen">
		<div class="d-flex w-100 bg-white-30 h-100 position-fixed meng" v-if="isPhone" @click="close">
			<div class="min-width-180 d-flex flex-column flex-nowrap h-100 bg-393D49 padding-10" @click="None">
				<div class="w-100 h-30-px"></div>
				<a class="w-100 h-40-px c-white text-decoration-none margin-left-10" href="/index.html?key=0">Home</a>
				<div class="w-100 h-40-px c-white text-decoration-none margin-left-10"
				   @click="openProduct('Products')">Products</div>
				<div class="w-100 h-40-px c-white text-decoration-none margin-left-10"
					 @click="openProduct('About Us')">About Us</div>
				<div class="w-100 h-40-px c-white text-decoration-none margin-left-10"
					 @click="openProduct('Support')">Support</div>
				<div class="w-100 h-40-px c-white text-decoration-none margin-left-10"
					 @click="openProduct('Contact Us')">Contact Us</div>
			</div>
		</div>
	</div>

	<!--h5 products-->
	<div class="w-100 fixed-top h-100-vh z-index-99999999" id="phoneproducts" v-if="isProductOpen">
		<div class="d-flex w-100 bg-white-30 h-100 position-fixed meng" v-if="isPhone" @click="close">
			<div class="min-width-180 d-flex flex-column flex-nowrap h-100 bg-393D49 padding-10" @click="None">
				<div class="w-100 h-30-px"></div>
				<a class="w-100 h-40-px c-white text-decoration-none margin-left-10" href="../manga.html?key=1" >Ai Manga</a>
				<a class="w-100 h-40-px c-white text-decoration-none margin-left-10" href="../upscale.html?key=1" >Ai Upscale</a>
				<a class="w-100 h-40-px c-white text-decoration-none margin-left-10" href="../outpaint.html?key=1" >Ai Outpainting</a>
				<a class="w-100 h-40-px c-white text-decoration-none margin-left-10" href="../chat.html?key=1" >Ai Chat</a>
			</div>
		</div>
	</div>
</div>

<script src="https://cdn.auth0.com/js/auth0/9.11/auth0.min.js"></script>
<script>

	let isApp = getHtmlHash();
	let pageIndex=getHtmlKey();
	//Vue.use(httpVueLoader);
	let nav = new Vue({
		el: "#nav",
		data:{
			title:"Little Grass",
			user: null,
			// user:{
			// 	avatar:"",
			// 	name: "",
			// 	nickname: ""
			// },
			isPhone:isPhone(),
			isOpen:false,
			isProductOpen:false,
			billing_url:"",
			infos:[],
			toolIndex:0,
		},
		mounted(){
			localStorage.removeItem("login");
			localStorage.removeItem("paycoin");
			showDia=false;
			navView=this;
			//localStorage.setItem("tk","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTczNTg5MjY1NSwianRpIjoiMjc0MzhmMjUtYTVlOC00M2RkLWEyODktNThiYjEzZTI5NmFkIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6NjAwMzM0LCJuYmYiOjE3MzU4OTI2NTUsImNzcmYiOiI5MzRkNzM0Mi1iYjYzLTRlNjMtODIxZS0yOWMxNjY0YWVjNWUiLCJleHAiOjE3Mzg0ODQ2NTV9.vmfuURLkr9fcMLvrBIeC1GcPeMmgBEg144IuyM5Rd3s");
			this.getTitle();
			this.navInit();
		},
		methods:{
			openProduct(title){
				if(title==="Products"){
					this.isopen=false;
					this.isProductOpen=true;
				}else{
					let map={
						"About Us" 	 : "../about.html?key=1",
						"Support" 	 : "../support.html?key=1",
						"Contact Us" : "../contact.html?key=1",
					}
					location.href=map[title];
				}
			},
			getTitle(){
				let titles={
					"":"Little Grass",
					"index":"Little Grass",
					"generatevideo":"Video",
					"transpic":"Ai Manga",
					"upscale":"Upscale",
					"outpaint":"Outpaint",
					"chat":"Chat",
					"paycoin":"Buy Coin",
					"payvip":"Buy Vip",
					"task":"Task",
					"pricing":"Pricing",
					"manga":"Ai Manga",
					"api":"API"
				}

				let pathname=location.pathname;
				let lastIndex=pathname.lastIndexOf("/");
				pathname=pathname.substring(lastIndex+1);
				let path=pathname.split(".")[0];
				let infos=["Information","Activity"];
				if(titles[path]){
					this.title=titles[path];
				}else if(infos[pageIndex]){
					this.title=infos[pageIndex];
				}else{
					this.title="Little Grass";
				}
			},
			None(){
				event.stopPropagation();
				return false;
			},
			open(){
				this.isOpen=true;

			},
			close(){
				this.isOpen=false;
				this.isProductOpen=false;
			},
			logout(){
				window.parent.logout();
			},
			navInit() {
				setHistory();
				window.history.replaceState({},'',location.origin+location.pathname);
				this.checkUser();
			},
			checkUser(){
				let user= localStorage.getItem("user");
				if(pageIndex!==-1&&user){
					user=JSON.parse(user);
					this.user=user;
					let src=user["avatar"];
					if(src){
						setTimeout(function (){
							let img=document.getElementById("head");
							img.src=src;
						},10);
					}
					this.loadInfos();
					if(typeof(setUser)==="function"){
						setUser(user);
					}
				}else{
					let that=this;
					let token=localStorage.getItem("tk");
					if(token){
						getUser(null,this,function(res){
							if(res){
								that.user=res;
								localStorage.setItem("user",JSON.stringify(res));
								let src=res["avatar"];
								if(src){
									setTimeout(function (){
										let img=document.getElementById("head");
										img.src=src;
									},10);
								}

								that.loadInfos();
								//that.getSubscriptionLink();
								if(typeof(setUser)==="function"){
									setUser(res);
								}
							}
						});
					}
				}

			},

			loadInfos(){
				if(this.user){
					let that=this;
					layui.use(["element",'dropdown'], function(){
						let dropdown = layui.dropdown
						dropdown.render({
							elem: '#head', //可绑定在任意元素中，此处以上述按钮为例
							data: infos,
							id:"head",
							trigger:that.isPhone?'mousedown':"hover",
							align:"center",
						});
					});
				}
			},
			getSubscriptionLink(){
				var that=this;
				get("/home/<USER>/subscription_link",null,"GET",function(res){
					if(res.code===1){
						that.billing_url=res.data.billing_url;
					}
					that.loadInfos();
				});
			},
			DingYue(){
				window.open(this.billing_url,"_blank");
			}
		}
	});
	function imgError(){
		let img=event.srcElement;
		img.src="img/head.png";
		img.onerror=null;
	}
	function mouseMove(obj,src){
		let img= obj.children[0];
		if(src){
			img.src=src;
		}

	}
</script>