<div class="w-100 d-flex flex-column align-items-center justify-content-center position-relative" id="vue-progress">
    <div class="w-20 position-relative" :class="isphone?'':'w-10'">
        <div class="wave-content">
            <img class="load0" src="img/loading0.png"/>
            <img class="load1" src="img/loading1.png"/>
        </div>
    </div>
    <a class="w-100 text-center position-relative margin-top-20 c-white" :class="tipIndex===1?'c-ccc text-decoration-underline':'c-white'"
       :href="tipIndex===1?('task.html?key='+key):'javascript:;'" :target="tipIndex===1?'_blank':''" id="tip">Processing...</a>
    <p class="margin-top-20 font-weight font-18">{{getPro()!==""?('('+getPro()+')'):''}}</p>
</div>

<script id="progress-js">
    progress = new Vue({
        el: "#vue-progress",
        data:{
            tipIndex:0,
            estimate_time:0,
            estime:0,
            timer:null,
            timer1:null,
            timer2:null,
            isphone:isPhone(),
            key:1,
        },
        mounted(){
            this.init();
        },
        methods:{
            init(){
                let arr={
                    "transpic":0,
                    "enhancement":1,
                    "outpaint":2,
                    "generatevideo":3
                }
                let path=location.pathname.slice(1).split(".")[0];
                this.key=arr[path];
                this.initData();
                this.esTimer();
                this.changeTip(1);
                this.setPercent(-2);
            },
            getPro(){
                if(typeof(transpic)!=="undefined"){
                    return transpic.getPro();
                }
                return "";
            },
            initData(){
                this.tipIndex=0;
                this.estimate_time=0;
                this.estime=0;
                clearTimeout(this.timer);
                clearTimeout(this.timer1);
                clearTimeout(this.timer2);
                this.timer=null;
                this.timer1=null;
                this.timer2=null;
            },
            setTimeLeft(){
                let node=document.getElementById("tip");
                if(node){
                    clearTimeout(this.timer);
                    let curStr=node.innerHTML;
                    if(curStr.indexOf("Processing")===-1&&curStr.indexOf("You can")===-1){
                        let time=Math.floor(this.estimate_time-this.estime);
                        if(time<0){
                            time=0;
                        }
                        let str="It is estimated that there will be "+time+"s";
                        if(isNaN(time)){
                            str="Processing...";
                        }
                        if(node){
                            node.innerHTML=str;
                        }
                        let that=this;
                        this.timer=setTimeout(function (){
                            that.setTimeLeft();
                        },1000)
                    }
                }
            },
            changeTip(index){
                index=index?0:1;
                let tips=["Processing...","You can click this link to view the task list."];
                let time=Math.floor(this.estimate_time-this.estime);
                if(time<=0){
                    time=0;
                    index=0;
                }else{
                    tips[0]="It is estimated that there will be "+time+"s";
                }

                clearTimeout(this.timer1);
                let that=this;
                let node=document.getElementById("tip");
                if(node){
                    this.tipIndex=index;
                    node.innerHTML=tips[index];
                    this.setTimeLeft();
                    this.timer1=setTimeout(function (){
                        that.changeTip(index);
                    },5000)
                }else{
                    this.timer1=setTimeout(function (){
                        that.changeTip(index);
                    },5000)
                }
            },
            esTimer(){
                clearTimeout(this.timer2);
                let that=this;
                this.timer2=setTimeout(function (){
                    that.estime+=0.1;
                    setPercent(that.estime/that.estimate_time*100);
                    if(that.estime<that.estimate_time||that.estimate_time===0){
                        that.esTimer();
                    }
                },100);
            },
            getPolygon0(per){
                let pos="";
                for(let i=0;i<=100;i+=10){
                    let y=100-per-2*Math.cos(2*i+10);
                    let p=i+"% "+y+"%";
                    if(pos!==""){
                        pos+=",";
                    }
                    pos+=p;
                }
                pos+=",100% 100%";
                pos+=",0% 100%";
                return "polygon(" + pos + ")";
            },

            getPolygon1(per){
                let pos="";
                for(let i=0;i<=100;i+=10){
                    let y=100-per-2*Math.sin(2*i+4)+1;
                    let p=i+"% "+y+"%";
                    if(pos!==""){
                        pos+=",";
                    }
                    pos+=p;
                }
                pos+=",100% 100%";
                pos+=",0% 100%";
                return "polygon(" + pos + ")";
            },

            setPercent(per){
                //console.log(per);
                if(per<=100){
                    let polygon0=this.getPolygon0(per);
                    document.documentElement.style.setProperty('--polygon0', polygon0);
                    let polygon1=this.getPolygon1(per);
                    document.documentElement.style.setProperty('--polygon1', polygon1);
                }
            },
        }
    });

    function setPercent(per){
        //console.log(per);
        progress.setPercent(per);
    }

    function initData(){
        progress.initData();
    }
</script>


<style>
    .wave-content{
        width: 100%;
        height: 100%;
        margin-left: 10%;
        margin-right: 10%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .wave-content .load1{
        width: 100%;
        position: absolute;
    }

    .wave-content .load0{
        width: 100%;
        position: relative;
    }

    .wave-content .load1{
        z-index: 2;
        animation: animate 1s ease-in-out infinite;
    }

    @keyframes animate {
        0%,100% {
            clip-path: var(--polygon0);
        }
        50% {
            clip-path: var(--polygon1);
        }
    }
</style>