<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
		<link rel="apple-touch-icon" sizes="180x180" href="img/icon/apple-touch-icon96.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="32x32" href="img/icon/32.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="16x16" href="img/icon/16.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="96x96" href="img/icon/96.png?v=1.0.104">
		<link rel="stylesheet" href="./css/main.css?v=1.0.113">
		<script src="frame/jquery.js"></script>
		<script src="frame/layui/layui.js"></script>
		<script src="frame/vue.js"></script>
		<title>Little Grass - AI Tools & Outpaint</title>
	</head>
	<body class="bg-1b1c1e text-white mt-0">
		<!--header-->
		<header class="fixed-top header"></header>

		<!--main-->
		<main class="w-100 position-relative" id="extended">
			<div class="w-100 h-92px"></div>
			<div class="w-100 web-h-100--bar d-flex flex-wrap justify-content-center">
				<!--left panel-->
				<div class="web-w-78 web-h-100-canvas web-bg-005fff position-relative">

					<!--canvas-->
					<div class="w-100 h-85 d-flex flex-column align-items-center justify-content-center position-relative"
						 :class="getImgLength()>0?'d-flex':'d-none'" id="canvas-bg">
						<div class="d-flex position-relative justify-content-center align-items-center transition disabled-touch-event" id="canvas">
							<div class="w-100 h-100 object-fit-contain d-flex justify-content-center align-items-center position-relative">
								<img class="object-fit-contain position-relative transition disabled-touch-event" :class="getImgWH()" id="preview_img" :src="getImage().preview??getImage().chooseimg"/>
							</div>
							<div class="position-absolute stretch-box disabled-touch-event " id="stretch-box" :class="transbegin===0?'d-flex':'d-none'"></div>
<!--							<div class="w-100 h-100 position-absolute bg-80-5 z-index-99 d-flex justify-content-center align-items-center" v-if="transbegin===1">-->
<!--								<div class="w-30-0 position-relative wave" id="lg"></div>-->
<!--							</div>-->
							<div class="position-absolute w-100 h-100 bg-black-5 d-flex align-items-center justify-content-center" v-if="getImgLength()>0&&transbegin===1">
								<img class="load" src="img/loading.png" width="50" height="50" alt="loading">
							</div>
						</div>
					</div>

					<!--ex-->
					<div class="w-100 h-100 d-flex justify-content-center align-items-center" v-if="transbegin===0&&getImgLength()===0">
						<div class="w-75 h-75 d-flex flex-column align-items-center justify-content-center">
							<img class="extend-img w-90 h-90 object-fit-contain" src="img/outpainting.png"/>
						</div>
					</div>

					<!--tool menu-->
					<div class="phone-w-100 web-tool-position-absolute web-right-80px web-bottom-40px web-bg-010fff border-radius-5 phone-z-index-99999" id="tool-menu">
						<div class="align-items-center web-slider-padding-10" :class="selectbtn===0?'d-flex':'d-none'">
							<div class="web-w-200px a-slide" id="slide"></div>
							<p class="font-8 h-100">{{slidervalue}}%</p>
						</div>

						<div class="align-items-center padding-10"  :class="selectbtn===1?'d-flex':'d-none'">
							<div class="web-tool-w-60-px h-80-px d-flex justify-content-center" v-for="(item,index) in sizes" :key="index">
								<div class="w-90 h-100 d-flex flex-column align-items-center justify-content-center"  :class="index==selectsize?'a-size-item-this':'border-a'" @click="selectSize(index)">
									<img class="a-size-img" :src="item.src" alt="size"/>
									<p>{{item.title}}</p>
								</div>
							</div>
						</div>

						<div class="align-items-center padding-10 f-wrap web-row-gap-10px"  :class="selectbtn===2?'d-flex':'d-none'">
							<div class="h-40-px w-50 border-radius-10 d-flex align-items-center" v-for="(item,index) in pos" :key="index">
								<p class="a-title1 w-50-px text-right margin-right-10">{{item.name}}:</p>
								<label>
									<input class="bg-80-5 padding-0-10 c-white w-70-px border-none border-radius-10" :id="'pos'+index" @input="inputEvent()"/>
								</label>
								<p class="a-title1 margin-left-10"></p>
							</div>
						</div>
					</div>
				</div>

				<!--right panel-->
				<div class="web-w-22 web-h-100 web-padding-10 web-position-relative phone-z-index-99999" id="right-panel">
					<div class="phone-h-10px"></div>
					<div class="w-100 web-flex flex-column">
						<p class="a-title1">Function Introduction</p>
						<p class="a-des1">Intelligent recognition of various elements in images, including characters, scenes, etc., and adopting different extension algorithms based on different elements.</p>
					</div>

					<div class="web-h-30px-p-0"></div>
					<div class="web-w-100 web-h-240-px" :class="getImgLength()>0?'web-flex':'d-flex'">
						<button class="w-100 h-100 flex-column align-items-center justify-content-center bg-005fff border-radius-10 phone-padding-40-0 phone-border-a-dash" id="img_original"
						:disabled="(getImgLength()>0&&transbegin===0)||transbegin!==0" :class="getImgLength()>0?'d-none':'d-flex'">
							<img class="position-absolute" src="img/upload.png" width="120" height="48" alt="upload">
						</button>
						<div class="w-100 h-100 position-relative d-flex justify-content-center align-items-center bg-005fff" v-if="getImgLength()>0">
							<img class="w-90 h-90 object-fit-contain" :src="getImage().chooseimg" alt="select image">
							<i class="bi bi-trash font-24 c-white position-absolute bottom-10-px right-10" @click="reset()" v-if="transbegin!==1"></i>
						</div>
					</div>
					<div class="web-h-30px"></div>
					<div class="w-100 d-flex flex-column justify-content-center align-items-center">
						<div class="web-w-100-tool d-flex flex-wrap">
							<div v-for="(item,index) in btns" style="width: 72px;" :style="index!=btns.length-1?'margin-right: calc((100% - 288px) / 3)':''">
								<div class="d-flex justify-content-center align-items-center" :class="selectbtn===index?'bg-5865f2':'bg-020fff'" @click="selectBtn(index)" style="width: 72px;height: 72px;border-radius: 8px">
									<img :src="item.icon" width="40" height="40" alt="icon">
								</div>
								<p class="text-center margin-top-10">{{item.name}}</p>
							</div>
						</div>
						<div class="web-h-30px"></div>
						<div class="w-100 d-flex justify-content-center align-items-center">
							<button class="btn-upscale web-w-100-tool web-line-height-48px border-radius-10 font-weight font-family-Bangers" @click="uploadAll()" :disabled="getImgLength()===0" v-if="transbegin==0">Submit</button>
							<button class="btn-upscale web-w-100-tool web-line-height-48px border-radius-10 font-weight font-family-Bangers" :disabled="transbegin==1" v-if="transbegin!=0">
								<a class="d-flex justify-content-center c-white" :href="getImage().download" :download="getImage().download">
									Download
								</a>
							</button>
						</div>
					</div>
				</div>
			</div>
		</main>

		<!-- footer -->
		<footer class="py-vh-4 bg-black footer" id="outpaint-footer"></footer>

		<script src="js/loadJS.js"></script>
		<script>
			let searchObj=getHtmlSearchMap();
			let extended = new Vue({
				el: "#extended",
				data:{
					btns:[
						{name:"Proportional",icon:"img/o1.png"},
						{name:"Fixed",icon:"img/o2.png"},
						{name:"Position",icon:"img/o3.png"},
						{name:"Freely",icon:"img/o4.png"}
					],
					selectbtn:0,
					sizes:sizes,
					selectsize:0,
					initextend:[
						{
							src:"img/0.png",
							value:"Original image",
							"e-src":"img/0-1.png",
							"e-value":"Extend 150%"
						}

					],
					pos:[
						{name:"left",icon:"bi bi-arrow-left"},
						{name:"right",icon:"bi bi-arrow-right"},
						{name:"up",icon:"bi bi-arrow-up"},
						{name:"down",icon:"bi bi-arrow-down"}
					],
					uppics:{
						 //"1":{preview:"img/0-3.png",chooseimg:'img/0.png',status:1,estimate_time:100,estime:0}
					},
					timers: {},
					transbegin:0,
					user:null,
					isphone:isPhone(),
					slidervalue:100,
					isDragging :false,
					startX:0, 
					startY:0, 
					startWidth:0, 
					startHeight:0,
					count:0,
					rect:{left:0,right:0,up:0,down:0},
					scales:[1,1,1,1],
					naturalWidth:0,
					naturalHeight:0,
					tipIndex:0,
					imgwhClass:"w-100 h-100",
				},
				mounted(){
					onLoad();
					this.show=true;
					let that=this;
					setTimeout(function(){
						that.setStretchBoxSize();
					},200)
					initSlider("slide",100,200,25,this);
					getUser(null,this,function(user){
						that.reStep();
					});
				},
				methods:{
					isSubmit(){
						let img=document.getElementById("preview_img");
						let size=this.getStretchBoxSize();
						return img.width===size.width&&img.height===size.height;
					},
					getMainHeight(){
						return "height: "+(window.innerHeight-139)+"px !important;";
						//return window.innerHeight;
						//return getMainHeight();
					},
					checkUploadDown(){
						console.log(checkUploadDown(this));
						return checkUploadDown(this);
					},
					setCanvasScale(scale){
						if(scale){
							this.scales[this.selectbtn]=scale;
						}else{
							scale=this.scales[this.selectbtn];
						}
						let canvas=document.getElementById("canvas");
						canvas.style.transform="scale("+scale+")";
						this.$forceUpdate();
					},
					getImgWH(){
						if(this.getImgLength()>0){
							let bg=document.getElementById('canvas-bg');
							let cs=bg.offsetWidth/bg.offsetHeight;
							if(bg.offsetWidth>0){
								let img=document.getElementById("preview_img");
								let mc=this.naturalWidth===0?(img.naturalWidth/img.naturalHeight):(this.naturalWidth/this.naturalHeight);
								if(mc>cs){
									return this.selectbtn===3?"w-50 h-auto":"w-100 h-auto";
								}else{
									return this.selectbtn===3?"w-auto h-50":"w-auto h-100";
								}
							}else{
								var that=this;
								setTimeout(function (){
									that.setStretchBoxSize();
								},50)
								return this.selectbtn===3?"w-50 h-50":"w-100 h-100";
							}
						}else{
							return "";
						}
					},
					getStretchBoxSize(){
						let box = document.getElementById('stretch-box');
						return {width: box.offsetWidth,height:box.offsetHeight};
					},
					getWidth(){
						if(this.selectbtn!==3){
							let bg=document.getElementById('canvas-bg');
							let maxWidth=bg.offsetWidth*0.9;
							let maxHeight=bg.offsetHeight*0.9;
							let sz=this.getStretchBoxSize();
							let sx=maxWidth/sz.width;
							let sy=maxHeight/sz.height;
							this.setCanvasScale(sx < sy ? sx : sy);
							this.$forceUpdate();
						}else{
							this.setCanvasScale();
						}
					},
					reStep(){
						let url=window.location.href;
						let data=getPageData(url);
						removePageData(url);
						if(data){
							console.log(data);
							this.uppics=data.uppics;
							this.transbegin=data.transbegin;
							this.selectbtn=data.selectbtn;
							this.selectsize=data.selectsize;
							this.slidervalue=data.slidervalue;
							this.startX=data.startX;
							this.startY=data.startY; 
							this.startWidth=data.startWidth;
							this.startHeight=data.startHeight;
							this.rect=data.rect;
							let senddata=data.senddata;
							console.log(senddata);
							initSlider("slide",100,200,25,this,this.slidervalue);
							
							let that=this;
							setTimeout(function(){
								for(let i=0;i<that.pos.length;i++){
									let item=that.pos[i];
									var node=document.getElementById("pos"+i);
									if(node){
										node.value=data.rect[item.name];
									}
								}
								
								that.selectBtn(that.selectbtn);

								if(searchObj["yes"]){
									that.Submit(null,senddata);
								}else{
									that.transbegin=0;
								}
							},50)
							
							
						}else{
							initSlider("slide",100,200,25,this);
						}
					},
					savePageData(key,senddata){
						let url=window.location.href;
						let data={
							transbegin:this.transbegin,
							uppics:this.uppics,
							selectbtn:this.selectbtn,
							selectsize:this.selectsize,
							slidervalue:this.slidervalue,
							startX:this.startX,
							startY:this.startY, 
							startWidth:this.startWidth, 
							startHeight:this.startHeight,
							rect:this.rect,
							senddata:senddata
						}
						savePageData(url,data);
					},
					initNode(ani){
						if(this.getImgLength()>0){
							let that=this;

							let bg=document.getElementById('canvas-bg');
							if(that.isphone){
								bg.addEventListener("touchstart",that.touchDown);
								bg.addEventListener("touchmove",that.touchMove);
								bg.addEventListener("touchend",that.touchUp);
								bg.addEventListener("touchcancel",that.touchUp);
							}else{
								bg.addEventListener("mousedown",that.touchDown);
								bg.addEventListener("mousemove",that.touchMove);
								bg.addEventListener("mouseup",that.touchUp);
								bg.addEventListener("mouseleave",that.touchUp)
							}
							setTimeout(function (){
								that.setStretchBoxSize();
								that.$forceUpdate();
								if(ani){
									that.Animation();
								}else{
									that.isDragging=true;
									setTimeout(function(){
										that.touchMove();
										//that.touchUp();
										that.isDragging=false;
									},10);
								}
							},10)
						}
					},
					getPreviewSize(){
						let node=document.getElementById('preview_img');
						return {width:node.width,height:node.height};
					},
					setStretchBoxSize(size,bound,ismove){
						if(!size){
							size=this.getPreviewSize();
						}
						let img=document.getElementById('preview_img');
						let node=document.getElementById('stretch-box');
						if(bound){
							node.style.width=size.width+bound.left+bound.right+"px";
							node.style.height=size.height+bound.up+bound.down+"px";

							if(this.selectbtn!==3){
								let x=(bound.left-bound.right)/2;
								let y=(bound.up-bound.down)/2;
								img.style.transform = "translate("+x+"px,"+y+"px)";
							}else{
								if(!ismove){
									img.style.transform = "translate(0px,0px)";
								}
								node.style.marginRight=bound.left+"px";
								node.style.marginLeft=bound.right+"px";
								node.style.marginTop=bound.down+"px";
								node.style.marginBottom=bound.up+"px";
							}
						}else{
							node.style.width=size.width+"px";
							node.style.height=size.height+"px";

							if(this.selectbtn===3){
								node.style.marginRight="0px";
								node.style.marginLeft="0px";
								node.style.marginTop="0px";
								node.style.marginBottom="0px";
							}
						}
						if(!ismove){
							this.getWidth();
						}
					},
					sliderChange(value){
						this.slidervalue=value;
						let sc=parseInt(this.slidervalue)/100.0;
						let size= this.getPreviewSize();
						this.setStretchBoxSize({width:size.width*sc,height:size.height*sc});
					},
					resetImgPos(){
						//this.setCanvasScale(1);
						let img=document.getElementById("preview_img");
						img.style.transform = "translate(0px,0px)";

						let node=document.getElementById('stretch-box');
						node.style.marginRight="0px";
						node.style.marginLeft="0px";
						node.style.marginTop="0px";
						node.style.marginBottom="0px";
					},
					selectBtn(index){
						this.selectbtn=index;
						let that=this;
						if(index===0){
							this.resetImgPos();
							setTimeout(function(){
								initSlider("slide",100,200,25,that,that.slidervalue);
								that.sliderChange(that.slidervalue);
							},10)

						}else if(index===1){
							this.resetImgPos();
							setTimeout(function (){
								that.selectSize(that.selectsize);
							},10)

						}else if(index===2){
							this.resetImgPos();
							setTimeout(function (){
								that.inputEvent(true);
							},10)

						}else if(index===3){
							this.resetImgPos();
							this.initNode(this.startX===0);
						}
					},
					getSendData(key){
						let that=this;
						let data={
							task_type:"IMAGE_OUTPAINT",
							input:{
								img_original:this.uppics[key].path
							}
						};
						let w=this.naturalWidth;
						let h=this.naturalHeight;

						let offw=0;
						let offh=0;
						if(that.selectbtn===0){
							let sc=that.slidervalue;
							let scale=parseInt(sc)/100.0;

							offw=parseInt((scale-1)*w/2);
							offh=parseInt((scale-1)*h/2);
							data.input.left=offw;
							data.input.right=offw;
							data.input.up=offh;
							data.input.down=offh;
						}else if(that.selectbtn===1){
							let scale=sizes[that.selectsize].value;

							let scale1=w/h;
							if(scale<scale1){
								//宽度需要拉长
								offh=parseInt((1/scale-1/scale1)*w/2);
							}else{
								//高度需要拉长
								offw=parseInt((scale-scale1)*h/2);
							}

							data.input.left=offw;
							data.input.right=offw;
							data.input.up=offh;
							data.input.down=offh;
						}else if(that.selectbtn===2){
							for(let i=0;i<that.pos.length;i++){
								let item=that.pos[i];
								let value=document.getElementById("pos"+i).value;
								if(value===""){
									value=0;
								}
								data.input[item.name]=value;
							}
						}else{

							//图片位置
							let bound=that.freeSize();

							//缩放尺寸
							let sz=that.getPreviewSize();
							//缩放比例
							let scale=w/sz.width;

							for(let k in bound){
								bound[k]=Math.floor(bound[k]*scale);
								if(bound[k]<0){
									bound[k]=0;
								}
								data.input.left=bound.left;
								data.input.right=bound.right;
								data.input.up=bound.up;
								data.input.down=bound.down;
							}
						}
						return data;
					},
					uploadAll(){
						this.transbegin=1;
						let keys= Object.keys(this.uppics);
						for(let i=0;i<keys.length;i++){
							let key=keys[i];
							this.uploadData(key);
						}
						this.$forceUpdate();
					},

					uploadData(key){
						let item=this.uppics[key];
						let upload=item["upload"];
						item.senddata=this.getSendData(key);
						this.uppics[key]=item;

						let token=localStorage.getItem("tk");
						if(token!=null&&token!==""){
							upload.obj.upload(upload.index, upload.file);
							this.uppics[key]["upload"]=null;
						}else if(!this.showlogin){
							this.showlogin=true;
							loginDialog(this);
						}
					},
					selectSize(index){
						this.selectsize=index;
						let sz=this.getPreviewSize();
						let scale=sizes[this.selectsize].value;
						let scale1=sz.width/sz.height;
						let size={};
						if(scale<scale1){
							//宽度需要拉长
							offh=parseInt((1/scale)*sz.width);
							size={width:sz.width,height:offh};
						}else{
							//高度需要拉长
							offw=parseInt(scale*sz.height);
							size={width:offw,height:sz.height};
						}
						
						this.setStretchBoxSize(size);
					},
					inputEvent(init){
						let that=this;
						let bound={};
						for(let i=0;i<that.pos.length;i++){
							let id="pos"+i;
							let name=that.pos[i].name;
							let input=document.getElementById(id);
							let value=input.value;
							if(value===""){
								value=that.rect[name];
								if(value===""){
									value=0;
								}
								if(value!==0&&init){
									input.value=value;
								}

							}
							bound[name]=parseInt(value);
						}
						that.rect=JSON.parse(JSON.stringify(bound));

						//缩放尺寸
						let sz=that.getPreviewSize();
						//实际尺寸
						let key=Object.keys(that.uppics)[0];
						if(key){
							let src=that.uppics[key].preview;
							if(this.naturalWidth===0){
								getImgSize(src,function(res){
									let w=res.width;
									//缩放比例
									let scale=sz.width/w;

									for(let k in bound){
										bound[k]=bound[k]*scale;
									}
									that.setStretchBoxSize(sz,bound);
								});
							}else{
								//缩放比例
								let scale=sz.width/this.naturalWidth;

								for(let k in bound){
									bound[k]=bound[k]*scale;
								}
								this.setStretchBoxSize(sz,bound);
							}
						}


					},
					getImgLength(){
						let len=getImgLength(this);
						let node=document.getElementById("outpaint-footer");
						let tool=document.getElementById("tool-menu");
						let node1=document.getElementById("right-panel");
						if(this.isphone) {
							let offsetH=window.innerHeight*0.09;
							tool.style.bottom=(node1.offsetHeight)+"px";
							node.style.paddingBottom=(node1.offsetHeight+offsetH)+"px";
						}else{
							node.style.paddingBottom="0px";
							tool.style.bottom="40px";
						}
						return len;
					},
					getImage(){
						return getImage(this);
					},
					parseImgStatus(index){
						return parseImgStatus(index,this);
					},
					down(){
						let key=Object.keys(this.uppics)[0];
						return down(key,this);
					},
					reset(){
						this.uppics={};
						this.transbegin=0;
						this.selectsize=0;
						this.isDragging=false;
						this.rect={left:0,right:0,up:0,down:0};
						this.slidervalue=100;
						this.scales=[1,1,1,1];
						this.startX=0;
						this.startY=0;
						this.startWidth=0;
						this.startHeight=0;
						this.naturalWidth=0;
						this.naturalHeight=0;
						initSlider("slide",100,200,25,this,this.slidervalue);
						this.resetDraw();

						let that=this;
						setTimeout(function(){
							for(let i=0;i<that.pos.length;i++){
								let node=document.getElementById("pos"+i);
								if(node){
									node.value="";
								}
							}
						},100);

						reset(this);
					},
					getFocusPos(){
						let x=0;
						let y=0;
						let bg=document.getElementById('canvas-bg');
						let image=document.getElementById("preview_img");
						if(image){
							x=(bg.offsetWidth-image.width)/2;
							y=(bg.offsetHeight-image.height)/2;
						}
						
						return {x:x,y:y};
					},
					resetDraw(){
						this.startX=0;
						this.startY=0; 
						this.startWidth=0; 
						this.startHeight=0;
						
						this.setStretchBoxSize(null,null,true);
					},
					freeSize(dir){
						let bg=document.getElementById('canvas-bg');
						let bgWidth=bg.offsetWidth;
						let bgHeight=bg.offsetHeight;
						let image=document.getElementById("preview_img");
						let imgW=image.width;
						let imgH=image.height;
						let p={
							left:bgWidth/2-imgW/2,
							right:bgWidth/2+imgW/2,
							up:bgHeight/2-imgH/2,
							down:bgHeight/2+imgH/2
						}

						let dirX=true,dirY=true;
						if(dir){
							dirX=dir.x;
							dirY=dir.y;
						}

						//拉伸位置
						let l={
							left:dirX?this.startX:(this.startX-this.startWidth),
							right:dirX?(this.startWidth+this.startX):this.startX,
							up:dirY?this.startY:(this.startY-this.startHeight),
							down:dirY?(this.startHeight+this.startY):this.startY
						}
						let bound={
							left:Math.floor(p.left-l.left),
							right:Math.floor(l.right-p.right),
							up:Math.floor(p.up-l.up),
							down:Math.floor(l.down-p.down)
						};
						return bound;

					},
					Animation(x,y){
						if(this.count<5&&!this.isDragging&&this.selectbtn===3){
							let minx=20,miny=20;
							let node=document.getElementById("canvas-bg");
							let width=node.offsetWidth;
							let height=node.offsetHeight;
							let maxx=width-minx,maxy=height-miny;
							let perx=(maxx-minx)/100.0;
							let pery=(maxy-miny)/100.0;
							if(!x&&!y){
								x=minx;
								y=miny;
								this.startX = x;
								this.startY = y;
								[this.startWidth, this.startHeight] = [1, 1];
							}
							x+=perx;
							y+=pery;
							let that=this;
							if(x>=minx&&x<=maxx){
								this.startWidth = x - this.startX;
								this.startHeight = y - this.startY;

								let bound=this.freeSize();

								this.setStretchBoxSize(this.getPreviewSize(),bound,true);

								setTimeout(function(){
									that.Animation(x,y);
								},5);
							}else{
								setTimeout(function(){
									that.count++;
									that.Animation();
								},500);
							}
						}else{
							if(!this.isDragging){
								this.resetDraw();
							}
						}

					},
					getImgRect(node){
						let parentRect=node.parentElement.getBoundingClientRect();
						let rect=node.getBoundingClientRect();
						const width = node.offsetWidth;
						const height = node.offsetHeight;
						// 左上角
						const topLeft = {
							x: rect.left - parentRect.left,
							y: rect.top - parentRect.top
						};

						// 右上角
						const topRight = {
							x: rect.left + width - parentRect.left,
							y: rect.top - parentRect.top
						};

						// 左下角
						const bottomLeft = {
							x: rect.left - parentRect.left,
							y: rect.top + height - parentRect.top
						};

						// 右下角
						const bottomRight = {
							x: rect.left + width - parentRect.left,
							y: rect.top + height - parentRect.top
						};
						return { topLeft, topRight, bottomLeft, bottomRight };
					},
					touchDown(){
						if(this.selectbtn===3){
							this.resetDraw();
							let e=event;
							if(e){
								e.preventDefault();
							}
							this.isDragging = true;
							if(!this.isphone){
								this.startX = e.offsetX;
								this.startY = e.offsetY;
							}else{
								if(event.touches){
									const touch = event.touches[0];
									let rect=event.currentTarget.getBoundingClientRect();
									this.startX = touch.clientX - rect.left;
									this.startY = touch.clientY - rect.top;
								}
							}
							
							[this.startWidth, this.startHeight] = [1, 1];
						}
					},

					touchMove(){
						if(this.selectbtn===3){
							let e=event;
							if (this.isDragging) {
								let dir={x:true,y:true};
								if(e){
									e.preventDefault();
									let x = 0;
									let y = 0;
									if(!this.isphone){
										x = e.offsetX;
										y = e.offsetY;
									}else if(event.touches){
										const touch = event.touches[0];
										let rect=event.currentTarget.getBoundingClientRect();

										x = touch.clientX - rect.left;
										y = touch.clientY - rect.top;
									}
									this.startWidth = Math.floor(Math.abs(x - this.startX));
									this.startHeight = Math.floor(Math.abs(y - this.startY));
									if(x<this.startX){
										dir.x=false;
									}
									if(y<this.startY){
										dir.y=false;
									}

								}
								if(this.startWidth===0){
									this.startWidth=1;
								}
								if(this.startHeight===0){
									this.startHeight=1;
								}
								let bound=this.freeSize(dir);
								let sz=this.getPreviewSize();
								this.setStretchBoxSize(sz,bound,e);
							}
						}
					},
					touchUp(){
						if(this.selectbtn===3&&this.isDragging){
							this.isDragging = false;



							//小于图片部分需要扩展到图片部分
							if(this.startWidth!==1){
								let imgRect=this.getImgRect(document.getElementById('preview_img'));
								let boxRect=this.getImgRect(document.getElementById('stretch-box'));
								if(boxRect.topLeft.x>imgRect.topLeft.x){
									boxRect.topLeft.x=imgRect.topLeft.x;
									boxRect.bottomLeft.x=imgRect.bottomLeft.x;
									this.startX=imgRect.topLeft.x;
									this.resetDraw();
								}
								if(boxRect.topLeft.y>imgRect.topLeft.y){
									boxRect.topLeft.y=imgRect.topLeft.y;
									boxRect.topRight.y=imgRect.topRight.y;
									this.startY=imgRect.topLeft.y;
									this.resetDraw();
								}

								if(boxRect.bottomRight.x<imgRect.bottomRight.x){
									boxRect.bottomRight.x=imgRect.bottomRight.x;
									boxRect.topRight.x=imgRect.topRight.x;
									this.resetDraw();
								}
								if(boxRect.bottomRight.y<imgRect.bottomRight.y){
									boxRect.bottomRight.y=imgRect.bottomRight.y;
									boxRect.bottomLeft.y=imgRect.bottomLeft.y;
									this.resetDraw();
								}
								this.startWidth=boxRect.topRight.x-boxRect.topLeft.x;
								this.startHeight=boxRect.bottomRight.y-boxRect.topRight.y
								let bound={
									left:imgRect.topLeft.x-boxRect.topLeft.x,
									right:boxRect.topRight.x-imgRect.topRight.x,
									up:imgRect.topLeft.y-boxRect.topLeft.y,
									down:boxRect.bottomRight.y-imgRect.bottomRight.y
								};
								console.log(this.startX+":"+this.startY+":"+this.startWidth+":"+this.startHeight);
								console.log(bound);
								this.setStretchBoxSize(this.getPreviewSize(),bound,!!event);
							}
						}
					},
					
				}
			});

			maininit(extended,false,function(index){
				if(!index){
					extended.selectBtn(extended.selectbtn);
				}
			});
			setFooterPadding();
		</script>
		<script type="module" src="js/firebase.js"></script>
	</body>
</html>