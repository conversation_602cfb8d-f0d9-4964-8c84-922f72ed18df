<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="description" content="Explore our AI-powered tools for seamless translation of comics and short videos. Unlock new possibilities in multilingual content creation with our innovative solutions" />
		<link rel="apple-touch-icon" sizes="180x180" href="img/icon/apple-touch-icon96.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="32x32" href="img/icon/32.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="16x16" href="img/icon/16.png?v=1.0.104">
		<link rel="icon" type="image/png" sizes="96x96" href="img/icon/96.png?v=1.0.104">
		<link rel="stylesheet" href="./css/main.css?v=1.0.113">
		<script src="frame/jquery.js"></script>
		<script src="frame/layui/layui.js?v=1.0.113"></script>
		<script src="frame/vue.js"></script>
		<title>Little Grass - AI Tools & Image Enhancement</title>
	</head>
	<body class="bg-black text-white mt-0" data-bs-spy="scroll" data-bs-target="#navScroll">
		<!--header-->
		<header class="fixed-top header bg-black"></header>

		<!--main-->
		<main class="bg-dark w-100 h-s-1" id="enhancement" :class="isphone?'overflow-y-scroll':''" :style="getMainHeight()">
		  <div class="container" :class="isphone?'':'border-radius-10 bg-80-5'">
			  <div class="w-100 col-12 col-xl-10 d-flex justify-content-center" v-if="!isphone">
			    <h2 class="text-mid">Upscale</h2>
			  </div>
			  <div class="w-100 d-flex justify-content-between align-items-center h-100-87px" :class="getClassName()">
				  <div class="d-flex flex-column justify-content-center position-relative" :class="isphone?'margin-bottom-20':'margin-right-20'" id="showimg" >
<!--					   @mousemove="mouseMove()" @mouseleave="mouseOut">-->
					  <img class="object-position-0-50 object-fit-cover" src="img/person00.jpg" :class="!isphone?'h-480-px':'w-100 h-300-px'"/>
					  <img class="object-position-0-50 object-fit-cover filter-blur-5 position-absolute" id="imgmeng" style="width: 50%" src="img/person00.jpg" :class="!isphone?'h-480-px':'h-300-px'"/>
					  <div class="e-point position-absolute" :style="'left: '+offsetX+'px;'"></div>
					  <div class="position-absolute bg-80-5 padding-5 border-radius-10" style="left: 20px;top:20px">Before repair</div>
					  <div class="position-absolute bg-80-5 padding-5 border-radius-10" style="right: 20px;top:20px">After repair</div>
					  <!-- <video loop autoplay muted>
						  <source src="img/aaa.mp4" type="video/mp4">
					  </video> -->
				  </div>
				  <button class="container-i-2 d-flex flex-column align-items-center justify-content-center padding-0" :class="isphone?'margin-top-20':''" id="img_original" :disabled="transbegin!=0">
					  <div class="d-flex flex-column align-items-center justify-content-center" v-if="getImgLength()===0">
						  <i class="bi bi-upload margin-top-20"></i>
						  <div class="font-1-14 c-black-888 line-height-20" style="white-space: pre-line;">
							  Please upload images.
							  image supports up to 5120x10240.
							  image supports up to 5M.
							  Supports formats such as jpg, jpeg, png, etc.
						  </div>
					  </div>
					  <div class="d-flex flex-column align-items-center justify-content-center w-100 position-relative" :class="isphone?'':'h-480-px'"
						v-if="getImgLength()>0">
						  <div class="w-100 h-100 bg-none progress" v-if="!isphone&&transbegin===1"></div>
						  <img class="w-100 object-fit-contain" :src="transbegin!==2?getImage().chooseimg:getImage().preview" onerror="this.src='img/error.jpg'" :class="isphone?'':'h-100'" v-if="(!isphone&&transbegin!==1)||isphone"/>
						  <div class="w-100 h-100 position-absolute bg-80-5 z-index-99 d-flex justify-content-center align-items-center" v-if="transbegin===0&&getImgLength()>0&&getImage().status===-2">
							  <div class="w-20 position-relative wave" id="lg"></div>
						  </div>
					  </div>
					  <div class="d-flex flex-column align-items-center justify-content-center" v-if="transbegin===2&&getImage().status===-1">
					  		<p class="font-28 c-red">Failed!</p>
					  </div>
				  </button>
			  </div>
			  <p class="c-red align-items-center margin-top-10" style="display: none" id="failed-tip" v-if="isphone"></p>
			  <div class="container-i-3 align-items-center justify-content-end flex-nowrap gap-10 padding-20-0 align-items-stretch position-relative" :class="(transbegin===2||transbegin===0)?'d-flex':'d-none'" v-if="!isphone">
				  <p class="w-100 c-red align-items-center margin-top-10 position-absolute" style="display: none" id="failed-tip"></p>
				  <button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px min-width-200"
					@click="createTask(0)" v-if="transbegin===0" :disabled="!checkUploadDown()" >
					Start
				  </button>
				  <button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px reset-btn min-width-200"
					@click="reset()" v-if="transbegin===2&&getImage().status===-1">
				  		Reset
				  </button>
				  <a class="btn-i-2 d-flex align-items-center justify-content-center h-40-px min-width-200" :href="getImage().download"
					 target="_blank" @click="down()" :download="getImage().download" v-if="transbegin===2&&getImage().status===1"><i class="bi bi-download"></i>Download</a>
			  </div>
		  </div>

			<div class="position-fixed bottom--2 bg-black container-i-3 align-items-center justify-content-end flex-nowrap padding-10-0 z-index-9999999" v-if="isphone">
				<p class="w-90 c-red align-items-center margin-top-10 position-absolute margin-left-p5" style="display: none" id="failed-tip"></p>
				<button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px w-90 margin-left-p5" @click="createTask(0)" v-if="transbegin===0" :disabled="!checkUploadDown()" >
					Start
				</button>
				<button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px reset-btn w-90 margin-left-p5" @click="reset()" v-if="transbegin===2&&getImage().status===-1">
					Reset
				</button>
				<button class="btn-i-2 d-flex align-items-center justify-content-center h-40-px w-90 margin-left-p5" disabled v-if="transbegin===1"><i class="bi bi-download"></i>Download</button>
				<a class="btn-i-2 d-flex align-items-center justify-content-center h-40-px w-90 margin-left-p5" :href="getImage().download" target="_blank" @click="down()" :download="getImage().download"
				   v-if="transbegin===2&&getImage().status===1"><i class="bi bi-download"></i>Download</a>
			</div>

			<div class="position-fixed justify-content-center w-100 h-100-vh bg-dark top-0 progress" v-if="isphone&&transbegin===1"></div>
		</main>

		<!-- footer -->
		<footer class="py-vh-2 border-top border-dark footer"></footer>

		
		
		<script src="js/util.js?v=1.0.113"></script>
		<script>
			let searchObj=getHtmlSearchMap();
			Vue.use(httpVueLoader);
			var enhancement = new Vue({
				el: "#enhancement",
				data:{
					uppics:{
						// "1":{preview:"https://ww3.sinaimg.cn/mw690/92321886gy1hqaaucag3ej21jk25nwtx.jpg",chooseimg:'img/0-2.png',status:1,estimate_time:100,estime:0}
					},
					timers: {},
					transbegin:0,
					user:null,
					isphone:isPhone(),
					offsetX:0,
					node:null,
					ism:false,
					tipIndex:0
				},
				mounted(){
					onLoad();
					this.show=true;
					this.node=document.getElementById("imgmeng");
					let node=document.getElementById("showimg");
					if(node){
						this.offsetX=node.offsetWidth/2;
						if(this.isphone){
							// node.addEventListener("touchmove",this.mouseMove);
							// node.addEventListener("touchcancel",this.mouseOut);
						}
					}

				},
				methods:{
					getMainHeight(){
						return getMainHeight();
					},
					getImageKey(){
						let keys=Object.keys(this.uppics);
						return keys[0];
					},
					getClassName(){
						let name="";
						if(this.isphone){
							name+=" flex-wrap";
						}
						return name;
					},
					setUser(user){
						if(user){
							this.user=user;
						}
						this.reStep();
					},
					reStep(){
						let url=window.location.href;
						let data=getPageData(url);
						removePageData(url);
						if(data){
							this.uppics=data.uppics;
							this.transbegin=data.transbegin;
							//document.getElementById("prompt").value=data.prompt;
							let senddata=data.senddata;
							if(searchObj["yes"]){
								this.createTask(data.index,senddata);
							}else{
								this.transbegin=0;
							}
						}
					},
					savePageData(key,senddata){
						let url=window.location.href;
						let data={
							transbegin:this.transbegin,
							uppics:this.uppics,
							//prompt:document.getElementById("prompt").value,
							index:0,
							senddata:senddata
						}
						savePageData(url,data);
					},
					mouseMove(){
						var rect = event.currentTarget.getBoundingClientRect();
						this.offsetX=event.clientX - rect.left;
						
						cancelAnimationFrame(this.node.animFrameID);
						var that=this;
						this.node.animFrameID = requestAnimationFrame(function() {
						    that.node.style.width = that.offsetX+"px"; // 设置新宽度
						});
					},
					mouseOut(){
						this.offsetX=document.getElementById("showimg").offsetWidth/2;
						cancelAnimationFrame(this.node.animFrameID);
						var that=this;
						this.node.animFrameID = requestAnimationFrame(function() {
						    that.node.style.width = that.offsetX+"px"; // 设置新宽度
						});
					},
					down(){
						let key=Object.keys(this.uppics)[0];
						reset(this);
						return down(key,this);
					},
					reset(){
						reset(this);
					},
					checkUploadDown(){
						return checkUploadDown(this);
					},
					getImgLength(){
						return getImgLength(this);
					},
					getImage(){
						return getImage(this);
					},
					createTask(index,senddata) {
						let key = Object.keys(this.uppics)[index];
						let data=senddata;
						if(!data){
							data={
								type:"IMAGE_ENHANCEMENT",
								input:{
									img_original:this.uppics[key].path,
									prompt:""
								}
							}
						}
						//console.log(data);
						//changeTip(1,this);
						loadProgress();
						createTask(data,key,this);
					}
				}
			});
			function setUser(user){
				enhancement.setUser(user);
			}
			maininit(enhancement,null,function (index){
				if(index){
					enhancement.createTask(0);
				}
			});
			setFooterPadding();
		</script>
		<script type="module" src="js/firebase.js"></script>
	</body>
</html>