

const task_menus = [
	{
		title:"Ai Manga",
		id:1,
		href:"./task.html?key=0",
		icon:"bi-textarea-t",
		templet:"<div class='d-flex align-content-center'><i class='bi bi-textarea-t'></i><p class='margin-left-10'>Manga Translator</p></div>"
	},
	{
		title:"Ai Upscale",
		id:2,
		href:"./task.html?key=1",
		icon:"bi-image",
		templet:"<div class='d-flex align-content-center'><i class='bi bi-image'></i><p class='margin-left-10'>Upscale</p></div>"
	},
	{
		title:"Ai Outpainting",
		id:3,
		href:"./task.html?key=2",
		icon:"bi-layout-text-window",
		templet:"<div class='d-flex align-content-center'><i class='bi bi-layout-text-window'></i><p class='margin-left-10'>Outpainting</p></div>"
	},
	// {
	// 	title:"Video",
	// 	id:4,
	// 	href:"./task.html?key=3",
	// 	icon:"bi-camera-video",
	// 	templet:"<div class='d-flex align-content-center'><i class='bi bi-camera-video'></i><p class='margin-left-10'>Video</p></div>"
	// }
]

const infos=[
	{
		title:"Information",
		id:0,
		href:"setting.html?key=0",
		target: '_top',
		icon:"bi-info-circle",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/info1.png`)' onmouseout='mouseMove(this,`/img/menu/info0.png`)'><img src='/img/menu/info0.png' width='20' height='20' alt='info'><p class='font-16 font-weight-600 margin-left-10'>Information</p></div>",
	},
	{
		title:"Activity",
		id:1,
		href:"setting.html?key=1",
		target: '_top',
		icon:"bi-credit-card-2-front",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/active1.png`)' onmouseout='mouseMove(this,`/img/menu/active0.png`)'><img src='/img/menu/active0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>Activity</p></div>",
	},
	{
		title:"Task",
		id:2,
		href:"setting.html?key=2",
		target: '_top',
		icon:"bi-list-task",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/task1.png`)' onmouseout='mouseMove(this,`/img/menu/task0.png`)'><img src='/img/menu/task0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>Task</p></div>",
	},
	{
		title:"Buy Vip",
		id:3,
		href:"setting.html?key=3",
		target: '_top',
		icon:"bi-chevron-down",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/vip1.png`)' onmouseout='mouseMove(this,`/img/menu/vip0.png`)'><img src='/img/menu/vip0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>Buy Vip</p></div>",
	},
	{
		title:"Buy Coin",
		id:4,
		href:"setting.html?key=4",
		target: '_top',
		icon:"bi-coin",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/coin1.png`)' onmouseout='mouseMove(this,`/img/menu/coin0.png`)'><img src='/img/menu/coin0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>Buy Coin</p></div>",
	},
	{
		title:"API",
		id:4,
		href:"setting.html?key=5",
		target: '_top',
		icon:"bi-code",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/api1.png`)' onmouseout='mouseMove(this,`/img/menu/api0.png`)'><img src='/img/menu/api0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>API</p></div>",
	},
	{
		title:"Sign out",
		id:5,
		href:"logout.html?a=1",
		target: '_top',
		icon:"bi-box-arrow-right",
		templet:"<div class='web-w-200px d-flex justify-content-start align-items-center h-40-px padding-0-10' onmousemove='mouseMove(this,`/img/menu/out1.png`)' onmouseout='mouseMove(this,`/img/menu/out0.png`)'><img src='/img/menu/out0.png' width='20' height='20' alt='active'><p class='font-16 font-weight-600 margin-left-10'>Sign out</p></div>",
	},
]


const sizes=[
	{title:"1:1",value:1,src:"img/1^1.png"},
	{title:"16:9",value:16.0/9,src:"img/16^9.png"},
	{title:"9:16",value:9/16.0,src:"img/9^16.png"},
	{title:"4:3",value:4.0/3,src:"img/4^3.png"},
	{title:"3:4",value:3.0/4,src:"img/3^4.png"}
];

const notLoading= {
	"/home/<USER>/subscription_link":1,
	"/home/<USER>/info":1,
	"/home/<USER>/query":1,
	"/home/<USER>/create":1,
	"/home/<USER>/send":1,
	"/home/<USER>/models":1
}

let currentVue=null;
let layerId=0;
let navView=null;
let showDia=false;

let http="https://api.littlegrass.cc";


function showToast(str,icon){
	layui.use('layer', function(){
		var layer=layui.layer;
		layer.msg(str);
	})
}

function parseDataToUrl(data){
	if(data){
		var url="?";
		for(let k in data){
			if(url!=="?"){
				url+="&";
			}
			let v=data[k];
			let str=k+"="+v;
			//console.log(str);
			url+=str;
		}
		return url;
	}
	return "";
}

function getDevice(){
	let device=localStorage.getItem("device");
	if(!device||device===""){
		device=md5(navigator.userAgent+navigator.platform+navigator.language);
		localStorage.setItem("device",device);
	}
	return device;
}

function parseResult(httpRequest,func){
	if(httpRequest.status === 200){
		let json = httpRequest.responseText;
		//console.log("get:"+json);
		json=JSON.parse(json);
		//console.log(json);
		if(json.code===-1&&json.msg==="unlogin"){
			localStorage.removeItem("tk");
			removeUser();
		}
		if(func){
			func(json);
		}
	}else{
		setFailedTip("Network error!");
		//showToast("response status:"+status);
		console.log("response status:"+status);
		if(func){
			func(null);
		}
	}
}

function post(url,data,func){
	if(!notLoading[url]){
		showLoading();
	}
	//let device = getDevice();
	let token=localStorage.getItem("tk");
	let httpRequest = new XMLHttpRequest();
	httpRequest.open('POST', http+url, true);
	httpRequest.setRequestHeader("Content-type","application/json");
	//httpRequest.setRequestHeader("Device",device);
	httpRequest.setRequestHeader("Authorization","Bearer "+token);

	httpRequest.onreadystatechange = function () {
		if (httpRequest.readyState === 4) {
			parseResult(httpRequest,func);
			if(!notLoading[url]){
				closeLoading();
			}
		}
	};

	httpRequest.send(JSON.stringify(data));
}

function get(url,data,method,func){
	// if(!notLoading[url]){
	// 	showLoading();
	// }
	//let device = getDevice();
	let token=localStorage.getItem("tk");
	if(!method){
		method="GET";
	}
	let u=parseDataToUrl(data);
	console.log(http+url+u);
	let httpRequest = new XMLHttpRequest();
	httpRequest.open(method, http+url+u, true);
	httpRequest.setRequestHeader("Content-type","application/json");
	//httpRequest.setRequestHeader("Device",device);
	httpRequest.setRequestHeader("Authorization","Bearer "+token);

	httpRequest.onreadystatechange = function () {
		if (httpRequest.readyState === 4) {
			parseResult(httpRequest,func);

			// if(!notLoading[url]){
			// 	closeLoading();
			// }
		}

	};
	httpRequest.send();
}

function getUserInStorage(){
	var user=localStorage.getItem("shdng");
	if(user){
		user=JSON.parse(user);
	}
	return user;
}

function setUserInStorage(user){
	if(user){
		localStorage.setItem("user",JSON.stringify(user));
	}
}

function removeUser(){
	localStorage.removeItem("user");
}

function setHistory(){
	let href=location.href;
	console.log("current href:"+href);
	if(href.indexOf("payvip")===-1&&href.indexOf("paycoin")===-1){
		localStorage.setItem("his",href);
	}
}

function getHistory(){
	let url=localStorage.getItem("his");
	if(!url){
		url=window.location.origin;
	}
	return url;
}

function requestUser(func){
	let url="/home/<USER>/info";
	get(url,null,"GET",function(res){
		if(res&&res.code===0){
			// var user=getUserInStorage();
			// user.vip=res.user.vip;
			// user.coin=res.user.coin;
			let user=res.user;
			setUserInStorage(user);
			if(func){
				func(user);
			}
		}else{
			localStorage.removeItem("tk");
			removeUser();
			if(func){
				func(null);
			}
		}
	});
}

function isPhone() {
	let info = navigator.userAgent;
	let isphone = /mobile/i.test(info);
	if(!isphone){
		let sc = window.innerWidth/window.innerHeight;
		if(sc<=1){
			isphone=true;
		}
	}
	return isphone;
}

function initDropdown(id,list){
	layui.use('dropdown', function(){
		let dropdown = layui.dropdown
		dropdown.render({
			elem: '#'+id, //可绑定在任意元素中，此处以上述按钮为例
			data: list,
			id:id,
			trigger:"hover",
			align:"center",
			click: function (obj){
				// console.log(obj);
				location.href=obj.href;
			},
			ready: function(elemPanel, elem){
				// console.log(elemPanel);
				// console.log(elem);
			},
			error: function(index){
				//请求异常回调
				console.log("error: "+index);
			}
		},);
	});
}

function initSlider(id,min,max,step,vm,value){
	layui.use('slider', function(){
		var slider = layui.slider;
		//渲染
		slider.render({
			elem: '#'+id  ,//绑定元素
			min:min,
			max:max,
			range:false,
			value:value?value:min,
			step:step,
			showstep:true,
			tips:true,
			theme:"#5865f2",
			change: function(value){
				if(vm){
					vm.sliderChange(value);
				}
			}
		});
	});
}

function getImgSize(src,func){
	var img = new Image();
	img.src = src;
	img.onload = function () {
		if(func){
			func({width:img.width,height:img.height});
		}
	}
}

function initUpload(key,vm,multipleid,func){
	let token=localStorage.getItem("tk");
	//let perPrice=[5,1];
	let maxWidth=5120;
	let maxHeight=10240;
	let user=getUserInStorage();
	layui.use('upload', function(){
		let upload = layui.upload;
		let uploadInst = upload.render({
			elem: '#'+key,
			url: http+'/home/<USER>/upload',
			accept:"images",
			acceptMime:"image/jpg,image/png,image/bmp,image/jpeg",
			field:"file",
			drag:true,
			multiple:multipleid,
			auto: false,
			number:multipleid?((user&&user.vip>0)?(20-vm.uppics.length):(1-vm.uppics.length)):1,
			size:5120,
			headers:{
				//Device:getDevice(),
				Authorization:"Bearer "+token},
			choose: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
				obj.preview(function(index, file, result){
					getImgSize(result,function(res){
						vm.naturalWidth=res.width;
						vm.naturalHeight=res.height;
						if(res.width <=maxWidth && res.height <=maxHeight){
							vm.uppics[index]={chooseimg:result,preview:result,status:0,name:file.name,visited:false,
								upload:{
									obj:obj,
									index:index,
									file: file
								}
							};
							vm.size+=file.size;
							setTimeout(function (){
								loadWave();
							},50);
							hideFailedTip();

							if(func){
								func(index);
							}

						}else{
							//showToast("The size of the small image you uploaded must be less than "+maxWidth+"x"+maxHeight);
							setFailedTip("The size of the small image you uploaded must be less than "+maxWidth+"x"+maxHeight);
							return false;
						}
						vm.$forceUpdate();
					});
					return true;
				});
			},
			progress: function(n, elem, res, index){
				setUploadPrograss(n/5,vm,index);
			},
			done: function(res,index,upload){
				let item=vm.uppics[index];
				if(res.code===0){
					hideFailedTip();
					if(item){
						item.path=res["file_url"];
						item.preview=res["file_url"];
						item.senddata["input"]["img_original"]=item.path;
						vm.uppics[index]=item;
						vm.$forceUpdate();
						createTask(item["senddata"],index,vm,function(){
							vm.$forceUpdate();
						});
					}
				}else{
					setFailedTip(res.msg);
					//showToast(res.msg);
					if(item){
						delete vm.uppics[index];
						//item.status=-1;
						vm.$forceUpdate();
					}
				}

				if(multipleid){
					initUpload(multipleid,vm,func);
				}
			},
			error: function(index){
				//请求异常回调
				setFailedTip("There was an error uploading the image.");
				//showToast("There was an error uploading the image.");
				let item=vm.uppics[index];
				if(item){
					delete  vm.uppics[index];
					//vm.uppics[index].status=-1;
					vm.$forceUpdate();
				}

			}
		});
	});
}

function getIndex(index){
	var arr=index.split("-");
	return parseInt(arr[1]);
}

function maininit(vm,multipleid,func){
	if(vm){
		initUpload("img_original",vm,multipleid,func);
	}
}

function reset(vm){
	if(vm.uppics){
		vm.uppics={};
	}
	if(vm.timers){
		vm.timers=typeof(vm.timers)=="string"?null:{};
	}
	if(vm.size){
		vm.size=0;
	}
	if(vm.downfailed){
		vm.downfailed=false;
	}
	vm.transbegin=0;
	vm.$forceUpdate();
	if(typeof(removeWave)==="function"){
		removeWave();
	}
	if(typeof(removeProgress)==="function"){
		removeProgress();
	}
	if(typeof(initData)==="function"){
		initData();
	}

	if(typeof(resetInit)=="function"){
		setTimeout(function (){
			resetInit();
		},10);
	}
}

function Download(vm){
	(async ()=>{
		const zip = new JSZip();
		const folder=zip.folder("");
		let is=true;
		for(let k in vm.uppics){
			let item=vm.uppics[k];
			let src=item.preview;
			let name=item.name;
			try{
				showLoading();
				await fetch(src).then(response => response.blob()).then(blob => {
					closeLoading();
					folder.file(name , blob, { Blob: true });
				});
			}catch (e){
				is=false;
				console.log(e);
				closeLoading();
			}

		}

		if(is){
			zip.generateAsync({ type: "blob" }).then((content) => {
				// 下载压缩包
				saveAs(content, 'littergrass.zip');
				reset(vm);
				//showToast("Download successfully");
			});
		}else{
			if(typeof(vm.showRest)==="function"){
				vm.showRest();
			}
			setFailedTip("Batch download failed!");
			//showToast("Download failed!");
		}
	})();
}

async function down(){
	event.stopPropagation();
	return false;
}


function reinitIframe(id,h) {
	var iframe = document.getElementById(id);
	if(iframe){
		height=iframe.contentWindow.document.body.children[0].offsetHeight;
		if(h==null){
			iframe.height = height;
		}else{
			iframe.height = height+ h;
		}
	}
}

function getUser(user,vm,func){
	if(!user){
		requestUser(function(user){
			vm.user=user;
			if(func){
				func(user);
			}
		});
	}else{
		vm.user=user;
		if(func){
			func(user);
		}
	}
}

function getImgLength(vm){
	return Object.keys(vm.uppics).length;
}

function getImage(vm){
	let keys=Object.keys(vm.uppics);
	if(keys.length>0){
		return vm.uppics[keys[0]];
	}
	return "";
}

function checkDone(vm){
	//status=-2 上传中 -1翻译失败  0翻译中 1翻译成功 2等待翻译
	//翻译完成状态 -1和1
	let finished=true;
	//console.log(vm.uppics);
	for(let k in vm.uppics){
		let item=vm.uppics[k];
		let status=item.status;
		if(status!==-1&&status!==1){
			finished=false;
			break;
		}
	}
	//console.log(finished);
	if(finished){
		vm.transbegin=2;
	}
}

function parseSize(size){
	if(size<1000*1000){
		return Math.floor(size*1.0/1000*10)/10+"K";
	}else{
		return Math.floor(size*1.0/1000/1000*10)/10+"M";
	}
}

function parseImgStatus(index,vm){
	let s=["Processing...","Success","Uploaded"];
	let item=vm.uppics[index];
	let status=item.status+"";
	status=parseInt(status);
	if(vm.transbegin===1&&status===2){
		return "Waiting...";
	}
	if(status===-2){
		return "Uploading...";
	}else if(status===-1){
		return "Failed";
	}else{
		return s[status];
	}
}

function createPost(url,data,key,vm,func){
	let perprice=[5,1];
	vm.transbegin=1;
	console.log("post url: "+url);
	post(url,data,function(res){
		//console.log(res);
		if(res){
			if(res.code===0){
				hideFailedTip();
				let coin=vm.user.vip===1?perprice[0]:perprice[1];
				if(vm.percents){
					vm.percents[key]="0%";
				}
				if(!vm.uppics[key]){
					vm.uppics[key]={};
				}
				vm.uppics[key].task_id=res.task_id;
				vm.uppics[key].status=0;

				if(res.estimate_time){
					if(typeof(progress)!=="undefined"){
						progress.estimate_time=res.estimate_time;
						progress.estime=0;
					}else{
						vm.uppics[key].estimate_time=res.estimate_time;
						vm.uppics[key].estime=0;
						estimeTimer(vm,key);
					}
				}

				vm.user.coin=vm.user.coin-coin;
				setUserInStorage(vm.user);
				vm.$forceUpdate();
				if(func){
					func(res.task_id,res);
				}
				return;
			}else if(res.code===-1){
				//失败
				vm.uppics[key].status=-1;
				vm.savePageData(key,data);
				if(res.msg==="unlogin"){
					loginDialog(vm);
					//window.location.href="./login.html";
				}else{
					coinDialog(vm);
					//window.location.href="./paycoin.html";
				}
				if(func){
					func(null,res);
				}
				return;
			}else{
				setFailedTip(res.msg);
				//showToast(res.msg);
			}
		}
		if(func){
			func(null,res);
		}

		checkDone(vm);
		vm.$forceUpdate();

	});

}

function estimeTimer(vm,key){
	setTimeout(function (){
		let item=vm.uppics[key];
		if(item.estime!==undefined){
			item.estime+=0.1;
			let per=item.estime/item.estimate_time*100+100/5;
			if(per>100){
				per=100;
			}
			if(vm.percents){
				vm.percents[key]=per+"%";
			}
			setPrograss(per,vm);
			vm.$forceUpdate();
			if(item.estime<item.estimate_time){
				estimeTimer(vm,key);
			}
		}
	},100);
}

function setPrograss(percent,vm){
	if(typeof(setPercent)==="function"){
		setPercent(percent);
	}
}

function setUploadPrograss(percent,vm,key){
	if(typeof(setPercent)==="function"){
		setPercent(percent);
	}else{
		if(vm.percents){
			vm.percents[key]=percent+"%";
		}
		vm.$forceUpdate();
	}
}

function queryGet(url,tid,key,vm,func){
	get(url,{task_id:tid},"GET",function(res){
		if(res){
			if(res.code===0&&res.task){
				vm.uppics[key]["senddata"]=null;
				hideFailedTip();
				if(parseInt(res.task.status)===1){
					//成功
					vm.transbegin=2;
					vm.uppics[key].status=1;
					if(vm.timers[key]){
						clearInterval(vm.timers[key]);
					}
					if(res.task&&vm.tasks){
						vm.tasks[key]=res.task;
					}
					if(res.task.output){
						//图片增强 图片扩展
						if(res.task.output.img_result){
							vm.uppics[key].preview=res.task.output.img_result;
							vm.uppics[key].download=res.task.output.img_result_download;
						}else if(res.task.output.video){
							vm.uppics[key].preview=res.task.output.video;
							vm.uppics[key].download=res.task.output.video_download;
						}
					}else{
						vm.uppics[key].preview=res.task.img_result;
						vm.uppics[key].download=res.task.output.img_result_download;
					}
					if(func){
						func();
					}
					vm.$forceUpdate();

				}else{
					//正在进行
					if(!vm.timers[key]){
						vm.timers[key]=setInterval(() => {
							queryGet(url,tid,key,vm,func);
						}, 5000);
					}
				}
			}else{
				//失败
				setFailedTip(res.msg);
				// showToast("response:"+res.code+",msg:"+res.msg);
				// if(!vm.timers[key]){
				// 	vm.timers[key]=setInterval(() => {
				// 	  queryGet(url,tid,key,vm,func);
				// 	}, 10000);
				// }
				if(func){
					func();
				}
			}
		}else{
			if(func){
				func();
			}
		}
		checkDone(vm);
		vm.$forceUpdate();
	});
}

function queryTask(tid,key,vm,func){
	queryGet("/home/<USER>/query",tid,key,vm,func);
}

function createTask(data,key,vm,func){
	vm.transbegin=1;
	loadWave();

	createPost("/home/<USER>/create",data,key,vm,function(tid,res){
		if(!tid){
			if(res.code===-1&&res.msg!=="unlogin"){
				vm.uppics[key].status=-1;
				checkDone(vm);
				vm.$forceUpdate();
			}
		}else{
			//vm.uppics[index]["senddata"]=null;
			queryTask(tid,key,vm,func);
		}

	});
}

function checkUploadDown(vm){
	if(Object.keys(vm.uppics).length>0){
		for(let k in vm.uppics){
			let item=vm.uppics[k];
			if(item.status!==-1&&item.status!==1){
				return false;
			}
		}
		return true;
	}else{
		return false;
	}
}

function deleteUp(index,vm){
	event.stopPropagation();
	delete vm.uppics[index];
	vm.$forceUpdate();
	return false;
}

function getMengClass(item,vm){
	if(item.status===1||(vm.transbegin===0&&item.status===2)){
		//上传完 翻译完
		return "img-meng-1";
	}else if(item.status===-1){
		//失败
		return "img-meng-0";
	}else if(item.status===0){
		//上传中
		return "";
	}
	return "";
}


let loadingIndex=-1;
function showLoading(){
	let listNode=document.getElementById("main-right");
	if(loadingIndex===-1){
		if(listNode){
			let left=window.innerWidth*0.04+220+listNode.offsetWidth/2+"px";
			if(layui.layer){
				if(!isPhone()){
					loadingIndex = layui.layer.load(1,{offset:["400px",left]});
				}else{
					loadingIndex = layui.layer.load(1);
				}

			}else{
				layui.use("layer",function (){
					if(!isPhone()){
						loadingIndex = layui.layer.load(1,{offset:["400px",left]});
					}else{
						loadingIndex = layui.layer.load(1);
					}
				})
			}
		}else{
			if(layui.layer){
				loadingIndex = layui.layer.load(1);
			}else{
				layui.use("layer",function (){
					loadingIndex = layui.layer.load(1);
				})
			}
		}
	}
}

function closeLoading(){
	if(layui.layer){
		layui.layer.close(loadingIndex);
		loadingIndex=-1;
	}else{
		layui.use("layer",function (){
			layui.layer.load(loadingIndex);
			loadingIndex=-1;
		})
	}
}

function setModel(data){
	localStorage.setItem("model",JSON.stringify(data));
}

function getModel(){
	let a=localStorage.getItem("model");
	if(a&&a!==""){
		return JSON.parse(a);
	}
	return a;
}

function savePageData(url,data){
	//console.log(data);
	//data["chooseimg"]="";
	//localStorage.setItem(url+"data",JSON.stringify(data));
}

function removePageData(url){
	localStorage.removeItem(url+"data");
}

function getPageData(url){
	// let data=localStorage.getItem(url+"data");
	// if(data&&data!==""){
	// 	data=JSON.parse(data);
	// }else{
	// 	data=null;
	// }
	// return data;
	return null;
}

function ImageLoad(parentNode){
	setTimeout(function (){
		let images=[];
		if(parentNode){
			images=parentNode.getElementsByTagName("img");
		}else{
			images=document.getElementsByTagName("img");
		}

		for(let i=0;i<images.length;i++){
			let image=images[i];
			let src=image.getAttribute("data-src");
			if(src){
				image.src=src;
			}
		}

		let sources=document.getElementsByTagName("source");
		for(let i=0;i<sources.length;i++){
			let source=sources[i];
			let src=source.getAttribute("data-src");
			if(src){
				source.src=src;
				source.parentElement.load();
			}
		}
	},100);
}

function getHtmlSearchMap(){
	let search = location.search;
	if(search!==""){
		return Object.fromEntries(search.slice(1).split("&").map(v => v.split("=")));
	}
	return {};
}

function getHtmlKey(){
	let search = location.search;
	if(search!==""){
		let items= getHtmlSearchMap();
		if(items["key"]===undefined){
			items["key"]="0";
		}
		return parseInt(items["key"]);
	}
	return -1;
}

function getHtmlHash(){
	let hash=location.hash;
	if(hash&&hash!==""){
		localStorage.setItem("isApp",hash);
	}else{
		hash=localStorage.getItem("isApp");
	}
	return hash&&hash!=="";
}

function loadProgress(){
	setTimeout(function (){
		layui.use(["layer"],function (){
			let $ = layui.jquery;
			if($(".progress")){
				$(".progress").load("common/progress.html");
			}
		})
	},10);
}

function removeProgress(){
	layui.use(["layer"],function (){
		let $ = layui.jquery;
		if($(".progress")){
			$(".progress").remove();
		}
	})

}

function loadWave(){
	setTimeout(function (){
		layui.use(["layer"],function (){
			let $ = layui.jquery;
			if($(".wave")){
				$(".wave").load("common/wave.html");
			}
		})
	},10);
}

function removeWave(){
	layui.use(["layer"],function (){
		let $ = layui.jquery;
		if($(".wave")){
			$(".wave").remove();
		}
	})
}

function addAutoCrateTask(){
	let url=getHistory();
	if(url.indexOf("?")===-1){
		return "?yes=1";
	}else{
		return "&yes=1";
	}
}

function setFailedTip(tip){
	let node=document.getElementById("failed-tip");
	if(node){
		node.style.display="flex";
		node.innerHTML="Error: "+tip;
	}
}

function hideFailedTip(){
	let node=document.getElementById("failed-tip");
	if(node){
		if(isPhone()){
			node.style.display="none";
		}else{
			node.innerHTML="";
		}
	}
}

function getMainHeight(main1,is){
	let winH=window.innerHeight;
	let h=winH;
	if(isPhone()){
		if(!main1){
			h=h-(is?0:60);
		}else{
			h=h-50;
		}
		return "min-height: "+h+"px !important;";
	}else{
		return "height: "+h+"px !important;";
	}
}

function setFooterPadding(){
	if(isPhone()){
		let footer=document.getElementsByClassName("footer")[0];
		if(footer){
			//footer.classList.add("padding-bottom-70");
		}
	}
}

function isIphone(){
	//console.log(navigator.userAgent);
	//alert(isIPhone?"true":"false");
	return /iPhone/i.test(navigator.userAgent);
}

function onLoad(){
	$(".header").load("common/nav.html?v=1.0.113");
	$(".footer").load("common/footer.html");
}

function getName(url){
	let index1=url.lastIndexOf("/");
	let index2=url.lastIndexOf(".");
	url=url.substring(index1+1,index2);
	return url;
}

function getImageBase64(image) {
	const canvas = document.createElement('canvas')
	canvas.width = image.width
	canvas.height = image.height
	const ctx = canvas.getContext('2d')
	ctx.drawImage(image, 0, 0, image.width, image.height)
	// 获取图片后缀名
	const extension = image.src
		.substring(image.src.lastIndexOf('.') + 1)
		.toLowerCase()
	// 某些图片 url 可能没有后缀名，默认是 png
	return canvas.toDataURL('image/' + extension, 1)
}

function downloadAll(sourceList,wm){
	let zipName="LG"+new Date().getTime();
	const zip = new JSZip()
	const fileFolder = zip.folder(zipName)
	const fileList = []
	for (let k in sourceList) {
		let url=sourceList[k].preview;//download
		const name =  getName(url);
		const image = new Image()
		image.setAttribute('crossOrigin', 'Anonymous') // 设置 crossOrigin 属性，解决图片跨域报错
		image.src = sourceList[k].preview
		image.onload = () => {
			const url = getImageBase64(image);
			fileList.push({ name: name, img: url.substring(22) }) // 截取 data:image/png;base64, 后的数据
			if (fileList.length === Object.keys(sourceList).length) {
				if (fileList.length) {
					for (let k = 0; k < fileList.length; k++) {
						// 往文件夹中，添加每张图片数据
						fileFolder.file(fileList[k].name + '.png', fileList[k].img, {
							base64: true
						})
					}
					zip.generateAsync({ type: 'blob' }).then(content => {
						saveAs(content, zipName + '.zip');
						wm.reset();
					})
				}
			}
		}
	}
}

function showLogin(end){
	let area=["70%","70%"];
	if(isPhone()){
		area=["100%","100%"];
	}
	layerId=layer.open({
		title: "",
		type: 2,
		content: window.location.origin+'/login.html',
		area:area,
		//skin:"none",
		end: function (){
			end();
		}
	});
}

function loginDialog(vm){
	if(!showDia){
		showDia=true;
		showLogin(function (){
			showDia=false;
			let success=localStorage.getItem("login");
			if(success!=null&&success==="1"){
				localStorage.removeItem("login");
				vm.showlogin=false;

				if(navView!=null){
					navView.checkUser();
				}

				vm.uploadAll();
			}else{
				localStorage.removeItem("login");
				vm.showlogin=false;

				if(navView!=null){
					navView.navInit();
					navView.$forceUpdate();
				}

			}
		});
	}
}

function showCoin(end){
	let area=["70%","70%"];
	if(isPhone()){
		area=["100%","100%"];
	}
	layerId=layer.open({
		title: "Buy Coin",
		type: 2,
		content: window.location.origin+'/pay/paycoin.html',
		area:area,
		//skin:"none",

		end: function (){
			end();
		}
	});
}

function coinDialog(vm){
	if(!showDia){
		showDia=true;
		showCoin(function (){
			showDia=false;
			let success=localStorage.getItem("paycoin");
			if(success!=null&&success==="1"){
				localStorage.removeItem("paycoin");
				vm.showlogin=false;

				reCreateTask(vm);

				vm.uploadAll();
			}else{
				localStorage.removeItem("paycoin");
				vm.showlogin=false;

				//重置begin
			}
		});
	}
}

//coin
function reCreateTask(vm){
	for(let key in vm.uppics){
		let item=vm.uppics[key];
		if(item["senddata"]!=null){
			createTask(item["senddata"],key,vm,function (){
				vm.$forceUpdate();
			});
		}
	}
}

function getDate(){
	let currentDate = new Date();
	let year = currentDate.getFullYear();
	let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
	let day = currentDate.getDate().toString().padStart(2, '0');
	let hours = currentDate.getHours().toString().padStart(2, '0');
	let minutes = currentDate.getMinutes().toString().padStart(2, '0');
	let seconds = currentDate.getSeconds().toString().padStart(2, '0');

	return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}