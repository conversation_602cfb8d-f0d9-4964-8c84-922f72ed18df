var device=md5(navigator.userAgent+navigator.platform+navigator.language);
var token=localStorage.getItem("tk");
Vue.use(httpVueLoader);
var vm = new Vue({
	el: "#main",
	data:{
		show:false,
		isphone:isPhone(),
		line1:[
			{
				title:"Manga Translator",
				description:"AI Translation accurately and seamlessly convert image text into the target language.",
				join:"Try now",
				url:"./manga.html?key=0",
				img:"img/imgt.png",
			},
			{
				title:"Upscale",
				description:"Enhance image quality by improving clarity, contrast and color saturation while effectively reducing noise and distortion.",
				join:"Try now",
				url:"./enhancement.html?key=0",
				img:"img/imgt.png",
			},
			{
				title:"Outpaint",
				description:"AI-powered upscaling provide clearer, more natural magnification while preserving image detail and color.",
				join:"Try now",
				url:"./outpaint.html?key=0",
				img:"img/imgt.png",
			},
			{
				title:"Chat",
				description:"I have a powerful AI conversation engine that can help you write quickly, come and try it!",
				join:"Try now",
				url:"./chat.html?key=0",
				img:"img/imgt.png",
			},
			// {
			// 	title:"Video",
			// 	description:"AI videos are generated from text. We access the world's leading video generation model to provide users with high-quality artistic creations.",
			// 	join:"Try now",
			// 	url:"./generatevideo.html?key=0",
			// 	img:"img/imgt.png",
			// }

		],
		line2:[
			{classname:"col-12 col-lg-6",src:"img/webp/abstract18.webp",width:2280},
			{classname:"col-12 col-md-6 col-lg-3",src:"img/webp/abstract6.webp",width:1116},
			{classname:"col-12 col-md-6 col-lg-3",src:"img/webp/abstract9.webp",width:1116}
		],
		line3:{
			title:"Our Products",
			description:"We providing innovative content solutions\
	            using AI technology. Our products are designed to help content creators and platform providers\
	            distribute globally more easily, enhance user experience, and increase return on investment.",
		},
		line4:[
			[
				{classname:"card bg-transparent mb-5",src:"img/webp/abstract3.webp",width:582,title:"Comic Player",description:"A player designed specifically for comic content, providing a smooth reading experience"},
				{classname:"card bg-transparent",src:"img/webp/abstract2.webp",width:582,title:"Comic Automatic Translation Tool",description:"Utilizing advanced AI technology to automatically translate comic content, facilitating multilingual distribution"},
			],
			[
				{classname:"card bg-transparent mb-5",src:"img/webp/abstract17.webp",width:582,title:"Video Face Swapping Tool",description:"Using advanced techniques to achieve face swapping and restoration in videos, enhancing video quality and attractiveness"},
				{classname:"card bg-transparent",src:"img/webp/abstract4.webp",width:582,title:"Video Automatic Translation Tool",description:"Achieving speech translation for audio and video content, breaking down language barriers"}
			]
		],
		line5:{
			background:"img/webp/abstract12.webp",
			src:"img/webp/person103.webp",
			width:684,
			description:"Whether you are a creator or a content platform, we are committed to providing you\
	                  with the most advanced tools and technology to help you achieve wider content distribution and greater \
	                  business success.",
			item:{
				title:"The numbers",
				list:[
					{number:">400",description:"Comic translations processed per day",classname:"display-huge fw-bolder"},
					{number:"120,000",description:"Comic Player users",classname:"display-huge fw-bolder border-top border-secondary pt-5 mt-5"},
					{number:"98%",description:"Translation and image overlay accuracy",classname:"display-huge fw-bolder border-top border-secondary pt-5 mt-5"}
				]
			}
		},
		line6:[
			{
				list:[
					{
						classname:"rounded-5 bg-black p-5 shadow",fill:5,src:"img/webp/person13.webp",width:96,alt:"Nathan Anderson",description:"COO, ShortVista Creations",
						lead:'"'+"Efficient and Accurate! Our team relies on the enterprise edition translation tool for seamless multilingual comic localization. It's a game-changer!"+'"',
						aos:"fade",smallclass:"text-secondary"
					},
					{
						classname:"rounded-5 bg-black p-5 shadow mt-5",fill:4,src:"img/webp/person14.webp",width:96,alt:"Agus Setiawan",description:"Player User",
						lead:'"'+"A Comic Lover's Dream!"+'"\n"'+"This player is a revelation! It has completely transformed my comic reading experience. It's intuitive, accurate, and has opened up a whole new world of comics for me. Thank you for making it so easy to dive into the colorful world of comics!"+'"',
						aos:"fade",smallclass:"text-secondary"
					},
				],
			},
			{
				title:"What others have to say",
				description:"Whether for personal entertainment or commercial use, it receives praise from users",
				list:[
					{
						classname:"rounded-5 bg-black p-5 shadow mt-5 gradient",fill:5,src:"img/webp/person16.webp",width:96,alt:"Emily Johnson",description:"Founder, ComicFlix Studios",
						lead:'"'+"Streamlined Workflow! The enterprise version of the translation tool has significantly sped up our comic localization process. It's intuitive and reliable, ensuring top-notch quality"+'"',
						aos:"fade",smallclass:""
					}
				],
			},
		],
		user: null
	},
	mounted(){
		this.show=true;
		this.onLoad();
		this.getUser();
		ImageLoad();
		this.carouselInit();
	},
	methods:{
		onLoad(){
			window.onload=function(){
				$(document).ready(function(){
					$(".header").load("common/nav.html");
					if($(".footer")){
						$(".footer").load("common/footer.html");
					}
				})

				init();
			}
		},
		carouselInit(){
			layui.use('carousel', function(){
			  var carousel = layui.carousel;
			  //建造实例
			  carousel.render({
			    elem: '#banner'
			    ,width: '100%' //设置容器宽度
			    ,arrow: 'always' //始终显示箭头
			    ,anim: 'default' //切换动画方式
				,interval:5000
				,indicator:"inside"
			  });
			});
		},
		clickEvent(url){
			window.open(url,"_top");
		},
		getUser(){
			let that=this;
			requestUser(function(user){
				that.user=user;
			});
		}
	}
});

