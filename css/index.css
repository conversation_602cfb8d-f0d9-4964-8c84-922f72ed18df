.line2{
	padding-top: 4rem;
}

.h-s-1{
	/* min-height: 20rem; */
	padding: 2.5rem 0;
	margin-top: var(--topHeight,116px);
}

.container-i-1{
	width: 60% !important;
	background-color: rgba(80,80,80,0.5) !important;
	min-height: 30rem;
	padding: 1.3rem;
}

.bg-80-5{
	background-color: rgba(80,80,80,0.5) !important;
}

.bg-80-8{
	background-color: rgba(80,80,80,0.8) !important;
}

.min-height-30rem{
	min-height: 30rem;
}

.padding-1-3rem{
	padding: 1.3rem;
}

.border-radius-10{
	border-radius: 10px !important;
}

.layui-carousel{
	height: unset !important;
}

.btn-i-1{
	padding: 8px 30px;
	border-radius: 20px;
	background-color: #278ff0;
	border: #217ed5 1px solid;
	color: #fff;
	text-decoration: none;
}

.bg-1{
	text-decoration: none;
}

.t-img-bg{
	padding-top: 20px;
	padding-left: 24px;
	display: flex;
	align-items: center;
}

.layui-upload-file{
	display: none;
}

.img-up{
	width: 128px;
	height: 128px;
	object-fit: cover;
}

.container-i-3{
	padding-top: 10px;
	width: 100%;
	/* height: 4rem; */
}

.con-left{
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: space-around;
}

.font-14{
	font-size: 14px !important;
}

.font-1-14{
	font-size: 14px;
	/*padding: 20px 0px;*/
	/*text-align: center;*/
}

.font-14-0{
	font-size: 0.8rem;
}

.btn-i-2{
	padding: 8px 20px;
	border-radius: 6px;
	background-color: #278ff0;
	border: #217ed5 1px solid;
	font-size: 16px;
	color: #fff;
}

.btn-i-2:disabled{
	border: #aaa 1px solid;
}

.btn-i-3{
	background: none;
	border: 1px solid #888;
	color: #fff;
	padding: 8px 40px;
}

.container-i-4{
	padding-top: 20px;
}

.img-meng{
	width: calc(100% - 100px);
	height: 30px;
	background-color: rgba(0,0,0,0.5);
	font-size: 14px;
	text-align: center;
	line-height: 30px;
	margin-left: 10px;
	border-radius: 20px;
}
/*******上传完 翻译完*****/
.img-meng-1{
	background-color: #278ff0;
}

/*******失败*****/
.img-meng-0{
	background-color: #888;
}

.float-mid{
	width: 300px;
	height: auto;
	background-color: #fff;
	right: 3vw;
	top: 70px;
	border-radius: 10px;
	z-index: 9999;
}

.container-i-5{
	min-width: 60%;
	max-width: 80%;
	margin-left: auto;
	margin-right: auto;
	justify-content: space-between;
}

.container-i-7{
	min-width: 60%;
	max-width: 80%;
	margin-left: auto;
	margin-right: auto;
	justify-content: space-between;
}

.container-i-6{
	width: 50%;
	margin-left: auto;
	margin-right: auto;
	justify-content: space-between;
}

.btn-payment{
	width: 50%;
	margin-top: 40px;
	border-radius: 40px;
}

.btn-payment:hover{
	background-color: #eaeaea;
	color: #666;
}

.pay-icon{
	height: 25px;
	object-fit: contain;
	margin-right: 10px;
}

.download{
	margin-left: 10px;
	color: #fff;
	font-size: 14px;
}

.download-visited{
	color: #888;
}

.a-left{
	width: 360px !important;
	min-height: 30rem !important;
	margin-right: 20px;
	background-color: rgba(255,255,255,0.2);
	padding: 20px;
	border-radius: 20px;
}

.a-nav{
	width: 100% !important;
	background-color: #313338;
	margin-bottom: 10px;
	border-radius: 10px;
}

.a-btn{
	width: 50%;
	padding: 0px;
	background: none !important;
	color: #fff !important;
	border-radius: 5px !important;
}

.a-btn-this{
	background-color: #5865f2 !important;
	border-radius: 5px !important;
}

.a-des{
	width: 100% !important;
	background-color: #313338 !important;
	border-radius: 10px !important;
	padding: 10px;
	display: flex;
	justify-content: space-between;
	margin-top: 10px;
}

.a-title1{
	font-weight: bolder;
	height: 30px;
	line-height: 30px;
}

.a-des1{
	line-height: 20px;
	color: rgba(255,255,255,0.7);
	font-size: 12px;
}

.a-title{
	width: 100%;
	height: 40px;
	line-height: 40px;
	display: flex;
	align-items: center;
}

.layui-icon-tips{
	height: 40px;
	line-height: 40px;
	margin-right: 8px;
	color: #99c7f2;
	font-weight: bolder;
}

.a-scale{
	width: 100% !important;
	background-color: #313338 !important;
	border-radius: 10px !important;
	padding: 20px 0px;
	display: flex;
	flex-direction: column;
	height: 190px;
	line-height: 30px;
}

.a-size{
	padding: 10px;
	flex-direction: row;
	justify-content: space-between;
	flex-wrap: wrap;
	
}

.a-size-item{
	width: 60px !important;
	height: 80px !important;
	border: 2px solid #eaeaea;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.a-size-item-this{
	border: 2px solid #5865f2;
	background-color: rgba(255,255,255,0.1);
}

.a-size-img{
	width: 80%;
	height: 60%;
	object-fit: contain;
}

.a-slide{
	/*width: 100% !important;*/
	height: 4px !important;
	padding: 0 20px;
	font-size: 20px !important;
}

.a-upload{
	width: 100% !important;
}

/*.layui-slide{*/
/*	height: 30px !important;*/
/*}*/

.a-footer{
	width: 90%;
	padding: 5%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.a-right{
	width: calc(100% - 400px) !important;
	/*width: 80%;*/
	background-color: rgba(255,255,255,0.2);
	padding: 20px;
	border-radius: 20px;
	height: 100%;
}

.a-right::-webkit-scrollbar {
    display: none;
}

.a-line{
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.a-right-left{
	width: 250px;
	height: 96%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.a-right-right{
	width: 50%;
	height: 30%;
	margin-left: 20px;
}

.extend-img{
	width: 100%;
	object-fit: contain;
}

.extend-img1{
	width: 200px !important;
	object-fit: contain;
}

.extend-img2{
	width: 50%;
	object-fit: contain;
}

.stretch-box {
    position: absolute;
    border: 1px dashed #fff;
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
	/* width: 400px;
	height: 400px; */
  }

.a-img-title{
	width: 100%;
	height: 40px;
	line-height: 40px;
	text-align: left;
}

video{
	width: 100% !important;
	/*height: 480px !important;*/
	object-fit: contain;
	overflow-clip-margin: content-box;
	overflow: clip;
}

.padding-0{
	padding: 0px !important;
}

.t2vstyle{
	flex-direction: row !important;
	align-items: center !important;
	justify-content: center;
}

.t2vimgbg{
	position: relative;
	width: calc(100% / 3);
	display: flex;
	justify-content: center;
	align-items: center;
}

.t2v-img{
	border-radius: 6px;
	border: 4px solid rgb(35,35,39);
}

.t2v-img-this{
	border: 4px solid #8d72d2;
}

.imgt{
	width: calc(80% - 8px);
	bottom: 4px;
	background-color: rgba(0,0,0,0.6);
	color: #fff;
	text-align: center;
	font-size: 12px !important;
	height: 16px;
	line-height: 16px;
}

.w-300-px{
	width: 300px !important;
}

.w-100vw-320-px{
	width: calc(100% - 320px) !important;
}

.chat-left{
	height: 520px !important;
	overflow-y: scroll;
}

.chat-left::-webkit-scrollbar{
	width: 0 !important;
}

.w-400-px{
	width: 400px !important;
}

.min-height-40rem{
	min-height: 40rem !important;
}

.e-point{
	width: 4px !important;
	background-color: #fff;
	height: 100%;
}

.w-10-px{
	width: 10px;
}

.w-p100{
	width: 100%;
}

.bg-120{
	background: url(../img/120x120.png) no-repeat;
	background-size: 100% auto;
	border-radius: 20px;
}

.text-decoration-underline{
	text-decoration: underline !important;
}

.margin-left-0{
	margin-left: 0px !important;
}

.w-100{
	width: 100% !important;
}

.user-bg{
	width: 70px;
	height: 40px;
	border: 1px solid rgba(128,128,128,0.3);
}

.w-auto{
	width: auto !important;
}

.min-height-unset{
	min-height:unset !important;
}

.reset-btn{
	border:1px solid #fff !important;
	color: #fff !important;
	background: none !important;
}

.progress{
	z-index: 999;
}

.main,.main1{
	height: calc(100vh - var(--topHeight,116px)) !important;
}

.layui-dropdown ul li{
	list-style: none !important;
}


@media screen and (max-width: 768px) {
	.main{
		height: calc(100vh - var(--topHeight,116px) - 60px) !important;
	}
	.main1{
		height: calc(100vh - var(--topHeight,116px) - 50px) !important;
	}
	.float-mid {
		width:80%;
	}
	.container-i-1{
		width: 100% !important;
	}

	.h-s-1{
		padding: 0px;
	}
	.container-i-6{
		width: 80%;
	}
	.btn-payment{
		width: 100%;
	}
	.container-i-7{
		max-width: 100%;
	}
	.t-img-bg{
		/*width: 100%;*/
		padding-top: 0;
		padding-left: 0;
		margin: 10px 0px;
	}
	.img-meng{
		width: calc(100% - 80px);
		border-radius: 6px;
	}
	video{
		margin-right: 0px !important;
		margin-bottom: 20px !important;
		width: 100% !important;
		height: auto !important;
	}
	.a-right{
		width: 100% !important;
		margin-top: 20px !important;
		height: 100vh;
	}
	.w-300-px{
		width: 100% !important;
	}
	.a-left.h-100{
		height: auto !important;
		min-height: auto !important;
	}
	.chat-left{
		height: auto !important;
	}
	.a-left{
		width: 100% !important;
	}
	.h-auto{
		height: auto !important;
	}
	.margin-top-0{
		margin-top: 0 !important;
	}
	.a-btn-this{
		border-radius: 0;
	}
}