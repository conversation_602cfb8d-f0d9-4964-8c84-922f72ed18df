html,body{ background: #000; }


nav,iframe::-webkit-scrollbar {
    display: none;
}

.header{
	width: 100%;
}

i.bi.bi-arrow-right-circle{
	font-size: 24px;
	height: 24px !important;
}

/* i.bi.bi-arrow-right{
	font-size: 16px;
	height: 16px !important;
} */

.w-70-px{
	width: 70px !important;
}

.w-400-px{
	width: 400px !important;
}

.padding-10-0{
	padding: 10px 0 !important;
}

.padding-20-10{
	padding: 20px 10px !important;
}

.padding-top-20{
	padding-top: 20px !important;
}

.w-50-px{
	width: 50px !important;
}

i.bi.bi-star-fill{
	font-size: 16px;
	height: 16px !important;
}

.padding-5-0{
	padding: 5px 0 !important;
}

i.bi.bi-star-half{
	font-size: 16px;
	height: 16px !important;
}

.bi-translate{
	margin-right: 10px;
}

.bi-x-circle-fill{
	/* position: absolute;
	right: 10px;
	top: -10px; */
	margin-left: 10px;
	color: #f00;
}

.font-18{
	font-size: 18px !important;
}

.padding-bottom-1{
	padding-bottom: 4rem !important;
}

.padding-bottom-2{
	padding-bottom: 2rem !important;
}

.head{
    height: 40px;
    width: 40px;
    object-fit: cover;
	border-radius: 20px;
}

.c-black{
	color: #000 !important;
	top: 3px;
	right: 6px;
	font-size: 20px;
}

.c-black-666-1{
	color: #666;
	top: 3px;
	right: 6px;
	font-size: 20px;
}

.c-black-1{
	color: #000;
	margin-bottom: 0px;
	margin-top: 10px;
}

.c-black-2{
	color: #000;
	margin-bottom: 0px;
	margin-top: 0px;
	margin-left: 10px;
	font-size: 16px;
}

.w-float-90{
	width: 100%;
	height: 100%;
	padding: 5%;
}

.margin-left-20{
	margin-left: 20px;
}

.margin-right-20{
	margin-right: 20px;
}

.head-1{
	width: 80px;
	height: 80px;
	border-radius: 40px;
}
.bg-transparent{
	background: transparent;
}
.h-float-40{
	height: 40px;
}

.bg-1{
	width: 70%;
	background-color: #eaeaea;
	color: #000;
	padding: 5px 0px;
	border-radius: 20px;
	justify-content: center;
	margin-top: 10px;
}

.bi-box-arrow-right{
	font-size: 20px;
}

.w-50--45{
	width: calc(50% - 45px);
	object-fit: cover;
}

.bi-forward{
	font-size: 50px;
	margin: 0px 20px;
}

.margin-top-40{
	margin-top: 40px;
}

.margin-top-20{
	margin-top: 20px;
}

.bg-green{
	background-color: green;
}

.text-mid{
	text-align: center;
	/*padding: 20px 0px;*/
}

.align-content-start{
	align-content: start;
}

.margin-10-px{
	margin: 10px;
}

.w-30{
	width: 28%;
	padding: 20px 1%;
	background-color: #fff;
	border-radius: 20px;
}

.pading-20-1{
	padding: 20px 1%;
	background-color: #fff;
	border-radius: 20px;
}

.w-100-1{
	width: 98%;
	padding: 10px 1%;
	border-radius: 10px;
	border: 1px solid #888;
	margin-bottom: 20px;
}

.justify-space-between{
	justify-content: space-between;
}

.padding-20-0{
	padding: 20px 0px !important;
}

.font-28{
	font-size: 28px;
}

.font-20{
	font-size: 20px;
}

.font-24{
	font-size: 24px;
}

.font-16{
	font-size: 16px;
}

.font-weight{
	font-weight: bold;
}

.font-weight-600{
	font-weight: 600;
}

.c-black-222{
	color: #222;
}

.c-black-666{
	color: #666;
}

.margin-right-5{
	margin-right: 5px;
}

.font-weight-nomarl{
	font-weight: normal;
}

.top-0{
	top:0px;
}

.left-0{
	left:0px;
}

.padding-4-10{
	padding: 4px 10px;
}

.padding-10{
	padding: 10px !important;
}

.w-40-px{
	width: 40px !important;
}

.w-100--50px{
	width: calc(100% - 50px) !important;
}

.border-radius-20{
	border-radius: 20px !important;
}

.line-height-20{
	line-height: 20px !important;
}

.h-90{
	height: 90% !important;
}

.h-16-px{
	height: 16px !important;
}

.c-blue{
	color: blue;
}

.right-50{
	right: 50px !important;
}

/* 定义动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
 
/* 应用动画到元素 */
.rotating-element {
  animation: rotate 1s linear infinite;
}

.margin-left-5{
	margin-left: 5px !important;
}

.opacity-80{
	opacity: 0.8;
}

.margin-right-0{
	margin-right: 0px !important;
}

.border-radius-10{
	border-radius: 10px;
}

.border-left-top-radio-20{
	border-top-left-radius: 20px;
}

.border-right-bottom-radio-20{
	border-bottom-right-radius: 20px;
}

.border-left-top-radio-10{
	border-top-left-radius: 10px;
}

.border-right-bottom-radio-10{
	border-bottom-right-radius: 10px;
}

.bg-red{
	background-color: #f00;
}

.text-line-through{
	text-decoration: line-through;
}

.c-white{
	color: #fff !important;
}

.padding-top-20{
	padding-top: 20px;
}

.c-black-888{
	color: #888;
}

.h-50-px{
	height: 50px;
	line-height: 50px;
}

.btn-1{
	min-width: 120px;
	border:none;
	color: #fff;
	border-radius: 20px;
	font-size: 16px;
	padding: 4px 10px;
	text-align: center;
	text-decoration: none;
}

.btn-1:hover{
	color: #eaeaea;
	background-color: #62ca32;
}

.border-grey{
	border: 4px solid #2e93ff;
	background-color: #f8f8f8;
}

.border-grey1{
	border: 2px solid #fff;
}

.top--10{
	top: -10px;
}

.font-12{
	font-size: 12px;
}

.padding-0-10{
	padding: 0 10px !important;
}

.c-red{
	color: #f00;
}

.h-30-px{
	height: 30px !important;
}

.w-100--140{
	width: calc(100% - 140px) !important;
}

.min-height-unset{
	min-height:unset !important;
}

.h-190-px{
	height: 190px !important;
}

.bg-light:hover{
	color: #888;
}

.margin-bottom-20{
	margin-bottom: 20px;
}

.bi-upload{
	font-size: 50px;
}

.h-500-px{
	height: 500px !important;
}

.justify-content-end{
	justify-content: flex-end;
}

.h-106-px{
	height: 106px;
}

.h-121-px{
	height: 121px;
}

.pointer-events-none{
	pointer-events: none;
}

.pointer-events-auto{
	pointer-events: auto;
}

.pointer-events-all{
	pointer-events: all;
}

.c-ccc{
	color: #ccc !important;
}

.pe-md-l-4{
	padding-left: 1.5rem !important;
}

.text-decoration-none{
	text-decoration: none;
}

.h-52-px{
	display: flex;
	height: 52px;
	line-height: 52px;
}

.bi-caret-down-fill{
	font-size: 8px;
	color: #888;
	margin-left: 6px;
}

.layui-panel{
	/*width: 100%;*/
	background-color: #2C2D31 !important;
	border: none !important;
	border-radius: 4px !important;
	padding-top: 5px;
	/*padding-top: 20px;*/
	/*padding: 0 7vw;*/
	/*top: 80px !important;*/
}

/*.layui-panel ul{*/
/*	!*width: 120px !important;*!*/
/*	!*padding: 0 !important;*!*/
/*	height: 30px;*/
/*}*/

.layui-panel ul li{
	/*height: 36px;*/
	border-radius: 4px;
}

.layui-panel ul li:hover{
	background: none;
}

.layui-panel ul li div:hover{
	color: rgb(205,238,45);
}

.layui-menu{
	background-color: #2C2D31 !important;
	/*display: flex;*/
	/*justify-content: center;*/
	/*padding: 10px 31%;*/
	/*padding-right: 33%;*/
	border-radius: 3px !important;
}

/*.layui-layer{*/
/*	--bs-bg-opacity: 1;*/
/*	background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;*/
/*}*/

/*.layui-layer-title{*/
/*	border-bottom: 0 solid #0C0C0C;*/
/*	color: #fff;*/
/*}*/

.h-584-px{
	height: 584px !important;
}

.h-574-px{
	height: 574px !important;
}

.h-690-px{
	height: 690px !important;
}

.h-100--120{
	height: calc(100vh - 120px) !important;
}

.line-height-40 {
	line-height: 40px !important;
}

.padding-right-60{
	padding-right: 60px !important;
}

.c-white-60{
	color: rgba(255,255,255,0.6);
}

.layui-menu-body-title a{
	width: 100%;
	color: #fff !important;
	border-radius: 0.75rem !important;
	/*text-decoration: none !important;*/
}

/*.layui-menu li{*/
/*	border-radius: 0.75rem !important;*/
/*}*/

/*.layui-menu li:hover{*/
/*	background-color: rgb(80,81,84) !important;*/
/*}*/

.w-80{
	width: 80% !important;
}

.margin-top-116{
	margin-top: var(--topHeight,116px);
}

.font-8{
	font-size: 11px;
}

.justify-content-evenly{
	justify-content: space-evenly;
}

.h-40-px{
	height: 40px !important;
	line-height: 40px !important;
}

.w-30-0{
	width: 30% !important;
}

.meng{
	left:0;
	top: 0;
	z-index: 9999999999999;
}

.bg-393D49{
	background-color: #393D49 !important;
}

.h-100-vh{
	height: 100vh !important;
}

.bg-white-30{
	background-color: rgba(255,255,255,0.3);
}

.margin-right-40{
	margin-right: 40px !important;
}

.position-unset{
	position: unset !important;
	
}

.object-position-0-50{
	object-position: 0% 50% !important;
}

.opacity-0{
	opacity: 0;
}

.padding-5{
	padding: 5px !important;
}

.padding-right-100{
	padding-right: 140px !important;
}

.h-480-px{
	height: 480px !important;
}

.filter-blur-5{
	-webkit-filter: blur(2px);
	filter: blur(2px);
}

.bg-none{
	background: none !important;
}

.right-30{
	right: 30px !important;
}

.border-grey{
	border: 2px solid grey !important;
}

.min-height-160{
	min-height: 160px !important;
}

.max-height-240{
	max-height: 240px !important;
}

.w-100--20{
	width: calc(100% - 40px);
}

.bg-grey{
	background-color: grey !important;
}

.border-a{
	border: 2px solid #aaa !important;
}

.margin-top-50{
	margin-top: 50px !important;
}

.h-240-px{
	height: 240px !important;
}

.h-250-px{
	height: 250px !important;
}

.h-290-px{
	height: 290px !important;
}

.text-left{
	text-align: left !important;
}

.margin-left-10{
	margin-left: 10px !important;
}

.margin-left-0{
	margin-left: 0px !important;
}

.modelbtn:hover{
	background-color: #aaa !important;
}

.top-70-px{
	top:70px !important
}

.z-index-99{
	z-index: 99 !important;
}

.margin-bottom-10{
	margin-bottom: 10px !important;
}

.h-max-290-px{
	max-height: 290px !important;
}

.right-10{
	right: 10px !important;
}

.h-100-8{
	height: calc(100% - 8px);
	
}

.line-height-80-px{
	line-height: 80px;
}

.w-350-px{
	width: 350px !important;
}

.text-clip{
	text-overflow: clip;
}

.padding-top-200{
	padding-top: 200px;
}

.text-hidden{
	overflow: hidden; /* 隐藏超出宽度的内容 */
	white-space: nowrap; /* 禁止文本换行 */
	word-wrap: break-word;
}

.white-sapce-nowrap{
	white-space: nowrap;
}

.bg-80{
	background-color: #888 !important;
}

.margin-0{
	margin: 0px !important;
}

.layui-form-switch{
	margin-top: 0px !important;
}

.layui-form-switch i{
	top:2px !important;
}

.border-none{
	border: none !important;
}

.layui-form-onswitch{
	border-color: #99c7f2 !important;
	background-color: #99c7f2 !important;
}

.right-hover:hover{
	color: #ccc !important;
}

.padding-0{
	padding: 0px !important;
}

.min-h-40-px{
	min-height: 40px !important;
}

.top-50-px{
	top: 50px !important;
}

.padding-top-50{
	padding-top: 50px !important;
}

.bg-white-20{
	background-color: rgba(255,255,255,0.2);
}

.padding-bottom-50{
	padding-bottom: 50px !important;
}

textarea::placeholder {
  color: #ccc; 
}

.border-top-1px input::placeholder {
  color: #ccc; 
}

.margin-right-10{
	margin-right: 10px !important;
}

.border-top-1px input {
  color: #fff; 
}

.layui-icon-face-smile,.layui-icon-util{
	height: 30px;
	line-height: 30px;
	margin-right: 8px;
	color: #99c7f2;
}

.h-20-px{
	height: 20px !important;
}

.w-80-px{
	width: 80px !important;
}

.text-right{
	text-align: right;
}

.padding-0-5{
	padding: 0 5px !important;
}

.c-e5484d{
	color: #e5484d;
}

.padding-bottom-10{
	padding-bottom: 10px !important;
}

.h-100--50{
	height: calc(100% - 50px) !important;
}

.h-49-px{
	height: 49px !important;
}

.border-top-1px{
	border-top: 1px solid #666 !important;
}

.line-height-30{
	line-height: 30px !important;
}

.border--top-right{
	border-radius: 10px !important;
	border-top-right-radius: 0px !important;
}

.margin-bottom-6{
	margin-bottom: 6px !important;
}

.border--top-left{
	border-radius: 10px !important;
	border-top-left-radius: 0px !important;
}

.c-99c7f2{
	color: #99c7f2 !important;
}

.maxcolum-3{
	display: -webkit-box; /* 作为弹性伸缩盒子模型显示 */
	  -webkit-line-clamp: 3; /* 限制在三行 */
	  -webkit-box-orient: vertical; /* 垂直排列盒子 */
	  overflow: hidden; /* 隐藏溢出的内容 */
	  text-overflow: ellipsis; /* 多行时显示省略号 */
	  white-space: normal; /* 使用正常的白空格处理方式，允许换行 */
}

.maxcolum-2{
	display: -webkit-box; /* 作为弹性伸缩盒子模型显示 */
	-webkit-line-clamp: 2; /* 限制在三行 */
	-webkit-box-orient: vertical; /* 垂直排列盒子 */
	overflow: hidden; /* 隐藏溢出的内容 */
	text-overflow: ellipsis; /* 多行时显示省略号 */
	white-space: normal; /* 使用正常的白空格处理方式，允许换行 */
}

.bottom-0{
	bottom: 0px !important;
}

.overflow-y-scroll{
	overflow-y: scroll;
}

.h-640-px{
	height: 640px !important;
}

.align-items-stretch{
	align-items:stretch !important;
}

.border-radius-5{
	border-radius: 5px !important;
}

.w-250-px{
	width: 250px !important;
}

.gap-10{
	gap: 10px !important;
}

.layui-layer-msg{
	background-color: #393D49 !important;
}

.border-1E9FFF{
	border: 5px solid #1E9FFF !important;
}

.w-60{
	width: 60% !important;
}

.h-700-px{
	height: 700px !important;
}

.h-60{
	height: 60% !important;
}

.h-350-px{
	height: 350px !important;
}

.banner-this-down{
	opacity: 0;
	transform: translateY(-40px);
	transition: opacity 1s ease-out, transform 1s ease-out;
	pointer-events: none; /* 防止点击未显示的元素 */
}

.layui-this .banner-this-down{
	opacity: 1;
	transform: translateY(0);
	pointer-events: auto; /* 使元素可点击 */
}

.banner-this-left{
	opacity: 0;
	transform: translateX(200px);
	transition: opacity 1s ease-out, transform 1s ease-out;
	pointer-events: none; /* 防止点击未显示的元素 */
}

.layui-this .banner-this-left{
	opacity: 1;
	transform: translateX(0);
	pointer-events: auto; /* 使元素可点击 */
}

.banner-this-fade{
	opacity: 0;
	transition: opacity 2s ease-out, transform 2s ease-out;
	pointer-events: none; /* 防止点击未显示的元素 */
}

.layui-this .banner-this-fade{
	opacity: 1;
	pointer-events: auto; /* 使元素可点击 */
}

.margin-right-60{
	margin-right: 60px !important;
}

.margin-left-2{
	margin-left: 2px !important;
}

.border-right-444{
	border-right: 2px solid #444 !important;
}

.border-right-aaa{
	border-right: 2px solid #aaa !important;
}

.border-radius-0{
	border-radius: 0 !important;
}

.info-this{
	/*border-right: 2px solid #aaa !important;*/
	color: #fff;
}

.info:hover{
	background: rgba(255,255,255,0.2);
	color: #fff;
}

.padding-left-5{
	padding-left: 5px !important;
}

.border-radius-lr-5{
	border-top-left-radius: 5px !important;
	border-bottom-left-radius: 5px !important;
}

.flex-wrap-unset{
	flex-wrap: unset !important;
}

.w-80{
	width: 80% !important;
}

.border-444{
	border: 1px solid #444;
}

.border-bottom-444{
	border-bottom: 1px solid #444;
}

.border-top-444{
	border-top: 1px solid #444;
}

.w-20{
	width: 20% !important;
}

.overflow-y-scroll::-webkit-scrollbar{
	display: none !important;
}

.margin-left-p25{
	margin-left: 25% !important;
}

.min-h-100--320px{
	min-height: calc(100vh - 320px) !important;
}

.w-40{
	width: 40% !important;
}

.w-15{
	width: 15% !important;
}

.h-80-px{
	height: 80px !important;
}

.c-red-60{
	color: rgba(255,0,0,0.6) !important;
}

.c-green-60{
	color: rgba(0,255,0,0.6) !important;
}

.laypage .layui-laypage a{
	background: none !important;
	color: #888 !important;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em{
	background-color: rgba(128,128,128,0.8) !important;
}

.text-clip{
	overflow: hidden !important;
	white-space: nowrap !important;
	text-overflow: ellipsis !important;
}

.max-w-70{
	max-width: 80% !important;
}

.min-height-45rem{
	min-height: 45rem !important;
}

.min-height-48rem{
	min-height: 48rem !important;
}

.top-10-px{
	top: 10px !important;
}

.w-20-px{
	width: 20px !important;
}

.h-20-px{
	height: 20px !important;
}

.border-radio-30{
	border-radius: 30px !important;
}

.bottom-10rem{
	bottom: 10rem !important;
}

.w-60-px{
	width: 60px !important;
}

.h-60-px{
	height: 60px !important;
}

.w-360-px{
	width: 360px !important;
}

.bg-while-20{
	background-color: rgba(255,255,255,0.2) !important;
}

.padding-20{
	padding: 20px !important;
}

.w-100-380px{
	width: calc(100% - 380px) !important;
}

.align-items-stretch{
	align-items: stretch !important;
}

.h-100vh-120px{
	height: calc(100vh - 120px) !important;
}

.bottom-10-px{
	bottom: 10px !important;
}

.h-100--60px{
	height: calc(100% - 60px) !important;
}

.px-vw-2{
	padding-left: 2vw;
	padding-right: 2vw;
}

.margin-13rem{
	padding-left: 1.3rem;
	padding-right: 1.3rem;
}

.px-vw-1{
	padding-right: 1vw !important;
	padding-left: 1vw !important;
}

.padding-top-30{
	padding-top: 30px !important;
}

.user-select-none{
	user-select: none;
}

.flex-shrink-0{
	flex-shrink:0;
}

.w-14-px{
	width: 14px;
}

.w-90{
	width: 90% !important;
}

.disabled-touch-event{
	pointer-events: none;
}

img {
	-webkit-user-select: none;  /* Safari */
	-moz-user-select: none;     /* Firefox */
	-ms-user-select: none;      /* IE/Edge */
	user-select: none;          /* 标准语法 */
}

.h-30rem{
	height: 30rem !important;
}

button[disabled] {
	cursor: not-allowed;
	background-color: grey !important;

}

.w-120-px{
	width: 120px !important;
}

.w-45{
	width: 45% !important;
}

.transition{
	transition: transform 0s ease-in-out;
}

.transition-2s{
	transition: transform 2s ease-in-out;
}

@keyframes rotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.w-100--400{
	width: calc(100% - 400px) !important;
}

.w-0{
	width: 0px !important;
}

.bg-1e9fff{
	background-color: #1e9fff;
}

.h-100--2{
	height: calc(100% - 2px) !important;
}

.w-25{
	width: 25% !important;
}

.word-wrap{
	word-wrap: break-word;
}

.line-height-25{
	line-height: 25px !important;
}

.w-12-5{
	width: 12.5% !important;
}

.w-10{
	width: 10% !important;
}

.w-12{
	width: 12% !important;
}

.w-23{
	width: 23% !important;
}

.h-75-px{
	height: 75px !important;
}

.min-height-unset{
	min-height: unset !important;
}

.border-aaa{
	border: 1px solid #aaa !important;
}

.margin-bottom-30{
	margin-bottom: 30px !important;
}

.min-height-203-px{
	min-height:203px !important;
}

.min-height-400-px{
	min-height:400px !important;
}

.w-70{
	width: 70% !important;
}

.h-300-px{
	height: 300px !important;
}

.padding-40-0{
	padding: 40px 0px !important;
}

.min-height-43rem{
	min-height: 43rem !important;
}

.min-height-28rem{
	min-height: 28rem !important;
}

.border-radius-6{
	border-radius: 6px !important;
}

.vertical-align-middle{
	display: table-cell;
	vertical-align: middle;
}

.top-100-px{
	top: 100px !important;
}

.object-position-0-100{
	object-position: left bottom !important;
}

.flex-1{
	flex: 1 !important;
}

.w-30-px{
	width: 30px !important;
}

.h-30-px{
	height: 30px !important;
}

.padding-40-0{
	padding: 40px 0 !important;
}

.margin-top-0{
	margin-top: 0 !important;
}

.h-720-px{
	height: 720px !important;
}

.h-20-rem{
	height: 20rem !important;
}

.margin-bottom-0{
	margin-bottom: 0 !important;
}

.bg-greyscale{
	filter: grayscale(100%);
}

.w-400-rpx{
	width: 400rpx !important;
}

.h-0{
	height: 0;
}

.animation-wave-4s{
	animation: animate-wave 4s ease-in-out infinite;
}

.bg-white-50{
	background-color: rgba(120,120,120,0.5);
}

@keyframes animate-wave {
	0%,100%{
		clip-path: polygon(0 45%, 7% 42%, 13% 40%, 20% 41%, 26% 42%, 32% 46%, 37% 51%,44% 53%,51% 55%, 55% 60%,64% 60%,71% 62%, 80% 62%, 84% 59%,90% 57%, 94% 55%, 98% 54%,100% 51%,100% 100%,0% 100%);
	}
	50%{

	}
}

.tip{
	font-size: 24px;
}

.min-height-300-px{
	min-height: 300px !important;
}

.min-width-110{
	min-width: 110px;
}

.margin-top-10{
	margin-top: 10px !important;
}

.padding-left-right-0{
	padding-left: 0 !important;
	padding-right: 0 !important;
}

.padding-bottom-20{
	padding-bottom: 20px !important;
}

.min-width-120{
	min-width: 120px !important;
}

.min-width-180{
	min-width: 180px !important;
}

.justify-content-revert{
	justify-content: revert;
}

.padding-0-20{
	padding: 0 20px !important;
}

.min-height-720-px{
	min-height: 720px !important;
}

.h-100-87px{
	height: calc(100% - 87px) !important;
}

.min-width-200{
	min-width: 200px !important;
}

.h-100-20px{
	height: calc(100% - 20px) !important;
}

.h-10px{
	height: 10px;
}

.w-60--40{
	width: calc(60% - 40px);
}

.h-100-120px{
	height: calc(100% - 120px);
}

.h-100--150px{
	height: calc(100% - 150px);
}

.w-90-px{
	width: 90px;
}

.h-100--10px{
	height: calc(100% - 10px) !important;
}

.padding-bottom-60{
	padding-bottom: 60px !important;
}

.min-height-100--50px{
	min-height: calc(100% - 50px) !important;
}

.min-100--10px{
	min-height: calc(100% - 10px);
}

.margin-left-p5{
	margin-left: 5%;
}

.margin-top-106{
	margin-top: var(--topHeight,106px) !important;
}

.h-100-vh--166px{
	height: calc(100vh - 50px - var(--topHeight,116px)) !important;
}

.z-index-9999999{
	z-index: 9999999 !important;
}

.z-index-99999999{
	z-index: 99999999 !important;
}

.box-shadow-down-10{
	/*水平偏移 垂直偏移 模糊半径 扩散半径 颜色*/
	box-shadow: 0px 10px 10px 0px rgba(255, 255, 255, 0.1);
}

.h-100--40{
	height: calc(100% - 40px) !important;
}

.w-150-px{
	width: 150px !important;
}

.line-height-100{
	line-height: 100% !important;
}

.font-family-Bangers{
	font-family: Bangers, sans-serif;
}

.font-style-italic{
	font-style:  italic;
}

.font-style-oblique{
	font-style:  oblique;
}

.min-h-100-120px{
	min-height: calc(100% - 120px) !important;
}

.min-h-160-px{
	min-height: 160px !important;
}

.max-h-316-px{
	max-height: 316px !important;
}

.right-0{
	right: 0 !important;
}

.w-520-px{
	width: 520px !important;
}

.right--10{
	right: -10px !important;
}

.right--9{
	right: -9px !important;
}

.top--9{
	top: -9px !important;
}

.w-18-px{
	width: 18px !important;
}

.h-18-px{
	height: 18px !important;
}

.border-radius-9px{
	border-radius: 9px !important;
}

.h-600-px{
	height: 600px !important;
}

.border-bottom-aaa{
	border-bottom: 1px solid #aaa;
}

.border-top-aaa{
	border-top: 1px solid #aaa;
}

.w-100-vw{
	width: 100vw !important;
}

.h-100--120px{
	height: calc(100% - 120px) !important;
}

.margin-top-60-px{
	margin-top: 60px !important;
}

.bg-black-5{
	background-color: rgba(0,0,0,0.8);
}

.padding-60-0{
	padding: 60px 0 !important;
}

.bgred{
	background-color: rgb(180, 24, 61) !important;
}

.cred{
	color: rgb(180, 24, 61) !important;
}

.margin-lr-25{
	margin-left: 25%;
	margin-right: 25%;
}

.ul-1::marker{
	unicode-bidi: isolate;
	font-variant-numeric: tabular-nums;
	text-transform: none;
	text-indent: 0px !important;
	text-align: start !important;
	text-align-last: start !important;
}

.font-32{
	font-size: 32px !important;
}

.padding-30-100{
	padding: 30px 100px !important;
}

.font-size-50{
	font-size: 50px !important;
}

.border-cred{
	border:1px solid rgb(180, 24, 61);
}

/*ul li {*/
/*	list-style-type: disc !important;*/
/*}*/

.bg-0{
	background-color: #000 !important;
}

.bottom--2{
	bottom: -2px !important;
}

.padding-bottom-70{
	padding-bottom: 70px;
}

.padding-top-60{
	padding-top: 60px !important;
}

.up {
	display: grid;
	grid-template-columns: repeat(auto-fit, 128px);
	-webkit-box-pack: center;
	justify-content: center;
	gap: 1rem;
	max-height: 400px;
	overflow-y: auto;
	background-color: rgb(204, 204, 204);
	border-radius: 0.5rem;
	filter: brightness(1);
	cursor: default;
	padding: 1rem;
}

.max-h-75px{
	max-height: 75px;
}

.max-h-50px{
	max-height: 50px;
}

.h-95-px{
	height: 95px !important;
}

.main-min-h{
	min-height: calc(100vh - var(--topHeight,116px)) !important;
}

.main-h{
	height: calc(100vh - var(--topHeight,116px) - 100px) !important;
}

.h-200-px{
	height: 200px;
}

.w-144px{
	width: 144px;
}

.h-48px{
	height: 48px;
}

.line-height-48px{
	line-height: 48px;
}

.h-92px{
	height: 92px;
}

.bg-cdee2d{
	background-color: #cdee2d;
}

.border-radius-48px{
	border-radius: 48px;
}

.display-flex-h5{
	display: none;
}

.vw100{
	width: 100vw;
}

.vh100{
	height: 100vh;
}

.padding-100px{
	padding: 100px;
}

.padding-right-0{
	padding-right: 0;
}


.h-80{
	height: 80%;
}

.bg-1b1c1e{
	background-color: #1b1c1e;
}

.bg-005fff{
	background-color: #ffffff0c;
}

.bg-5865f2{
	background-color: #5865f2;
}

.bg-020fff{
	background-color: #ffffff32;
}

.bg-010fff{
	background-color: #ffffff19;
}

.h-85{
	height: 85%;
}

.h-100-92px{
	height: calc(100% - 92px);
}

.border-white-dash{
	border: white dashed 1px;
}

.hide-appearance{
	-webkit-appearance: none; /* 对于Chrome、Safari等浏览器，隐藏原生箭头 */
	-moz-appearance: none;    /* 对于Firefox等浏览器，隐藏原生箭头 */
	appearance: none;
}

.border-top-right-5px{
	border-top-right-radius: 5px;
}

.border-bottom-left-10px{
	border-bottom-left-radius: 10px;
}

.padding-5-10{
	padding: 5px 10px;
}

.load{
	object-fit:fill;
	animation: move 1s linear infinite;
}

@keyframes move{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}

}

.border-50fff{
	border: 1px dashed #ffffff79;
}

.order-1{
	order: 1;
}

.font-weight-700{
	font-weight: 700;
}

.font-weight-500{
	font-weight: 500;
}

.font-weight-800{
	font-weight: 800;
}

.border-bottom-radius-10{
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
}

.line-height-60px{
	line-height: 60px;
}

.border-right-white10{
	border-right: 1px solid rgba(255,255,255,0.1);
}

@media screen and (max-width: 768px) {
	.login {
		/*position: absolute;*/
		/*right: 10px;*/
		/*padding: 0.075rem 0.325rem !important;*/
	}
	.bi-forward{
		font-size: 26px;
		margin: 0px 10px;
	}
	.w-50--45{
		width: calc(50% - 23px);
		object-fit: cover;
	}
	.w-30{
		width: 98%;
		margin-bottom: 20px;
	}
	.menus{
		display: none !important;
	}
	.nav-menu{
		display: none !important;
	}
	.header{
		background-color: #000;
	}
	.fs-4{
		font-size: 1.3rem !important;
	}
	.tip{
		font-size: 16px;
	}

	.display-flex-h5{
		display: flex;
	}

	.padding-100px{
		padding: 5%;
	}
}