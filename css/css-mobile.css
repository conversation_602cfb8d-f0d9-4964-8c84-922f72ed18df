@media screen and (max-width: 768px) {


    @property --_w {
        syntax: "<length>";
        inherits: true;
        initial-value: 100vw;
    }
    :root {
        --w: tan(atan2(var(--_w), 1px));
    }

    .h-139px{
        height: 80px;
    }

    .w-539px{
        width: 90%;
    }

    .f-56{
        font-size: 28px;
    }

    .l-h-68{
        line-height: 34px;
    }

    .f-20{
        font-size: 14px;
    }

    .l-h-32{
        line-height: 20px;
    }

    .padding-w7{
        padding: 5vw;
    }

    .w-601px{
        width: 95%;
    }

    .w-80px{
        width: 100%;
        height: 40px !important;
    }

    .h-652px{
        height: auto;
    }

    .m-t-10{
        margin-top: 10px;
        margin-bottom: 30px;
    }

    .scale-1{
        scale: 0.8;
    }

    .a-i-c{
        align-items: center;
    }

    .j-c-c{
        justify-content: center;
    }

    .f-wrap{
        flex-wrap: wrap;
    }

    .w-525px{
        width: 95%;
    }

    .w-67px{
        width: 40px;
    }

    .w-360px{
        width: 55%;
    }

    .h-330px{
        height: 200px;
    }

    .h-441px{
        height: auto;
        padding-bottom: 40px;
    }

    .r-20px{
        right: 0;
    }

    .t-center{
        text-align: center;
    }

    .l-h-24{
        line-height: 18px;
    }

    .l-h-48{
       line-height: 28px;
    }

    .f-40{
        font-size: 20px;
    }

    .w-730px{
        width: 90%;
    }

    .h-96px{
        height: 56px;
    }

    .h-116px{
        height: 50px;
    }

    .w-1140px{
        width: 95%;
    }

    .h-670px{
        height: auto;
    }

    .h-447px{
        height: auto;
    }

    .h-120px{
        height: 100px;
    }

    .w-400px{
        width: 96%;
    }

    .w-570px{
        width: 95%;
    }

    .w-40pc{
        width: 95%;
        margin-bottom: 40px;
    }

    .f-24{
        font-size: 16px;
    }

    .f-18{
        font-size: 14px;
    }

    .f-30{
        font-size: 18px;
    }

    .web-right-padding{
        padding: 10px;
    }

    .l-h-26{
        line-height: 20px;
    }


    .web-flex{
        display: none;
    }

    .w-p-50{
        width: 100%;
    }

    .w-20px{
        width: 100%;
        height: 20px;
    }

    .phone-flex{
        display: flex;
    }

    .web-w-200px{
        width: 80%;
    }

    .web-h-480px{
        height: auto;
    }

    .web-h-150px{
        height: auto;
    }

    .phone-h-40px{
        height: 40px;
    }

    .phone-position-fixed{
        position: fixed;
        bottom: 0;
        left: 0;
    }

    .btn-upscale{
        background-color: #278ff0;
        border: #217ed5 1px solid;
        font-size: 16px;
        color: #fff;
        border-radius: 10px;
    }

    .phone-bg-black{
        background: #000;
    }

    .phone-border-none{
        border: none;
    }

    .max-w-100{
        max-width: 100%;
    }

    .web-row-gap-10px{
        row-gap: 10px;
    }

    .web-w-p-10{
        width: 33.33%;
        height: 150px;
    }

    .web-padding-20{
        padding: 10px;
    }

    .web-w-50{
        width: 90%;
    }

    .btn-translate-upload{
        border: 2px dashed #dadada;
        border-radius: 10px;
        padding: 20px;
        width: 100%;
        background: none;
        color: #fff;
    }

    .web-w-70{
        width: 100%;
    }


    .web-w-80{
        width: 90%;
    }

    .web-w-90{
        width: 100%;
    }

    .layui-layer-title{
        text-align: center;
        font-size: 18px !important;
        font-weight: bolder;
        padding: 0 !important;
    }

    .web-min-500-px{
        height: auto;
    }

    .web-translate-pic{
        width: 33.33%;
        height: 120px;
    }

    .web-h-360px{
        height: auto;
    }

    .web-h-100--bar{
        height: calc(100vh - 92px - 180px);
    }

    .web-w-22{
        width: 100%;
    }

    .web-w-78{
        width: 100%;
    }

    .web-right-80px{
        right: auto;
    }

    .web-bottom-40px{
        bottom: auto;
    }

    .web-w-200px{
        width: 90%;
    }

    .web-position-relative{
        position: fixed;
        bottom: 0;
        left: 0;
        background-color: black;
        padding: 10px 0;
    }

    .web-w-100{
        width: 100%;
        padding: 0 5%;
    }

    .web-w-100-tool{
        width: 90%;
    }

    .web-line-height-48px{
        line-height: 40px;
    }

    .phone-z-index-99999{
        z-index: 99999;
    }

    .web-h-30px{
        height: 10px;
    }

    .phone-bg-005fff{
        background: none;
    }

    .web-h-100-canvas{
        height: 68%;
    }

    .web-h-30px-p-0{
        height: 0;
    }

    .web-padding-10{
        padding: 0 0 10px 0;
    }

    .web-h-240-px{
        height: auto;
    }

    .phone-padding-40-0{
        padding: 40px 0;
    }

    .phone-h-10px{
        height: 10px;
    }

    .phone-border-a-dash{
        border: #aaa dashed 1px;
    }

    .web-tool-position-absolute{
        position: fixed;
        left: 0;
    }

    .phone-w-100{
        width: 100%;
    }

    .web-tool-w-60-px{
        width: 25%;
    }

    .web-bg-010fff{
        background-color: black;
    }

    .web-slider-padding-10{
        padding: 20px 10px;
    }

    .web-92px{
        width: 92px;
    }

    .web-100--92px{
        width: calc(100% - 92px);
    }

    .web-h-480-px{
        width: 100%;
        height: 180px;
    }

    .web-pw-20{
        padding-left: 20px;
        padding-right: 20px;
    }

    .web-h-960-px{
        height: 180px;
    }

    .web-w-1160px{
        width: 100%;
    }


    .f-50{
        font-size: 32px;
    }


    .web-w-537px{
        width: 100%;
    }

    .web-h-620px{
        height: auto;
    }

    .web-align-items-start{
        align-items: center;
    }

    .web-align-items-end{
        align-items: center;
    }

    .web-w-60{
        width: 100%;
    }

    .phone-h-60px{
        height: 60px;
    }


    .web-text-start{
        text-align: center;
    }


    .web-h-120px{
        height: 60px;
    }


    .web-justify-content-end{
        justify-content: center;
    }

    .phone-h-60px{
        width: 100%;
        height: 60px;
    }


    .web-h-screen{
        min-height: auto;
    }

    .web-h-screen--159px{
        min-height: auto;
    }

    .web-w-340px{
        width: 100%;
    }


    .web-w-100--340px{
        width: 100%;
    }

    .linear-1b-2a{
        background: linear-gradient(to right, #1b1b1b, #2a2c30);
    }

    .line-2{
        padding: 55px 0;
    }

    .web-w-506px{
        width:100%;
    }

    .font-weight-800{
        font-weight: 800;
    }

    .c-9a9e9e{
        color: #9a9e9e;
    }

    .line-height-50px{
        line-height: 50px;
    }

    .w-1000px{
        width: 100%;
    }

    .h-150-px{
        height: 150px;
    }

    .w-900-px{
        width: 900px;
    }

    .line-height-76px{
        line-height: 42px;
    }

    .web-h-700-px{
        height: 600px;
    }

    .h-218-px{
        height: auto;
    }

    footer{
        background: linear-gradient(to right, rgba(211,255,0,0.2), rgba(0,0,0,0));
    }

    .w-1200px{
        width:100%;
    }

    .w-48{
        width: 100%;
    }

    .w-4{
        width: 4%;
    }

    .w-560-px{
        width: 100%;
    }

    .clip-50{
        clip-path: inset(0 50% 0 0);
    }

    .h-440px{
        height: 158px;
        padding: 10px 0;
    }

    .padding-8-21{
        padding: 8px 21px;
    }

    .w-5-px{
        width: 5px;
    }

    .padding-left-169{
        padding-left: 169px;
    }

    .right-169{
        right: 169px;
    }

    .web-h-701-px{
        height: auto;
    }

    .phone-text-center{
        text-align: center;
    }

    .w-901-px{
        width: 90%;
    }

    .phone-flex-wrap{
        flex-wrap: wrap;
    }

    .web-h-80-px{
        height: 20px;
    }

    .web-h-150-px{
        height: 50px;
    }

    .phone-justify-content{
        justify-content: center;
    }

    .w-400-h-600{
        width:100%;
        height: 600px;
    }

    .w-1000-h-440{
        width:100%;
        height: calc(var(--_w) / 1000 * 440);
    }

    .phone-flex-h-80-px{
        height: 80px;
    }

    .upload{
        background: url("img/upload.png") no-repeat;
        background-size: 120px 48px;
    }

    .layui-menu-setting{
        background: none;
    }

    .c-f0f2f5{
        color: #f0f2f5;
    }

    .w-1330-h-585{
        width: 100%;
    }

    .padding-right-10px{
        padding-right: 10px;
    }

    .display-flex-web{
        display: none;
    }
    .web-padding-100px{
        padding: 100px 0 !important;
    }
    .web-justify-content-between{
        justify-content: center;
    }
    .slogan-img{
        width: 100%;
        height: auto;
    }

    .phone-padding-0-20{
        padding: 0 20px;
    }

    .web-line-height-48px{
        padding-top: 10px;
        line-height: 24px;
    }
}