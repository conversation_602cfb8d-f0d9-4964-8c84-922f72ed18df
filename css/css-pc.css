@property --_w {
    syntax: "<length>";
    inherits: true;
    initial-value: 100vw;
}

/* inter-300 - latin */
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: local(''),
    url('fonts/inter-v12-latin-300.woff2') format('woff2'),
        /* Chrome 26+, Opera 23+, Firefox 39+ */
    url('fonts/inter-v12-latin-300.woff') format('woff');
    /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

/* inter-400 - latin */
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local(''),
    url('fonts/inter-v12-latin-regular.woff2') format('woff2'),
        /* Chrome 26+, Opera 23+, Firefox 39+ */
    url('fonts/inter-v12-latin-regular.woff') format('woff');
    /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local(''),
    url('fonts/inter-v12-latin-500.woff2') format('woff2'),
        /* Chrome 26+, Opera 23+, Firefox 39+ */
    url('fonts/inter-v12-latin-500.woff') format('woff');
    /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: local(''),
    url('fonts/inter-v12-latin-700.woff2') format('woff2'),
        /* Chrome 26+, Opera 23+, Firefox 39+ */
    url('fonts/inter-v12-latin-700.woff') format('woff');
    /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}

:root {
    --w: tan(atan2(var(--_w), 1px));
}

.w-539px{
    width: 539px;
}

.h-139px{
    height: 139px;
}

.f-56{
    font-size: 56px;
}

.l-h-68{
    line-height: 68px;
}

.f-20{
    font-size: 20px;
}

.l-h-32{
    line-height: 32px;
}

.padding-w7{
    padding: 7vw;
}

.w-601px{
    width: 601px;
}

.w-80px{
    width: 80px;
}

.h-652px{
    height: 652px;
}

.m-t-10{
    margin-top: 10px;
}

.scale-1{
    scale: 1.0;
}

.a-i-c{

}

.j-c-c{

}

.f-wrap{

}

.w-525px{
    width: 525px;
}

.w-67px{
    width: 67px;
}

.w-360px{
    width: 360px;
}

.h-330px{
    height: 330px;
}

.h-441px{
    height: 411px;
}

.r-20px{
    right: 20px;
}

.t-center{

}

.l-h-24{
    line-height: 24px;
}

.l-h-48{
    line-height: 48px;
}

.f-40{
    font-size: 40px;
}

.w-730px{
    width: 730px;
}

.h-96px{
    height: 96px;
}

.h-116px{
    height: 116px;
}

.w-1140px{
    width: 1140px;
}

.h-670px{
    height: 670px;
}

.h-447px{
    height: 447px;
}

.h-120px{
    height: 120px;
}

.w-400px{
    width: 400px;
}

.w-40pc{
    width: 40%;
}

.f-24{
    font-size: 24px;
}

.f-18{
    font-size: 18px;
}

.f-30{
    font-size: 30px;
}

.web-right-padding{
    padding: 100px 80px 0 80px;
}

.l-h-26{
    line-height: 26px;
}

.web-flex{
    display: flex;
}

.w-p-50{
    width: 50%;
}

.w-20px{
    width: 20px;
}

.phone-flex{
    display: none;
}

.web-w-200px{
    width: 300px;
}

.web-h-480px{
    height: 480px;
}

.web-h-150px{
    height: 150px;
}

.phone-h-40px{
    height: 0;
}

.phone-position-fixed{
    position: relative;
}

.btn-upscale{
    background-color: #278ff0;
    border: #217ed5 1px solid;
    font-size: 16px;
    color: #fff;
    border-radius: 10px;
}

.phone-bg-black{
    background: none;
}

.phone-border-none{

}

.max-w-100{
    max-width: 100%;
}

.web-row-gap-10px{
    row-gap: 10px;
}

.web-w-p-10{
    width: 10%;
    min-width: 100px;
    height: 150px;
}

.web-padding-20{
    padding: 20px;
}

.web-w-50{
    width: 50%;
}

.btn-translate-upload{
    border: 2px dashed #dadada;
    border-radius: 10px;
    padding: 20px;
    width: 100%;
    background: none;
    color: #fff;
}

.web-w-70{
    width: 70%;
}

.web-w-80{
    width: 80%;
}

.web-w-90{
    width: 90%;
}

.layui-layer-title{
    text-align: center;
    font-size: 28px !important;
    font-weight: bolder;
    padding: 0 !important;
}

.web-min-500-px{
    height: 500px;
}

.web-translate-pic{
    width: 120px;
    height: 150px;
}

.web-h-360px{
    height: 360px;
}

.web-h-100--bar{
    height: calc(100vh - 92px);
}

.web-w-22{
    width: 350px;
}

.web-w-78{
    width: calc(100% - 350px);
}

.web-right-80px{
    right: 80px;
}

.web-bottom-40px{
    bottom: 40px;
}

.web-w-200px{
    width: 200px;
}

.web-position-relative{
    position: relative;
}

.web-w-100-tool,.web-w-100{
    width: 100%;
}

.web-line-height-48px{
    line-height: 48px;
}

.web-h-30px{
    height: 30px;
}

.web-h-100-canvas{
    height: 100%;
}

.web-h-30px-p-0{
    height: 30px;
}

.web-padding-10{
    padding: 10px;
}

.web-bg-005fff{
    background: #ffffff0c;
}

.web-h-240-px{
    height: 240px;
}

.phone-padding-40-0{
    padding: 0 0;
}

.phone-h-10px{
    height: 0;
}

.phone-border-a-dash{
    border: none;
}

.web-tool-position-absolute{
    position: absolute;
}

.web-tool-w-60-px{
    width: 60px;
}

.web-bg-010fff{
    background-color: #ffffff19;
}

.web-slider-padding-10{
    padding: 10px;
}

.web-92px{
    width: 92px;
}

.web-100--92px{
    width: calc(100% - 92px);
}

.a:hover{
   color: #01AAED !important;
}

.web-h-480-px{
    height: 480px;
}

.web-pw-20{
    padding-left: 20vw;
    padding-right: 20vw;
}

.web-w-1160px{
    width: 1160px;
}

.f-50{
    font-size: 50px;
}

.web-w-537px{
    width: 537px;
}

.web-h-620px{
    height: 620px;
}

.web-align-items-start{
    align-items: start;
}

.web-align-items-end{
    align-items: end;
}

.web-w-60{
    width: 60%;
}

.phone-h-60px{
    height: 0;
}

.web-text-start{
    text-align: start;
}

.web-h-120px{
    height: 120px;
}

.web-justify-content-end{
    justify-content: end;
}


.phone-h-60px{
    width: 0;
    height: 0;
}

.web-h-screen{
    min-height: 100vh;
}

.web-h-screen--159px{
    min-height: calc(100vh - 159px);
}

.web-w-340px{
    width: 340px;
}

.web-w-100--340px{
    width: calc(100% - 340px);
}

.linear-1b-2a{
    background: linear-gradient(to right, #1b1b1b, #2a2c30);
}

.line-2{
    padding: 55px 0;
}

.web-w-506px{
    width: 506px;
}

.font-weight-800{
    font-weight: 800;
}

.c-9a9e9e{
    color: #ffffff;
}

.line-height-50px{
    line-height: 50px;
}

.w-1000px{
    width: 1000px;
}

.h-150-px{
    height: 150px;
}

.w-900-px{
    width: 900px;
}

.line-height-76px{
    line-height: 76px;
}

.web-h-700-px{
    height: 600px;
}

.h-218-px{
    height: 218px;
}

footer{
    background: linear-gradient(to right, rgba(211,255,0,0.2), rgba(0,0,0,0));
}

.w-1200px{
    width: 1200px;
}

.w-48{
    width: 48%;
}

.w-4{
    width: 4%;
}

.w-560-px{
    width: 560px;
}

.clip-50{
    clip-path: inset(0 50% 0 0);
}

.h-440px{
    height: 440px;
}

.padding-8-21{
    padding: 8px 21px;
}

.w-5-px{
    width: 3px;
}

.padding-left-169{
    /*padding-left: 169px !important;*/
    padding-left: calc(var(--_w) / 1440 * 169);
}

.right-169{
    right: 169px;
}

.web-h-701-px{
    height: 701px;
}

.w-901-px{
    width: 901px;
}

.web-h-80-px{
    height: 80px;
}

.web-h-150-px{
    height: 150px;
}

.w-400-h-600{
    width:400px;
    height: 600px;
}

.w-1000-h-440{
    width:1000px;
    height: 440px;
}

.web-flex-w-80-px{
    width: 80px;
}

.layui-menu-bg{
    background: none !important;
}

.web-padding-left-5vw{
    padding-left: 5vw;
}

.bg-493D49{
    background-color: #888888 !important;
}

.layui-menu-item-group{
    border-radius: 4px !important;
}

.upload{
    background: url("img/upload.png") no-repeat;
    background-size: 120px 48px;
}

/*894 752*/
.slogan-img{
    /*width: calc(var(--_w) / 1680 * 852 );*/
    /*height: calc(var(--_w) / 1680 * 752);*/
    /*right: calc(var(--_w) / 1680 * 169);*/
    object-position: left;
    /*width: calc(var(--_w) / 1440 *852 );*/
    height: calc(100% - 48px);
    /*height: 100vh;*/
}

.phone-flex-wrap{
    flex-wrap: nowrap;
}

body{
    /*opacity: 0;*/
}

.layui-menu-setting{
    background: none !important;
}

.c-f0f2f5{
    color: #f0f2f5;
}

.w-1330-h-585{
    width: 1330px;
    height: 585px;
}

.canvas{
    background-color: rgba(100,100,100,0.3);
}

.padding-right-10px{
    padding-right: 10px;
}

.layui-icon-circle-dot{
    font-size: 12px !important;
}

.c-aaa{
    color: #aaaaaa;
}

.bg-2C2D31{
    background-color: #2C2D31;
}

.layui-nav .layui-nav-more{
    font-size: 14px;
    right: 0 !important;
}

.product-item{
    background: none !important;
    font-size: 16px;
    font-weight: 600;
}

.product-item:hover{
    background: none !important;
    color: rgb(205, 238, 45) !important;
    font-size: 16px;
    font-weight: 600;
}

.display-flex-web{
    display: flex;
}

.web-padding-100px{
    padding: 100px;
}

.web-justify-content-between{
    justify-content: space-between;
}

.web-line-height-48px{
    line-height: 48px;
}

.web-border-bottom-5px{
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.web-border-5px{
    border-radius: 5px;
}

.web-border-radius-20{
    border-radius: 20px;
}

.left-50px{
    left: 50px;
}

.right-50px{
    right: 50px;
}

.bottom-50--60px{
    bottom: calc(50% - 60px);
}

.bottom-50--120px{
    bottom: calc(50% - 120px);
}

.line1{
    height: calc(var(--_w) / 1440 * 792);
    display: flex;
    align-items: center;
    scale: calc(1440 / var(--_w));
}

.padding-92px{
    padding-top: 92px;
}

.left-540{
    left: 540px;
}

.h-36px{
    height: 36px;
}

.font-family-Inter{
    font-family: "Inter Medium";
}

.padding-0-16{
    padding: 0 16px;
}

.line-height-36px{
    line-height: 36px;
}

.font-15{
    font-size: 15px;
}